
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>cmake_host_system_information &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="cmake_language" href="cmake_language.html" />
    <link rel="prev" title="break" href="break.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="cmake_language.html" title="cmake_language"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="break.html" title="break"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_host_system_information</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cmake-host-system-information">
<span id="command:cmake_host_system_information"></span><h1>cmake_host_system_information<a class="headerlink" href="#cmake-host-system-information" title="Permalink to this heading">¶</a></h1>
<p>Query various host system information.</p>
<section id="synopsis">
<h2>Synopsis<a class="headerlink" href="#synopsis" title="Permalink to this heading">¶</a></h2>
<pre class="literal-block"><a class="reference internal" href="#query-host-system-specific-information">Query host system specific information</a>
  cmake_host_system_information(RESULT &lt;variable&gt; QUERY &lt;key&gt; ...)

<a class="reference internal" href="#query-windows-registry">Query Windows registry</a>
  cmake_host_system_information(RESULT &lt;variable&gt; QUERY WINDOWS_REGISTRY &lt;key&gt; ...)</pre>
</section>
<section id="query-host-system-specific-information">
<h2>Query host system specific information<a class="headerlink" href="#query-host-system-specific-information" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="nv">&lt;variable&gt;</span><span class="w"> </span><span class="no">QUERY</span><span class="w"> </span><span class="nv">&lt;key&gt;</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Queries system information of the host system on which cmake runs.
One or more <code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code> can be provided to select the information to be
queried.  The list of queried values is stored in <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code> can be one of the following values:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">NUMBER_OF_LOGICAL_CORES</span></code></dt><dd><p>Number of logical cores</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NUMBER_OF_PHYSICAL_CORES</span></code></dt><dd><p>Number of physical cores</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HOSTNAME</span></code></dt><dd><p>Hostname</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FQDN</span></code></dt><dd><p>Fully qualified domain name</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TOTAL_VIRTUAL_MEMORY</span></code></dt><dd><p>Total virtual memory in MiB <a class="footnote-reference brackets" href="#mebibytes" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">AVAILABLE_VIRTUAL_MEMORY</span></code></dt><dd><p>Available virtual memory in MiB <a class="footnote-reference brackets" href="#mebibytes" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TOTAL_PHYSICAL_MEMORY</span></code></dt><dd><p>Total physical memory in MiB <a class="footnote-reference brackets" href="#mebibytes" id="id3" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">AVAILABLE_PHYSICAL_MEMORY</span></code></dt><dd><p>Available physical memory in MiB <a class="footnote-reference brackets" href="#mebibytes" id="id4" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">IS_64BIT</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor is 64Bit</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_FPU</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor has floating point unit</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_MMX</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports MMX instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_MMX_PLUS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports Ext. MMX instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_SSE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports SSE instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_SSE2</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports SSE2 instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_SSE_FP</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports SSE FP instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_SSE_MMX</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports SSE MMX instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_AMD_3DNOW</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports 3DNow instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_AMD_3DNOW_PLUS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor supports 3DNow+ instructions</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_IA64</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if IA64 processor emulating x86</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HAS_SERIAL_NUMBER</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>One if processor has serial number</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROCESSOR_SERIAL_NUMBER</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Processor serial number</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROCESSOR_NAME</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Human readable processor name</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROCESSOR_DESCRIPTION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Human readable full processor description</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OS_NAME</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>See <span class="target" id="index-0-variable:CMAKE_HOST_SYSTEM_NAME"></span><a class="reference internal" href="../variable/CMAKE_HOST_SYSTEM_NAME.html#variable:CMAKE_HOST_SYSTEM_NAME" title="CMAKE_HOST_SYSTEM_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_HOST_SYSTEM_NAME</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OS_RELEASE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>The OS sub-type e.g. on Windows <code class="docutils literal notranslate"><span class="pre">Professional</span></code></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OS_VERSION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>The OS build ID</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OS_PLATFORM</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>See <span class="target" id="index-0-variable:CMAKE_HOST_SYSTEM_PROCESSOR"></span><a class="reference internal" href="../variable/CMAKE_HOST_SYSTEM_PROCESSOR.html#variable:CMAKE_HOST_SYSTEM_PROCESSOR" title="CMAKE_HOST_SYSTEM_PROCESSOR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_HOST_SYSTEM_PROCESSOR</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DISTRIB_INFO</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>Read <code class="file docutils literal notranslate"><span class="pre">/etc/os-release</span></code> file and define the given <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code>
into a list of read variables</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DISTRIB_&lt;name&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>Get the <code class="docutils literal notranslate"><span class="pre">&lt;name&gt;</span></code> variable (see <a class="reference external" href="https://www.freedesktop.org/software/systemd/man/os-release.html">man 5 os-release</a>) if it exists in the
<code class="file docutils literal notranslate"><span class="pre">/etc/os-release</span></code> file</p>
<p>Example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="no">PRETTY_NAME</span><span class="w"> </span><span class="no">QUERY</span><span class="w"> </span><span class="no">DISTRIB_PRETTY_NAME</span><span class="nf">)</span><span class="w"></span>
<span class="nf">message(</span><span class="no">STATUS</span><span class="w"> </span><span class="s">&quot;${PRETTY_NAME}&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="no">DISTRO</span><span class="w"> </span><span class="no">QUERY</span><span class="w"> </span><span class="no">DISTRIB_INFO</span><span class="nf">)</span><span class="w"></span>

<span class="nf">foreach(</span><span class="no">VAR</span><span class="w"> </span><span class="no">IN</span><span class="w"> </span><span class="no">LISTS</span><span class="w"> </span><span class="no">DISTRO</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">message(</span><span class="no">STATUS</span><span class="w"> </span><span class="s">&quot;${VAR}=`${${VAR}}`&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">endforeach()</span><span class="w"></span>
</pre></div>
</div>
<p>Output:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>-- Ubuntu 20.04.2 LTS
-- DISTRO_BUG_REPORT_URL=`https://bugs.launchpad.net/ubuntu/`
-- DISTRO_HOME_URL=`https://www.ubuntu.com/`
-- DISTRO_ID=`ubuntu`
-- DISTRO_ID_LIKE=`debian`
-- DISTRO_NAME=`Ubuntu`
-- DISTRO_PRETTY_NAME=`Ubuntu 20.04.2 LTS`
-- DISTRO_PRIVACY_POLICY_URL=`https://www.ubuntu.com/legal/terms-and-policies/privacy-policy`
-- DISTRO_SUPPORT_URL=`https://help.ubuntu.com/`
-- DISTRO_UBUNTU_CODENAME=`focal`
-- DISTRO_VERSION=`20.04.2 LTS (Focal Fossa)`
-- DISTRO_VERSION_CODENAME=`focal`
-- DISTRO_VERSION_ID=`20.04`
</pre></div>
</div>
</dd>
</dl>
<p>If <code class="file docutils literal notranslate"><span class="pre">/etc/os-release</span></code> file is not found, the command tries to gather OS
identification via fallback scripts.  The fallback script can use <a class="reference external" href="http://linuxmafia.com/faq/Admin/release-files.html">various
distribution-specific files</a> to collect OS identification data and map it
into <a class="reference external" href="https://www.freedesktop.org/software/systemd/man/os-release.html">man 5 os-release</a> variables.</p>
<section id="fallback-interface-variables">
<h3>Fallback Interface Variables<a class="headerlink" href="#fallback-interface-variables" title="Permalink to this heading">¶</a></h3>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CMAKE_GET_OS_RELEASE_FALLBACK_SCRIPTS">
<span class="sig-name descname"><span class="pre">CMAKE_GET_OS_RELEASE_FALLBACK_SCRIPTS</span></span><a class="headerlink" href="#variable:CMAKE_GET_OS_RELEASE_FALLBACK_SCRIPTS" title="Permalink to this definition">¶</a></dt>
<dd><p>In addition to the scripts shipped with CMake, a user may append full
paths to his script(s) to the this list.  The script filename has the
following format: <code class="docutils literal notranslate"><span class="pre">NNN-&lt;name&gt;.cmake</span></code>, where <code class="docutils literal notranslate"><span class="pre">NNN</span></code> is three digits
used to apply collected scripts in a specific order.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_&lt;varname&gt;">
<span class="sig-name descname"><span class="pre">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_&lt;varname&gt;</span></span><a class="headerlink" href="#variable:CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_<varname>" title="Permalink to this definition">¶</a></dt>
<dd><p>Variables collected by the user provided fallback script
ought to be assigned to CMake variables using this naming
convention.  Example, the <code class="docutils literal notranslate"><span class="pre">ID</span></code> variable from the manual becomes
<code class="docutils literal notranslate"><span class="pre">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_ID</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CMAKE_GET_OS_RELEASE_FALLBACK_RESULT">
<span class="sig-name descname"><span class="pre">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT</span></span><a class="headerlink" href="#variable:CMAKE_GET_OS_RELEASE_FALLBACK_RESULT" title="Permalink to this definition">¶</a></dt>
<dd><p>The fallback script ought to store names of all assigned
<code class="docutils literal notranslate"><span class="pre">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_&lt;varname&gt;</span></code> variables in this list.</p>
</dd></dl>

<p>Example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># Try to detect some old distribution</span>
<span class="c"># See also</span>
<span class="c"># - http://linuxmafia.com/faq/Admin/release-files.html</span>
<span class="c">#</span>
<span class="nf">if(</span><span class="no">NOT</span><span class="w"> </span><span class="no">EXISTS</span><span class="w"> </span><span class="s">&quot;${CMAKE_SYSROOT}/etc/foobar-release&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">return()</span><span class="w"></span>
<span class="nf">endif()</span><span class="w"></span>
<span class="c"># Get the first string only</span>
<span class="nf">file(</span><span class="w"></span>
<span class="w">    </span><span class="no">STRINGS</span><span class="w"> </span><span class="s">&quot;${CMAKE_SYSROOT}/etc/foobar-release&quot;</span><span class="w"> </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_CONTENT</span><span class="w"></span>
<span class="w">    </span><span class="no">LIMIT_COUNT</span><span class="w"> </span><span class="m">1</span><span class="w"></span>
<span class="w">  </span><span class="nf">)</span><span class="w"></span>
<span class="c">#</span>
<span class="c"># Example:</span>
<span class="c">#</span>
<span class="c">#   Foobar distribution release 1.2.3 (server)</span>
<span class="c">#</span>
<span class="nf">if(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_CONTENT</span><span class="w"> </span><span class="no">MATCHES</span><span class="w"> </span><span class="s">&quot;Foobar distribution release ([0-9\.]+) .*&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">set(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_NAME</span><span class="w"> </span><span class="nb">Foobar</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">set(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_PRETTY_NAME</span><span class="w"> </span><span class="s">&quot;${CMAKE_GET_OS_RELEASE_FALLBACK_CONTENT}&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">set(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_ID</span><span class="w"> </span><span class="nb">foobar</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">set(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_VERSION</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_MATCH_1</span><span class="o">}</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">set(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_VERSION_ID</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_MATCH_1</span><span class="o">}</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">list(</span><span class="w"></span>
<span class="w">      </span><span class="no">APPEND</span><span class="w"> </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT</span><span class="w"></span>
<span class="w">      </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_NAME</span><span class="w"></span>
<span class="w">      </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_PRETTY_NAME</span><span class="w"></span>
<span class="w">      </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_ID</span><span class="w"></span>
<span class="w">      </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_VERSION</span><span class="w"></span>
<span class="w">      </span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_VERSION_ID</span><span class="w"></span>
<span class="w">    </span><span class="nf">)</span><span class="w"></span>
<span class="nf">endif()</span><span class="w"></span>
<span class="nf">unset(</span><span class="no">CMAKE_GET_OS_RELEASE_FALLBACK_CONTENT</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p class="rubric">Footnotes</p>
<aside class="footnote brackets" id="mebibytes" role="note">
<span class="label"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></span>
<span class="backrefs">(<a role="doc-backlink" href="#id1">1</a>,<a role="doc-backlink" href="#id2">2</a>,<a role="doc-backlink" href="#id3">3</a>,<a role="doc-backlink" href="#id4">4</a>)</span>
<p>One MiB (mebibyte) is equal to 1024x1024 bytes.</p>
</aside>
</section>
</section>
<section id="query-windows-registry">
<span id="id5"></span><h2>Query Windows registry<a class="headerlink" href="#query-windows-registry" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>cmake_host_system_information(RESULT &lt;variable&gt;
                              QUERY WINDOWS_REGISTRY &lt;key&gt; [VALUE_NAMES|SUBKEYS|VALUE &lt;name&gt;]
                              [VIEW (64|32|64_32|32_64|HOST|TARGET|BOTH)]
                              [SEPARATOR &lt;separator&gt;]
                              [ERROR_VARIABLE &lt;result&gt;])
</pre></div>
</div>
<p>Performs query operations on local computer registry subkey. Returns a list of
subkeys or value names that are located under the specified subkey in the
registry or the data of the specified value name. The result of the queried
entity is stored in <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Querying registry for any other platforms than <code class="docutils literal notranslate"><span class="pre">Windows</span></code>, including
<code class="docutils literal notranslate"><span class="pre">CYGWIN</span></code>, will always returns an empty string and sets an error message in
the variable specified with sub-option <code class="docutils literal notranslate"><span class="pre">ERROR_VARIABLE</span></code>.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code> specify the full path of a subkey on the local computer. The
<code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code> must include a valid root key. Valid root keys for the local computer
are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">HKLM</span></code> or <code class="docutils literal notranslate"><span class="pre">HKEY_LOCAL_MACHINE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HKCU</span></code> or <code class="docutils literal notranslate"><span class="pre">HKEY_CURRENT_USER</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HKCR</span></code> or <code class="docutils literal notranslate"><span class="pre">HKEY_CLASSES_ROOT</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HKU</span></code> or <code class="docutils literal notranslate"><span class="pre">HKEY_USERS</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">HKCC</span></code> or <code class="docutils literal notranslate"><span class="pre">HKEY_CURRENT_CONFIG</span></code></p></li>
</ul>
<p>And, optionally, the path to a subkey under the specified root key. The path
separator can be the slash or the backslash. <code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code> is not case sensitive.
For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="nb">result</span><span class="w"> </span><span class="no">QUERY</span><span class="w"> </span><span class="no">WINDOWS_REGISTRY</span><span class="w"> </span><span class="s">&quot;HKLM&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="nb">result</span><span class="w"> </span><span class="no">QUERY</span><span class="w"> </span><span class="no">WINDOWS_REGISTRY</span><span class="w"> </span><span class="s">&quot;HKLM/SOFTWARE/Kitware&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="nb">result</span><span class="w"> </span><span class="no">QUERY</span><span class="w"> </span><span class="no">WINDOWS_REGISTRY</span><span class="w"> </span><span class="s">&quot;HKCU\\SOFTWARE\\Kitware&quot;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">VALUE_NAMES</span></code></dt><dd><p>Request the list of value names defined under <code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code>. If a default value
is defined, it will be identified with the special name <code class="docutils literal notranslate"><span class="pre">(default)</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SUBKEYS</span></code></dt><dd><p>Request the list of subkeys defined under <code class="docutils literal notranslate"><span class="pre">&lt;key&gt;</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VALUE</span> <span class="pre">&lt;name&gt;</span></code></dt><dd><p>Request the data stored in value named <code class="docutils literal notranslate"><span class="pre">&lt;name&gt;</span></code>. If <code class="docutils literal notranslate"><span class="pre">VALUE</span></code> is not
specified or argument is the special name <code class="docutils literal notranslate"><span class="pre">(default)</span></code>, the content of the
default value, if any, will be returned.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># query default value for HKLM/SOFTWARE/Kitware key</span>
<span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="nb">result</span><span class="w"></span>
<span class="w">                              </span><span class="no">QUERY</span><span class="w"> </span><span class="no">WINDOWS_REGISTRY</span><span class="w"> </span><span class="s">&quot;HKLM/SOFTWARE/Kitware&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="c"># query default value for HKLM/SOFTWARE/Kitware key using special value name</span>
<span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="nb">result</span><span class="w"></span>
<span class="w">                              </span><span class="no">QUERY</span><span class="w"> </span><span class="no">WINDOWS_REGISTRY</span><span class="w"> </span><span class="s">&quot;HKLM/SOFTWARE/Kitware&quot;</span><span class="w"></span>
<span class="w">                              </span><span class="no">VALUE</span><span class="w"> </span><span class="s">&quot;(default)&quot;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Supported types are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">REG_SZ</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REG_EXPAND_SZ</span></code>. The returned data is expanded.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REG_MULTI_SZ</span></code>. The returned is expressed as a CMake list. See also
<code class="docutils literal notranslate"><span class="pre">SEPARATOR</span></code> sub-option.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REG_DWORD</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">REG_QWORD</span></code>.</p></li>
</ul>
<p>For all other types, an empty string is returned.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VIEW</span></code></dt><dd><p>Specify which registry views must be queried. When not specified, <code class="docutils literal notranslate"><span class="pre">BOTH</span></code>
view is used.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">64</span></code></dt><dd><p>Query the 64bit registry. On <code class="docutils literal notranslate"><span class="pre">32bit</span> <span class="pre">Windows</span></code>, returns always an empty
string.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">32</span></code></dt><dd><p>Query the 32bit registry.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">64_32</span></code></dt><dd><p>For <code class="docutils literal notranslate"><span class="pre">VALUE</span></code> sub-option or default value, query the registry using view
<code class="docutils literal notranslate"><span class="pre">64</span></code>, and if the request failed, query the registry using view <code class="docutils literal notranslate"><span class="pre">32</span></code>.
For <code class="docutils literal notranslate"><span class="pre">VALUE_NAMES</span></code> and <code class="docutils literal notranslate"><span class="pre">SUBKEYS</span></code> sub-options, query both views (<code class="docutils literal notranslate"><span class="pre">64</span></code>
and <code class="docutils literal notranslate"><span class="pre">32</span></code>) and merge the results (sorted and duplicates removed).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">32_64</span></code></dt><dd><p>For <code class="docutils literal notranslate"><span class="pre">VALUE</span></code> sub-option or default value, query the registry using view
<code class="docutils literal notranslate"><span class="pre">32</span></code>, and if the request failed, query the registry using view <code class="docutils literal notranslate"><span class="pre">64</span></code>.
For <code class="docutils literal notranslate"><span class="pre">VALUE_NAMES</span></code> and <code class="docutils literal notranslate"><span class="pre">SUBKEYS</span></code> sub-options, query both views (<code class="docutils literal notranslate"><span class="pre">32</span></code>
and <code class="docutils literal notranslate"><span class="pre">64</span></code>) and merge the results (sorted and duplicates removed).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HOST</span></code></dt><dd><p>Query the registry matching the architecture of the host: <code class="docutils literal notranslate"><span class="pre">64</span></code> on <code class="docutils literal notranslate"><span class="pre">64bit</span>
<span class="pre">Windows</span></code> and <code class="docutils literal notranslate"><span class="pre">32</span></code> on <code class="docutils literal notranslate"><span class="pre">32bit</span> <span class="pre">Windows</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TARGET</span></code></dt><dd><p>Query the registry matching the architecture specified by
<span class="target" id="index-0-variable:CMAKE_SIZEOF_VOID_P"></span><a class="reference internal" href="../variable/CMAKE_SIZEOF_VOID_P.html#variable:CMAKE_SIZEOF_VOID_P" title="CMAKE_SIZEOF_VOID_P"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_SIZEOF_VOID_P</span></code></a> variable. If not defined, fallback to
<code class="docutils literal notranslate"><span class="pre">HOST</span></code> view.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">BOTH</span></code></dt><dd><p>Query both views (<code class="docutils literal notranslate"><span class="pre">32</span></code> and <code class="docutils literal notranslate"><span class="pre">64</span></code>). The order depends of the following
rules: If <span class="target" id="index-1-variable:CMAKE_SIZEOF_VOID_P"></span><a class="reference internal" href="../variable/CMAKE_SIZEOF_VOID_P.html#variable:CMAKE_SIZEOF_VOID_P" title="CMAKE_SIZEOF_VOID_P"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_SIZEOF_VOID_P</span></code></a> variable is defined. Use the
following view depending of the content of this variable:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">8</span></code>: <code class="docutils literal notranslate"><span class="pre">64_32</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">4</span></code>: <code class="docutils literal notranslate"><span class="pre">32_64</span></code></p></li>
</ul>
<p>If <span class="target" id="index-2-variable:CMAKE_SIZEOF_VOID_P"></span><a class="reference internal" href="../variable/CMAKE_SIZEOF_VOID_P.html#variable:CMAKE_SIZEOF_VOID_P" title="CMAKE_SIZEOF_VOID_P"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_SIZEOF_VOID_P</span></code></a> variable is not defined, rely on
architecture of the host:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">64bit</span></code>: <code class="docutils literal notranslate"><span class="pre">64_32</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">32bit</span></code>: <code class="docutils literal notranslate"><span class="pre">32</span></code></p></li>
</ul>
</dd>
</dl>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SEPARATOR</span></code></dt><dd><p>Specify the separator character for <code class="docutils literal notranslate"><span class="pre">REG_MULTI_SZ</span></code> type. When not
specified, the character <code class="docutils literal notranslate"><span class="pre">\0</span></code> is used.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ERROR_VARIABLE</span> <span class="pre">&lt;result&gt;</span></code></dt><dd><p>Returns any error raised during query operation. In case of success, the
variable holds an empty string.</p>
</dd>
</dl>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake_host_system_information</a><ul>
<li><a class="reference internal" href="#synopsis">Synopsis</a></li>
<li><a class="reference internal" href="#query-host-system-specific-information">Query host system specific information</a><ul>
<li><a class="reference internal" href="#fallback-interface-variables">Fallback Interface Variables</a></li>
</ul>
</li>
<li><a class="reference internal" href="#query-windows-registry">Query Windows registry</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="break.html"
                          title="previous chapter">break</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmake_language.html"
                          title="next chapter">cmake_language</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/cmake_host_system_information.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="cmake_language.html" title="cmake_language"
             >next</a> |</li>
        <li class="right" >
          <a href="break.html" title="break"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_host_system_information</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>