# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
      script_debug_writes disable
      script_variables 
      // script variables used in the on_update block, mostly defining PRDATA variables      
         extern WsfCommandChain FTR_RULES_CHAIN; // PRDATA - When changing faz within fighter rules, change faz of everyone within this command chain (default, iflite, ielement)
         extern Array<int> PLAYBOOK; // PRDATA - Selects Desired Tactics
         extern Array<int> WINCHESTER; // PRDATA - {Fox3,Fox2,Fox1,AGM, Total Missiles} INT - BINGO Weapons for the flight
      end_script_variables
# File generated by Wizard 2.7.0 on Sep 22, 2020.
#      # This function "scores" bogies and bandits for consideration to be added
#      # to the bogie and bandit lists
      script double BogieBanditConsiderationScoring(WsfLocalTrack threat)
         writeln_d("T = ",TIME_NOW," ",iacid.Name()," ConsiderationScoring");
         if (threat.IsValid() && threat.Target().IsValid()){writeln_d("\t track ",threat.TargetName());}
         if (no_tree){return -1;}
         if ( !threat.Target().IsValid() || threat.Target().Side() == iacid.Side() || 
         iacid.SlantRangeTo(threat) > iacid->COMMIT_RNG*2.0 || 
         threat.Target().CategoryMemberOf("missile") || 
         threat.Target().CategoryMemberOf("surface") )
         {
            return -1.0; // custom filter for the bogey & bandit list.. ignore missiles, targets way outside of commit range, 
         }
         foreach (WsfSA_EntityPerception ent in PerceivedBandits())
         {
            if (ent.Track().IsValid() &&  mapSTIFF_ARM[ent] == true)
            {
               return -1;
            }
         }
         return 1.0;
      end_script
#
#      # This function "scores" assets for consideration to be added to the assets list
#      script double AssetConsiderationScoring(WsfSA_EntityPerception asset)
#          writeln("^^^^^^^  AssetConsiderationScoring ..............................");
#          if(asset.IsValid())
#          {
#             WsfGeoPoint pt = WsfGeoPoint.Construct(asset.Lat(), asset.Lon(), asset.Altitude());
#             double range_km = PLATFORM.SlantRangeTo(pt) * 0.001;
#             double score = range_km;
#             writeln("           Asset: ", asset.PerceivedName(), " score = ", score);
#             return score;          
#          }
#          else
#          {
#             writeln("           Asset *** INVALID ***");
#             return -1.0;
#          }
#          return 0.0;
#      end_script



      script double CalculateRiskPosedByEntity(WsfSA_EntityPerception aEntity) 
          writeln_d("T = ",TIME_NOW," ",iacid.Name()," RiskPosed");
          if (no_tree){return 0;} 
          if(aEntity.IsValid())
          {            
             WsfGeoPoint pt = WsfGeoPoint.Construct(aEntity.Lat(), aEntity.Lon(), aEntity.Altitude());
             double range_km = PLATFORM.SlantRangeTo(pt) * 0.001;
             double score = (600 - range_km)/600.0;
             if( score < -1.0 ) { score = -1.0; }
             if( score >  1.0 ) { score =  1.0; }
             return score;
          }
          # Entity is invalid
          return -1.0;
      end_script

      # This calculates the defensiveness that is induced by the specified entity
      # The score/value should range between +/- 1.0
      script double CalculateDefensivenessInducedByEntity(WsfSA_EntityPerception aEntity)
         writeln_d("T = ",TIME_NOW," ",iacid.Name()," DefensivenessInduced");
          if (no_tree){return 0;}
          if(aEntity.IsValid())
          {
             WsfGeoPoint pt = WsfGeoPoint.Construct(aEntity.Lat(), aEntity.Lon(), aEntity.Altitude());
             double range_km = PLATFORM.SlantRangeTo(pt) * 0.001;
             double score = (500 - range_km)/500.0;
             if( score < -1.0 ) { score = -1.0; }
             if( score >  1.0 ) { score =  1.0; }
             return score;
          }
          # Entity is invalid
          return -1.0;
      end_script

      # This calculates the urgency that is induced by the specified entity
      # The score/value should range between +/- 1.0
      script double CalculateUrgencyInducedByEntity(WsfSA_EntityPerception aEntity)
         writeln_d("T = ",TIME_NOW," ",iacid.Name()," UrgencyInduced");
          if (no_tree){return 0;}
          if(aEntity.IsValid())
          {
             WsfGeoPoint pt = WsfGeoPoint.Construct(aEntity.Lat(), aEntity.Lon(), aEntity.Altitude());
             double range_km = PLATFORM.SlantRangeTo(pt) * 0.001;
             double score = (400 - range_km)/400.0;
             if( score < -1.0 ) { score = -1.0; }
             if( score >  1.0 ) { score =  1.0; }
             return score;
          }
          # Entity is invalid
          return -1.0;
      end_script

      # These custom scripts will override default functions      
      #custom_scripts

         script double CalcRisk()
            writeln_d("T = ",TIME_NOW," ",iacid.Name()," CalcRisk");         
            if (no_tree){return 0;}
            Array<WsfSA_EntityPerception> threatList = PROCESSOR.PrioritizedThreatEntities();
            double highestRisk = -1.0;            
            for( int i ; i < threatList.Size() ; i = i + 1 )
            {
               WsfSA_EntityPerception threat = threatList.Get(i);
               double risk = threat.Risk();
               if( risk > highestRisk )
               {
                  highestRisk = risk;
               }
            }            
            return highestRisk;
         end_script
         
         script double CalcSelfRisk()
            return -0.4;
         end_script
         
         script double CalcFlightRisk()
            return -0.3;
         end_script
         
         script double CalcPackageRisk()
            return -0.2;
         end_script
         
         script double CalcMissionRisk()
            return -0.1;
         end_script
         
         script double CalcDefensiveness()
            writeln_d("T = ",TIME_NOW," ",iacid.Name()," CalcDefensiveness");
            if (no_tree){return 0;}
            Array<WsfSA_EntityPerception> threatList = PROCESSOR.PrioritizedThreatEntities();
            double highestDefensiveness = -1.0;            
            for( int i ; i < threatList.Size() ; i = i + 1 )
            {
               WsfSA_EntityPerception threat = threatList.Get(i);
               double defensiveness = threat.Defensiveness();
               if( defensiveness > highestDefensiveness )
               {
                  highestDefensiveness = defensiveness;
               }
            }            
            return highestDefensiveness;
         end_script
         
         script double CalcUrgency()
            writeln_d("T = ",TIME_NOW," ",iacid.Name()," CalcUrgency");
            if (no_tree){return 0;}
            Array<WsfSA_EntityPerception> threatList = PROCESSOR.PrioritizedThreatEntities();
            double highestUrgency = -1.0;            
            for( int i ; i < threatList.Size() ; i = i + 1 )
            {
               WsfSA_EntityPerception threat = threatList.Get(i);
               double urgency = threat.Urgency();
               if( urgency > highestUrgency )
               {
                  highestUrgency = urgency;
               }
            }            
            return highestUrgency;
         end_script
         
         script bool CalcWeaponSupport()
         /*
         PURPOSE   evaluate when or if weapons need support, create list of targets
         AUTHOR   Snyder

         Parameter Description
             me         WsfPlatform - Platform being analyzed, loop through my flight
             guid_list  Array<WsfPlatform>   - List of weapons I am supporting
             needil     BOOL - True if a weapon requires my support
             ill_list   Array - Array of target tracks I'm supporting weapons against
             beans      int  - number of missiles that require my support 

         Technical Description:
            Loop over all percieved bandits, if there is an active weapon that I own against any one of 
            my them, and missile range to go is > pitbull range

         TO DO: 
            -make this work for forward pass shots/third party/net enabled
            -add logic for snipping missiles or assessing to fail 
         */
              if (no_tree){return 0;}
              writeln_d("T = ",TIME_NOW," ",iacid.Name()," wpn");              
              needil = false;
              ill_list.Clear();        // clear out the ill_list

               bool support = false;
#               Array<WsfLocalTrack> i_list = Array<WsfLocalTrack>();
               Array<WsfPlatform> g_list = iacid->guid_list;

               foreach (WsfPlatform msl in g_list)
               {
                  if (msl.IsNull() || !msl.IsValid()){continue;}
                  WsfTrack trk = msl.CurrentTargetTrack();
                  if (msl.SlantRangeTo(trk) > iacid->PITBULL)
                  { 
                     support = true ; 
                     foreach (WsfSA_EntityPerception saent in PerceivedBandits())
                     {
                        if (saent.Track().IsValid() && !saent.Track().IsNull() && saent.Track().Target().IsValid() 
                            && saent.Track().TargetName() == trk.TargetName())
                        {
                           WsfLocalTrack ltrk = iacid.MasterTrackList().Find(saent.Track().TrackId());
                           ill_list.PushBack(ltrk);
                        }
                     }
                  } 
               }
               beans = ill_list.Size(); // total number of missiles requiring my support
               needil = support; // set platform global variable needil
               
               // determine if anyone in my flight is guiding missiles 
               flt_guiding=false; // initialize to false
               if(needil) // I'm guiding
               {
                  flt_guiding = true;
               }
               else if (iacid == FTR_RULES_CHAIN.Commander()) // if flight lead
               {  // loop through my subordinates
                  for (int mate=0; mate < FTR_RULES_CHAIN.SubordinateCount(); mate+=1)
                  {
                     if (FTR_RULES_CHAIN.SubordinateEntry(mate).CategoryMemberOf("missile")) {continue;}
                     if (FTR_RULES_CHAIN.SubordinateEntry(mate)->needil)
                     {
                        flt_guiding = true; 
                        break;
                     }
                  }
               }
               else // i'm a subordinate
               {  // check my flight lead
                  if (FTR_RULES_CHAIN.Commander()->needil)
                  {
                     flt_guiding = true;
                  }
                  else
                  {  // check my peers
                     for (int mate=0; mate < FTR_RULES_CHAIN.PeerCount(); mate+=1)
                     {
                        if (FTR_RULES_CHAIN.PeerEntry(mate).CategoryMemberOf("missile")){continue;}
                        if (FTR_RULES_CHAIN.PeerEntry(mate)->needil || flt_guiding)
                        {
                           flt_guiding = true; 
                           break;
                        }
                     } // check my subordinates
                     for (int mate=0; mate < FTR_RULES_CHAIN.SubordinateCount(); mate+=1)
                     {
                        if (FTR_RULES_CHAIN.SubordinateEntry(mate).CategoryMemberOf("missile")){continue;}
                        if (FTR_RULES_CHAIN.SubordinateEntry(mate)->needil || flt_guiding)
                        {
                           flt_guiding = true; 
                           break;
                        }
                     }
                  }
               }
               return needil;
         end_script
        

         # This calculates the threat level that is induced by the specified entity
         # The score/value should range between +/- 1.0
        script double CalculateThreatLevel(WsfSA_EntityPerception aEntity, bool aIsBogie)
        /*
        PURPOSE  This calculates the threat level by the specified entity, the higher the value the more threatened I feel
        AUTHOR   Snyder
        Parameter Description
           iacid       WsfPlatform  - Platform being analyzed
           MAR         DOUBLE - Threat value is 1 if inside this range
           DOR         DOUBLE - Threat value is 0.5 if inside this range
        Technical Description:
           The score/value should range between +/- 1.0     
           me        MAR   DOR   Lim       thrt      
          --->        |     |     |        <---
                     1.0   0.5   0.0
                     
                     Lim (limit range) = DOR + (DOR-MAR) = 2*DOR-MAR
                     
                     If the bandit is outside of Lim range, threat value is 0.0
                     If the bandit is inside of MAR range, threat value is 1.0                     
                     If the bandit is between MAR and Lim range and hot to me, calculate a normalized threat value
                     and if the bandit is at DOR range, the threat value calculated should be 0.5
        */
           if (no_tree){return 0;}     
           writeln_d("T = ",TIME_NOW," ",iacid.Name()," thrtlvl");              
           double ThreatVal = -1.0;
           double Lim = 2*(iacid->DOR) - iacid->MAR;
           if( !aEntity.IsValid() )
           {
              writeln("Invalid entity in CalculateThreatLevel");
              return 0.0;
           }
           if (aEntity.Track().IsValid() && aEntity.Track().Target().IsValid() && aEntity.Track().AirDomain() && 
           iacid.Side() != aEntity.Track().Side())
           {
              if (iacid.SlantRangeTo(aEntity.Track()) > Lim)
              {
                 return 0.0; // not threatening 
              }
              if (iacid.SlantRangeTo(aEntity.Track()) < iacid->MAR)
              {
                 return 1.0; // Very threatening
              }
              if (!aEntity.Track().Target().CategoryMemberOf("missile")) //  && 
              {
                 // threat is not a missile and is between MAR and Lim range
                 ThreatVal = (Lim - iacid.SlantRangeTo(aEntity.Track())) / (Lim - iacid->MAR);
                 // if threat is not hot to me, decrease the threat level
                 if (MATH.Fabs(aEntity.Track().RelativeBearingTo(iacid)) > iacid->THRT_ASPCT)
                 {
                    ThreatVal = ThreatVal/2;
                 }
                 return ThreatVal;
              }
           }
           return ThreatVal;  
        end_script       
         
         script double CalculateTargetValue(WsfSA_EntityPerception aEntity, bool aIsBogie)
         /*
         PURPOSE:        score hostile tracks to determine ppmjid, leading to weapon firing decisions
         AUTHOR:         Snyder
         Classification: UNCLASSIFIED//FOUO

         Parameter Description  

         Technical Description:
            loop through my local tracks of hostiles, score each hostile based on WEZ percentage, if weapons currently
            in air against hostile, range from hostile to my protected entity,
            PRDATA tactics, and various other factors

            This assumes perfect correlation/CID 
            
             things to consider:
               - * WEZ percentage 
               - * mxtgtd 
               - * PLATFORM->ENG_TGT 
               - * targeting hostile who is threatening my PE
            */
            if (no_tree){return 0;}
            writeln_d("T = ",TIME_NOW," ",iacid.Name()," tgtval");            
            WsfLocalTrack temp_track;
            #WsfPlatform iacid = PLATFORM;
            Array<int> bng_msl = iacid->WINCHESTER;
            bool fox3_left = iacid.Weapon("fox3").QuantityRemaining() > bng_msl[0];
            bool fox2_left = iacid.Weapon("fox2").QuantityRemaining() > bng_msl[1];
            bool fox1_left = iacid.Weapon("fox1").QuantityRemaining() > bng_msl[2];
            WsfWeapon weap;
            bool in_rtr;
            double rmax_fraction;
            #FileIO iout = FileIO();
            #iout.Open(iout_path, "append");
            //  this assumes you always want to prioritize fox3 over others 
               if (fox3_left) { weap = iacid.Weapon("fox3");}
               else if (fox2_left) { weap = iacid.Weapon("fox2");}
               else if (fox1_left) { weap = iacid.Weapon("fox1");}

               double env_score;
               double temp_score;
               double min_oba=180;
               double max_oba=-180; 
               WsfLocalTrack min_oba_trk;
               WsfLocalTrack max_oba_trk;
               bool debug = true;
//               mapSCORE.Clear();
               WsfTrack ltrk_ibg = aEntity.Track();
               if (!aEntity.Track().IsValid() || !aEntity.Track().LocationValid() || aEntity.Track().TargetName() == iacid.Name())
               {
#                  writeln(TIME_NOW," ",iacid.Name()," track or location not valid ... ",ltrk_ibg.IsValid()," ");
                  return 0.0;
               }
               if (!fox3_left && !fox2_left && !fox1_left)
               { // no missiles left, don't need to score anyone
                  return 0.0;
               }
               if(debug && iout_print){iout.Write(write_str("\n",iacid.Name()," TIME = ",TIME_NOW," s","\n TGT_BIAS:  RANGE   RMAX    RMAX_FRAC   SCORE","\n"));} 
               temp_score = 0;
               in_rtr = iacid.SlantRangeTo(aEntity.Track()) <= weap.LaunchComputer().LookupResult(aEntity.Track())[2];
               rmax_fraction = iacid.SlantRangeTo(aEntity.Track())/weap.LaunchComputer().LookupResult(aEntity.Track())[0];   
               temp_score = 20.0/rmax_fraction;
               if (rmax_fraction < 0)
               {
                  temp_score = 0; // if rmax_fraction is < 0 , probably range or velocity is not valid on the track
               }
               if(debug && iout_print){iout.Write(write_str(aEntity.Track().TargetName().Pad(-12),iacid.SlantRangeTo(aEntity.Track())*MATH.NM_PER_M(),"   ",
               weap.LaunchComputer().LookupResult(aEntity.Track())[0]*MATH.NM_PER_M(),Format.Fixed(rmax_fraction,2).Pad(8),Format.Fixed(temp_score,2).Pad(10),"\n"));}

            //     check for PRDATA variable ENG_TGT, increase score of this guy
               if (aEntity.Track().TargetName() == iacid->ENG_TGT) 
               {   temp_score = 40.0/rmax_fraction;
                  if(debug && iout_print){iout.Write(write_str("ENG_TGT ",temp_score,"\n"));}
               } // 40 might be too much, need to test
            //     if hostile is within RTR, score them higher
               if (in_rtr) 
               {  temp_score = temp_score + 5;
                  if(debug && iout_print){iout.Write(write_str("IN RTR ",temp_score,"\n"));}
               }

#            //     call to count shot
               Array<int> shot_count = count_shot(iacid,aEntity,PerceivedBandits());
               int tgtd_ihost = shot_count[0];
               int num_i_shot = shot_count[1];
               int num_in_air = shot_count[2]; 

            //     check for target saturation
               if (tgtd_ihost >= iacid->I_MXTGTD || num_i_shot >= iacid->I_LNCHATTGT)
               {  // target is saturated, drastically reduce the score
                  temp_score = temp_score/5;
                  if(debug && iout_print){iout.Write(write_str("I_MXTGTD ",temp_score,"\n"));}
               }
               if (tgtd_ihost >= mapMXTGTD[aEntity])
               {  // target is saturated, drastically reduce the score
                  temp_score = temp_score/5;
                  if(debug && iout_print){iout.Write(write_str("mapMXTGTD ",temp_score,"\n"));}
               }
               else if (num_i_shot >= mapMXTGT_AC[aEntity])
               {  // target is saturated, drastically reduce the score
                  temp_score = temp_score/5;
                  if(debug && iout_print){iout.Write(write_str("mapMXTGT_AC ",temp_score,"\n"));}
               }

            //     bias guys who are threatening to my protected entity/base
            //     check PROTECTED (defined as string array in PRDATA)
               if (iacid->MISSION_TYPE == "DCA" ) // PROTECTING should be string of lat,long locations 
               {  
                  Array<string> ProtectedLocation = iacid->PROTECTING;
                  foreach (string pe_geo in ProtectedLocation) // loop through all the protected locations
                  {  // increase score more the closer they get
                     WsfGeoPoint protect_point = WsfGeoPoint.Construct(pe_geo);
                     if (protect_point.SlantRangeTo(aEntity.Track().CurrentLocation()) < iacid->DISPLN_X)
                     {  temp_score = temp_score + 5 + MATH.NM_PER_M()*(iacid->DISPLN_X-aEntity.Track().CurrentLocation().SlantRangeTo(WsfGeoPoint.Construct(pe_geo)));
                        if(debug && iout_print){iout.Write(write_str("THREATN PE ",temp_score,"\n"));}
                     } 
                  }
               }
               else if (iacid->MISSION_TYPE == "HVAADCA" || iacid->MISSION_TYPE == "ESCORT") // PROTECTING should be list of platforms i'm protecting
               {  // check perceived range from hostile to all protected entities
                  Array<string> ProtectedEntities = iacid->PROTECTING;
                  foreach (string pe in ProtectedEntities) // loop through all the platform names i'm protecting
                  {   
                     for (int i = 0; i <= PerceivedAssets().Size() - 1; i = i + 1)
                     {  
                        WsfSA_EntityPerception asset = PerceivedAssets()[i]; 
                        WsfGeoPoint aloc = WsfGeoPoint().Construct(asset.Lat(),asset.Lon(),asset.Altitude());
                           // see if I have a perception of that asset and check perceived range between hostiles and protected entity
                        if (asset.PerceivedName() == pe && 
                           aEntity.Track().CurrentLocation().SlantRangeTo(aloc) < iacid->DISPLN_X)
                        {  // increase score more the closer they get
                           temp_score = temp_score + 5 + MATH.NM_PER_M()*(iacid->DISPLN_X-aEntity.Track().CurrentLocation().SlantRangeTo(aloc));
                           if(debug && iout_print){iout.Write(write_str("THREATN PE ",temp_score,"\n"));}
                        } 
                     }
                  }
               }
               else if (iacid->MISSION_TYPE == "SWEEP" )
               { // if mission is SWEEP, prioritize aircraft
                  if (aEntity.Track().AirDomain())
                  {
                     temp_score = temp_score + 5;
                     if(debug && iout_print){iout.Write(write_str("SWEEP MISION AND TGT IS A/C ",temp_score,"\n"));}
                  }
               }
#               writeln(write_str(TIME_NOW," ",iacid.Name()," scoring threat ",ltrk_ibg.TargetName(),", score = ",temp_score));
            return temp_score;
         end_script 
        

      // initialize some assessment variables
      on_initialize 
         //Brawler RULES variables
         //set at initialization
         // number of times I have pumped
         n_pump = 0;
         // true if this is my first pass (haven't pumped)       
         first_pass = true; 
         // init to ingress
         faz = "ingress";  
         // true if my mission is complete
         attack_success = false; 
         // init to ingress
         faz_desired = "ingress"; 
         // set to true if I desire to change phase
         change_desired = false; 

         // initialize maneuver variables w/ some variation
         PLATFORM->REACT_RNG = MATH.RandomGaussian(PLATFORM->REACT_RNG,PLATFORM->EXEC_VAR/2);
         PLATFORM->DOR = MATH.RandomGaussian(PLATFORM->DOR,PLATFORM->EXEC_VAR/2);
         // determine if I will perform pre-planned maneuver
         react = MATH.RandomUniform() < iacid->PRE_PLAN; 
         // determine if I will evade a weapon  
         evd_msl = MATH.RandomUniform() < iacid->PMSL_EVD;
         // determine if I am willing to snip a weapon 
         p_snip = MATH.RandomUniform() < iacid->RISK_WPN;
         // determine if I am willing to pump/turn cold
         p_pump = MATH.RandomUniform() < iacid->RISK_AVRS;
         pump_per = iacid->p_pump;
         write(iacid.Name()," Agressiveness: ",100*(1-iacid->RISK_AVRS),"% p_pump: ",p_pump," p_snip: ",p_snip,"\n");
         // init to commit speed & altitude (PRDATA)
         pr_speed = iacid->COMMIT_SPD*atmos.SonicVelocity(iacid.Altitude());
         pr_altitude = iacid.Altitude();
         // initialize vector heading to current heading
         pr_heading = iacid.Heading();
         // init to PRDATA start type
         rule_type = iacid->START_TYPE;
         RuleReason = "START TYPE";
         flt_lead = false; // true if I am the flight lead
         reason = "";      
      end_on_initialize  
      
      // update my SA picture of the environment     
      on_update 
         if (no_tree){return;}
         writeln_d("T = ",TIME_NOW," ",iacid.Name()," on_update");
         WsfBrawlerProcessor BRAWLER;
         if (iacid.Mover().Type() == "WSF_BRAWLER_MOVER")
         {
            for (int i=0; i<iacid.ProcessorCount();i+=1)
            {
               if (iacid.ProcessorEntry(i).Type() == "WSF_BRAWLER_PROCESSOR")
               {
                  BRAWLER = (WsfBrawlerProcessor)iacid.ProcessorEntry(i);
                  break;
               }
            }
         }   
       if (iout_print)
       {
   #       iout.Writeln(" ");
          iout.Write(write_str("\nFIGHTER_ASSESS...SIMULATION TIME HAS REACHED ",TIME_NOW, " s\n",
                      "A/C ",iacid.Name(),"\n"));
       }
       if (PLATFORM.Altitude() < 0) 
       {
          PLATFORM.DeletePlatform();
       }
   
   //     initialize some variables
         dead_buddy=false;  // do i have a dead wingman
         onrail = false;    // 
         stiff_arm_all=false;
         ing4last = false;
         spiked = false;    // Am I spiked
         msl_spike = false; // Am I spiked by a missile 
         rng_cls_hst = 200.0*MATH.M_PER_NM(); // Range to closest hostile
         rng_cls_sam = 200.0*MATH.M_PER_NM(); // Range to closest SAM
         pr_altitude = iacid->COMMIT_ALT;
         pr_speed = iacid->COMMIT_SPD * atmos.SonicVelocity(iacid.Altitude());
         int plan_1 = PLAYBOOK[0]; 
         int plan_2 = PLAYBOOK[1]; // create local version of these to manipulate in this routine
         int plan_3 = PLAYBOOK[2]; 
         bng_msl = flight_weapon_left(iacid,WINCHESTER,ielement);
         // determine if i'm bingo or joker fuel
         joker_fuel = PLATFORM.FuelRemaining() <= PLATFORM->JOKER_FUEL;
         bng_fuel = PLATFORM.FuelRemaining() <= PLATFORM->BINGO_FUEL;          

   // set flight lead
   // convention: every A/C has a command chain named IFLITE & ELEMENT.
   //             only the flight lead can make decision to change phase                
         flight_lead = iflite.CommanderName();
         if (!WsfSimulation.FindPlatform(flight_lead).IsValid())
         {  // flight lead is dead, need to choose new flight lead
            // for now, just arbitrarily pick first person to get here
            iacid.SetCommander(iflite.Name(),iacid);
            flight_lead = iflite.CommanderName();
      #      iacid.SetCommander(iacid); // flight lead = commander
         }
         if (flight_lead == iacid.Name()) { flt_lead = true; }
         if (iout_print) {iout.Write(write_str(" fight leader = ",flight_lead," ",flt_lead,"\n"));}
      // re-set element command chain if element lead is dead
         if (!WsfSimulation.FindPlatform(iacid.CommandChain("ELEMENT").CommanderName()).IsValid())
         {
            iacid.SetCommander("ELEMENT",iacid); // just set to self
            el_lead = true;
         }
         if(iacid.CommandChain("ELEMENT").Commander() == iacid)
         {
            el_lead = true;
            if (iout_print) {iout.Write(write_str(" element = ",iacid.CommandChain("ELEMENT").Subordinates(),"\n"));}
         }
         else
         {
            el_lead = false;
            if (iout_print) {iout.Write(write_str(" element = ",iacid.CommandChain("ELEMENT").Commander(),"\n"));}
         }

      // take flight lead's data
         if (iacid == FTR_RULES_CHAIN.Commander())
         {  
            for (int mate; mate < FTR_RULES_CHAIN.SubordinateCount(); mate+=1)
            {
               // pre-planned reaction
               FTR_RULES_CHAIN.SubordinateEntry(mate)->react = react ;
            // determine if I will evade a weapon  
               FTR_RULES_CHAIN.SubordinateEntry(mate)->evd_msl = evd_msl; 
               FTR_RULES_CHAIN.SubordinateEntry(mate)->p_snip = p_snip;
               FTR_RULES_CHAIN.SubordinateEntry(mate)->p_pump = p_pump;
               FTR_RULES_CHAIN.SubordinateEntry(mate)->pump_per = pump_per;
            }
         }
      //  print out if I am to perform pre-planned maneuver, evade missile
         if (iout_print)
         {
            iout.Write(write_str(" rule_type = ",rule_type,"\n"));
            iout.Write(write_str(" phase = ",faz,"\n"));
            iout.Write(write_str(" reason = ",reason,"\n"));
            iout.Write(write_str(" pre-plan maneuver = ", react,"\n"));
            iout.Write(write_str(" evd_msl = ", evd_msl,"\n"));
            iout.Write(write_str(" t_phase = ",Format.Fixed(t_phase,2),"\n"));
            iout.Write(write_str(" bingo missile = ",PLATFORM->bng_msl,"\n"));
            iout.Write(write_str(" joker fuel = ",PLATFORM->joker_fuel,"\n"));
            iout.Write(write_str(" bingo fuel = ",PLATFORM->bng_fuel,"\n"));
            iout.Write(write_str(" n_pump = ",n_pump,"\n"));
            iout.Write(write_str(" needil = ", needil,"\n ill_list = ",ill_list,"\n"));     
            iout.Write(write_str(" flight guiding = ", flt_guiding,"\n"));                  
         }
         // search for the max score and set ppmjid equal to that guy
         double max = -100; 
         int ind = 0;
         shoot_list.Clear();
         ppmjid = null;
#         WsfTrack test = ppmjid.Track();
         foreach (WsfSA_EntityPerception ent in PerceivedBandits())
         {  
            if ( ent.TargetValue() > max )
            {  
               max = ent.TargetValue();
               ppmjid = ent; // set platform's ppmjid based on highest score
            }
         }
         // set first index in shoot list equal to ppmjid
         if (!ppmjid.IsNull() && ppmjid.IsValid() && ppmjid.Track().IsValid())
         {
            shoot_list[ind] = ppmjid;
            ind = ind + 1;
         }
         // need to loop through again to build the rest of the shoot list
            
         foreach (WsfSA_EntityPerception ent in PerceivedBandits())
         {  
            if ( ent.TargetValue() > 20.0 && ent != ppmjid) // add track to my shoot list
            {  
               shoot_list[ind] = ent;
               ind = ind + 1;
            }   
         }
            if(iout_print) {iout.Write(write_str(" shoot list = "));}
            foreach (WsfSA_EntityPerception ent in shoot_list)
            {
               if(iout_print) {iout.Write(write_str(" ",ent.PerceivedName()));}
            }
            if(iout_print) {iout.Write(write_str("\n"));}
            if (ppmjid.IsValid() && !ppmjid.IsNull())
            { 
               if(iout_print) {iout.Write(write_str(" ppmjid = ",ppmjid.PerceivedName(),"\n"));}
            }
            else
            {  
               if(iout_print) {iout.Write(write_str(" ppmjid = null","\n"));}
            }   
   // determine number of bad guys
         int nbg = 0;
         foreach (WsfSA_EntityPerception saent in PerceivedBandits())
         {
            if (saent.IsValid() && !saent.IsNull())
            {
               nbg = nbg + 1;
            }
      #            drawTrackLocation(PLATFORM,ltrk,1,Vec3.Construct(1.0,1.0,1.0));
      #            WsfDraw trk_drw = WsfDraw();
      #            if (!ltrk.IsValid()) {return;}
      #            trk_drw.SetDuration(1);
      #            trk_drw.SetColor(1,1,1);
      #            trk_drw.BeginLines();
      #               trk_drw.Vertex(PLATFORM.Location());
      #               trk_drw.Vertex(ltrk.CurrentLocation());
      #            trk_drw.End(); 
         }
         iout.Write(write_str(" bandits = ",PerceivedBandits().Size(),"\n"));
         if (iout_print) {iout.Write(write_str(" nbg = ",nbg,"\n"));}
   //    set apole tactic flag
         apole_tct = MATH.Fabs(iacid->APOLE_RDR) > 0.0;
         if (iout_print) {iout.Write(write_str(" apole tactic = ",apole_tct,"\n"));}

         // remove dead missiles from my guid_list
         foreach (WsfPlatform msl in guid_list)
         {
            if (!msl.IsValid())
            {
               guid_list.Erase(msl);
            }
         }
         if (iout_print && guid_list.Size() > 0) 
         {
            iout.Write(write_str(" guid_list: ","\n"));
            for (int i = 0; i<guid_list.Size(); i+=1)
            {
               iout.Write(write_str("    ",guid_list[i].Name(),"\n"));
            }
         }
     
         if (plan_3 == 3) // press to merge
         {
            p_pump = false;
         }
      // adjust pump_per based on risk_avrs and probability of snip (p_snip) 
         if (needil) 
         { 
            pump_per = p_pump && p_snip; 
         }
         else
         {
            pump_per = p_pump;
         }   
         pump_per = pump_per && n_pump < iacid->MX_PUMP;
         if (iout_print) {iout.Write(write_str(" pump_per = ",pump_per,"\n"));}

      // determine if any flight mates are dead
         dead_buddy = chk_dead_buddy(iacid);
         if (iout_print) {iout.Write(write_str(" dead_buddy = ", dead_buddy,"\n"));}

      // determine if I'm spiked
         spiked = spike_me(iacid);
         if (iout_print) {iout.Write(write_str(" spiked = ",spiked,"\n") );}
   
      // determine if someone is pointing at me and within a threatening range
         hot_asp = pointing_me(iacid,iacid->DOR,iacid->THRT_ASPCT, PerceivedBandits());
         if (iout_print) {iout.Write(write_str(" hot_asp = ",hot_asp,"\n"));}

      // determine if someone in my flight is hot
         buddy_hot;
         for (int i = 0; i <= PerceivedAssets().Size() - 1; i = i + 1)
         {  
            WsfSA_EntityPerception asset = PerceivedAssets()[i]; 
            if (!asset.IsValid() || !WsfSimulation.FindPlatform(asset.PerceivedName()).IsValid() || WsfSimulation.FindPlatform(asset.PerceivedName()).CategoryMemberOf("missile")) {continue;}
            if (WsfSimulation.FindPlatform(asset.PerceivedName())->faz != "pump" || WsfSimulation.FindPlatform(asset.PerceivedName())->faz != "egress") 
            { buddy_hot = true; } 
         }
   
      // determine if a sam is spiking me
         sam_spiked = sam_spike_me(iacid);
         if (iout_print) {iout.Write( write_str(" sam spiked = ",sam_spiked,"\n") );}

      // determine if I'm threatend by an A/C
         threatnd = false;
         foreach (WsfSA_EntityPerception ent in PerceivedBandits())
         {
            if (ent.ThreatLevel() > iacid->THRT_VAL)
            {
               threatnd = true;
               break;
            }
         }
         if (iout_print) {iout.Write( write_str(" threatnd = ",threatnd,"\n") );} 

      // determine if I'm threatened by a SAM  
         sam_threatnd = false;
         iacid->sam_threatnd = sam_threatn_me(iacid,iacid->SAM_PMP_RNG,iacid->SAM_AVD_RNG,iacid->SAM_ANG_AVD);
         if (iout_print) {iout.Write( write_str(" sam_threatnd = ",sam_threatnd,"\n") );}

      // determine if I've been in this faz long enough
         time_ok = time_check(iacid);
         if (iout_print) {iout.Write( write_str(" time_ok = ",time_ok,"\n") );}
   
      // print out each A/C's tail number
         drawCircle(PLATFORM,PROCESSOR.UpdateInterval(), "index", (string)iacid.Index(), "solid", 2, "line", Vec3.Construct(0.9, 0.6, 0.0), 1000);

      // print out each A/C's phase
         if (rule_type == "ftr")
         {
            drawCircle(PLATFORM,PROCESSOR.UpdateInterval(), "phase", faz, "solid", 2, "line", Vec3.Construct(0.9, 0.6, 0.0), 5000);
         }
         else
         {
            drawCircle(PLATFORM,PROCESSOR.UpdateInterval(), "rule_type", rule_type, "solid", 2, "line", Vec3.Construct(0.9, 0.6, 0.0), 5000);
         }
          // this is now handled in the behavior tree visualizer
   #      if (ppmjid.IsValid())
   #      {
   #         drawCircle(PLATFORM,PROCESSOR.UpdateInterval(), "ppmjid", ppmjid.TargetName(), "solid", 2, "line", Vec3.Construct(1.0, 1.0, 1.0), 5000);      
   #      }
   
         WsfPlatform my_wing;
         string element = "";
         if (iacid.CommandChain("ELEMENT").IsValid())
         {
            WsfCommandChain el_chain = iacid.CommandChain("ELEMENT");
            if (el_chain.CommanderName() == iacid.Name() && el_chain.SubordinateCount() > 0)
            {
               element = el_chain.SubordinateEntry(0).Name(); // should only be one element
            }
            else if (el_chain.CommanderName() != iacid.Name() && el_chain.Commander().IsValid())
            {
               element = el_chain.CommanderName(); // should only be one element
            }
         }
         if (element != "" && WsfSimulation.FindPlatform(element).IsValid()) 
         {  
            my_wing = WsfSimulation.FindPlatform(element); 
         }
      // determine if I'm aware of a threat missile and am threatened by it
         alerted = msl_alert(iacid);
         if (iout_print) 
         {
            if(msl_spike) 
            { 
               iout.Write(write_str(" MSL_SPIKED ","\n")); // msl_spike set in msl_alert
            } 
            iout.Write(write_str(" msl alerted = ",alerted,"\n"));
         } 

      // determine range to closest hostile
         rng_cls_hst = rng_close_host(iacid,PerceivedBandits());
         if (iout_print) {iout.Write(write_str(" closest hostile = ",Format.Fixed(rng_cls_hst*MATH.NM_PER_M(),2)," nmi","\n"));} 

         rng_cls_sam = rng_close_sam(iacid);
         if (iout_print) {iout.Write(write_str(" closest sam = ",Format.Fixed(rng_cls_sam*MATH.NM_PER_M(),2)," nmi","\n"));} 
      //  close log.txt and iout.txt
   #      if (iout_print){iout.Close();}
   #      if (log_print){log.Close();}
         WsfBrawlerMover mover = (WsfBrawlerMover)iacid.Mover();
   #      log.Write(write_str(iacid.Name()," ",BRAWLER.Throttle()," ",iacid.Fuel().DistanceToBingo()*MATH.NM_PER_M(),"\n"));
   #      BRAWLER
   #      return Success("Assessed the air picture");      
      end_on_update 