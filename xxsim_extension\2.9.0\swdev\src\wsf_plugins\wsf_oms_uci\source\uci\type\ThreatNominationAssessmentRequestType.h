// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__ThreatNominationAssessmentRequestType_h
#define Uci__Type__ThreatNominationAssessmentRequestType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SystemMissionPlanSpecificationType_h)
# include "uci/type/SystemMissionPlanSpecificationType.h"
#endif

#if !defined(Uci__Type__AssessmentSuppressionEnum_h)
# include "uci/type/AssessmentSuppressionEnum.h"
#endif

#if !defined(Uci__Type__EntityID_Type_h)
# include "uci/type/EntityID_Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** The inputs used in generating and/or requesting a Threat Nomination Assessment. */
      class ThreatNominationAssessmentRequestType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~ThreatNominationAssessmentRequestType()
         { }

         /** Returns this accessor's type constant, i.e. ThreatNominationAssessmentRequestType
           *
           * @return This accessor's type constant, i.e. ThreatNominationAssessmentRequestType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::threatNominationAssessmentRequestType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const ThreatNominationAssessmentRequestType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Systems to be considered for the assessment [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::type::SystemMissionPlanSpecificationType, uci::type::accessorType::systemMissionPlanSpecificationType> SystemToConsider;

         /** A list of entities that can be utilized during nomination (zero entities assumes that all entities can be nominated).
           * [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::EntityID_Type, uci::type::accessorType::entityID_Type> EntityID;

         /** Returns the bounded list that is identified by the SystemToConsider.
           *
           * @return The bounded list identified by SystemToConsider.
           */
         virtual const uci::type::ThreatNominationAssessmentRequestType::SystemToConsider& getSystemToConsider() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SystemToConsider.
           *
           * @return The bounded list identified by SystemToConsider.
           */
         virtual uci::type::ThreatNominationAssessmentRequestType::SystemToConsider& getSystemToConsider()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SystemToConsider.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSystemToConsider(const uci::type::ThreatNominationAssessmentRequestType::SystemToConsider& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the AssessmentSuppression.
           *
           * @return The value of the enumeration identified by AssessmentSuppression.
           */
         virtual const uci::type::AssessmentSuppressionEnum& getAssessmentSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the AssessmentSuppression.
           *
           * @return The value of the enumeration identified by AssessmentSuppression.
           */
         virtual uci::type::AssessmentSuppressionEnum& getAssessmentSuppression()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the AssessmentSuppression.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setAssessmentSuppression(const uci::type::AssessmentSuppressionEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the AssessmentSuppression.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setAssessmentSuppression(uci::type::AssessmentSuppressionEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the EntityID.
           *
           * @return The bounded list identified by EntityID.
           */
         virtual const uci::type::ThreatNominationAssessmentRequestType::EntityID& getEntityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the EntityID.
           *
           * @return The bounded list identified by EntityID.
           */
         virtual uci::type::ThreatNominationAssessmentRequestType::EntityID& getEntityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the EntityID.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setEntityID(const uci::type::ThreatNominationAssessmentRequestType::EntityID& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         ThreatNominationAssessmentRequestType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The ThreatNominationAssessmentRequestType to copy from
           */
         ThreatNominationAssessmentRequestType(const ThreatNominationAssessmentRequestType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this ThreatNominationAssessmentRequestType to the contents of the
           * ThreatNominationAssessmentRequestType on the right hand side (rhs) of the assignment
           * operator.ThreatNominationAssessmentRequestType [only available to derived classes]
           *
           * @param rhs The ThreatNominationAssessmentRequestType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::ThreatNominationAssessmentRequestType
           * @return A constant reference to this ThreatNominationAssessmentRequestType.
           */
         const ThreatNominationAssessmentRequestType& operator=(const ThreatNominationAssessmentRequestType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // ThreatNominationAssessmentRequestType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__ThreatNominationAssessmentRequestType_h

