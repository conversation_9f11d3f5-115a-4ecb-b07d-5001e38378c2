// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WeatherRadarCellLocationType_h
#define Uci__Type__WeatherRadarCellLocationType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__AngleHalfType_h)
# include "uci/type/AngleHalfType.h"
#endif

#if !defined(Uci__Type__AngleType_h)
# include "uci/type/AngleType.h"
#endif

#if !defined(Uci__Type__AltitudeType_h)
# include "uci/type/AltitudeType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WeatherRadarCellLocationType sequence accessor class */
      class WeatherRadarCellLocationType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WeatherRadarCellLocationType()
         { }

         /** Returns this accessor's type constant, i.e. WeatherRadarCellLocationType
           *
           * @return This accessor's type constant, i.e. WeatherRadarCellLocationType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::weatherRadarCellLocationType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WeatherRadarCellLocationType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Latitude.
           *
           * @return The value of the simple primitive data type identified by Latitude.
           */
         virtual uci::type::AngleHalfTypeValue getLatitude() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Latitude.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setLatitude(uci::type::AngleHalfTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Longitude.
           *
           * @return The value of the simple primitive data type identified by Longitude.
           */
         virtual uci::type::AngleTypeValue getLongitude() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Longitude.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setLongitude(uci::type::AngleTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the UpperAltitude.
           *
           * @return The value of the simple primitive data type identified by UpperAltitude.
           */
         virtual uci::type::AltitudeTypeValue getUpperAltitude() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the UpperAltitude.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setUpperAltitude(uci::type::AltitudeTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the LowerAltitude.
           *
           * @return The value of the simple primitive data type identified by LowerAltitude.
           */
         virtual uci::type::AltitudeTypeValue getLowerAltitude() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the LowerAltitude.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setLowerAltitude(uci::type::AltitudeTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by LowerAltitude exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by LowerAltitude is emabled or not
           */
         virtual bool hasLowerAltitude() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by LowerAltitude
           *
           * @param type = uci::type::accessorType::altitudeType This Accessor's accessor type
           */
         virtual void enableLowerAltitude(uci::base::accessorType::AccessorType type = uci::type::accessorType::altitudeType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by LowerAltitude */
         virtual void clearLowerAltitude()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WeatherRadarCellLocationType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WeatherRadarCellLocationType to copy from
           */
         WeatherRadarCellLocationType(const WeatherRadarCellLocationType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WeatherRadarCellLocationType to the contents of the
           * WeatherRadarCellLocationType on the right hand side (rhs) of the assignment operator.WeatherRadarCellLocationType
           * [only available to derived classes]
           *
           * @param rhs The WeatherRadarCellLocationType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::WeatherRadarCellLocationType
           * @return A constant reference to this WeatherRadarCellLocationType.
           */
         const WeatherRadarCellLocationType& operator=(const WeatherRadarCellLocationType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WeatherRadarCellLocationType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WeatherRadarCellLocationType_h

