
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>get_property &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="if" href="if.html" />
    <link rel="prev" title="get_filename_component" href="get_filename_component.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="if.html" title="if"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="get_filename_component.html" title="get_filename_component"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">get_property</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="get-property">
<span id="command:get_property"></span><h1>get_property<a class="headerlink" href="#get-property" title="Permalink to this heading">¶</a></h1>
<p>Get a property.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">get_property(</span><span class="nv">&lt;variable&gt;</span><span class="w"></span>
<span class="w">             </span><span class="o">&lt;</span><span class="no">GLOBAL</span><span class="w">             </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">DIRECTORY</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;dir&gt;</span><span class="p">]</span><span class="w">  </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">TARGET</span><span class="w">    </span><span class="nv">&lt;target&gt;</span><span class="w"> </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">SOURCE</span><span class="w">    </span><span class="nv">&lt;source&gt;</span><span class="w"></span>
<span class="w">                        </span><span class="p">[</span><span class="no">DIRECTORY</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">TARGET_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;target&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">INSTALL</span><span class="w">   </span><span class="nv">&lt;file&gt;</span><span class="w">   </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">TEST</span><span class="w">      </span><span class="nv">&lt;test&gt;</span><span class="w">   </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">CACHE</span><span class="w">     </span><span class="nv">&lt;entry&gt;</span><span class="w">  </span><span class="p">|</span><span class="w"></span>
<span class="w">              </span><span class="no">VARIABLE</span><span class="w">           </span><span class="o">&gt;</span><span class="w"></span>
<span class="w">             </span><span class="no">PROPERTY</span><span class="w"> </span><span class="nv">&lt;name&gt;</span><span class="w"></span>
<span class="w">             </span><span class="p">[</span><span class="no">SET</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">DEFINED</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">BRIEF_DOCS</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">FULL_DOCS</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Gets one property from one object in a scope.</p>
<p>The first argument specifies the variable in which to store the result.
The second argument determines the scope from which to get the property.
It must be one of the following:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">GLOBAL</span></code></dt><dd><p>Scope is unique and does not accept a name.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DIRECTORY</span></code></dt><dd><p>Scope defaults to the current directory but another
directory (already processed by CMake) may be named by the
full or relative path <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code>.
Relative paths are treated as relative to the current source directory.
See also the <span class="target" id="index-0-command:get_directory_property"></span><a class="reference internal" href="get_directory_property.html#command:get_directory_property" title="get_directory_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">get_directory_property()</span></code></a> command.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.19: </span><code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> may reference a binary directory.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TARGET</span></code></dt><dd><p>Scope must name one existing target.
See also the <span class="target" id="index-0-command:get_target_property"></span><a class="reference internal" href="get_target_property.html#command:get_target_property" title="get_target_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">get_target_property()</span></code></a> command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SOURCE</span></code></dt><dd><p>Scope must name one source file.  By default, the source file's property
will be read from the current source directory's scope.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.18: </span>Directory scope can be overridden with one of the following sub-options:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">DIRECTORY</span> <span class="pre">&lt;dir&gt;</span></code></dt><dd><p>The source file property will be read from the <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> directory's
scope.  CMake must already know about
the directory, either by having added it through a call
to <span class="target" id="index-0-command:add_subdirectory"></span><a class="reference internal" href="add_subdirectory.html#command:add_subdirectory" title="add_subdirectory"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_subdirectory()</span></code></a> or <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> being the top level directory.
Relative paths are treated as relative to the current source directory.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.19: </span><code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> may reference a binary directory.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TARGET_DIRECTORY</span> <span class="pre">&lt;target&gt;</span></code></dt><dd><p>The source file property will be read from the directory scope in which
<code class="docutils literal notranslate"><span class="pre">&lt;target&gt;</span></code> was created (<code class="docutils literal notranslate"><span class="pre">&lt;target&gt;</span></code> must therefore already exist).</p>
</dd>
</dl>
</div>
<p>See also the <span class="target" id="index-0-command:get_source_file_property"></span><a class="reference internal" href="get_source_file_property.html#command:get_source_file_property" title="get_source_file_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">get_source_file_property()</span></code></a> command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INSTALL</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Scope must name one installed file path.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TEST</span></code></dt><dd><p>Scope must name one existing test.
See also the <span class="target" id="index-0-command:get_test_property"></span><a class="reference internal" href="get_test_property.html#command:get_test_property" title="get_test_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">get_test_property()</span></code></a> command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CACHE</span></code></dt><dd><p>Scope must name one cache entry.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VARIABLE</span></code></dt><dd><p>Scope is unique and does not accept a name.</p>
</dd>
</dl>
<p>The required <code class="docutils literal notranslate"><span class="pre">PROPERTY</span></code> option is immediately followed by the name of
the property to get.  If the property is not set an empty value is
returned, although some properties support inheriting from a parent scope
if defined to behave that way (see <span class="target" id="index-0-command:define_property"></span><a class="reference internal" href="define_property.html#command:define_property" title="define_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">define_property()</span></code></a>).</p>
<p>If the <code class="docutils literal notranslate"><span class="pre">SET</span></code> option is given the variable is set to a boolean
value indicating whether the property has been set.  If the <code class="docutils literal notranslate"><span class="pre">DEFINED</span></code>
option is given the variable is set to a boolean value indicating
whether the property has been defined such as with the
<span class="target" id="index-1-command:define_property"></span><a class="reference internal" href="define_property.html#command:define_property" title="define_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">define_property()</span></code></a> command.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">BRIEF_DOCS</span></code> or <code class="docutils literal notranslate"><span class="pre">FULL_DOCS</span></code> is given then the variable is set to a
string containing documentation for the requested property.  If
documentation is requested for a property that has not been defined
<code class="docutils literal notranslate"><span class="pre">NOTFOUND</span></code> is returned.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <span class="target" id="index-0-prop_sf:GENERATED"></span><a class="reference internal" href="../prop_sf/GENERATED.html#prop_sf:GENERATED" title="GENERATED"><code class="xref cmake cmake-prop_sf docutils literal notranslate"><span class="pre">GENERATED</span></code></a> source file property may be globally visible.
See its documentation for details.</p>
</div>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-2-command:define_property"></span><a class="reference internal" href="define_property.html#command:define_property" title="define_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">define_property()</span></code></a></p></li>
<li><p><span class="target" id="index-0-command:set_property"></span><a class="reference internal" href="set_property.html#command:set_property" title="set_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set_property()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">get_property</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="get_filename_component.html"
                          title="previous chapter">get_filename_component</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="if.html"
                          title="next chapter">if</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/get_property.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="if.html" title="if"
             >next</a> |</li>
        <li class="right" >
          <a href="get_filename_component.html" title="get_filename_component"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">get_property</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>