// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SubsystemCompletedCalibrationType_h
#define Uci__Type__SubsystemCompletedCalibrationType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__CalibrationID_Type_h)
# include "uci/type/CalibrationID_Type.h"
#endif

#if !defined(Uci__Type__DateTimeType_h)
# include "uci/type/DateTimeType.h"
#endif

#if !defined(Uci__Type__SubsystemCalibrationResultEnum_h)
# include "uci/type/SubsystemCalibrationResultEnum.h"
#endif

#if !defined(Uci__Type__VisibleString256Type_h)
# include "uci/type/VisibleString256Type.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SubsystemCompletedCalibrationItemType_h)
# include "uci/type/SubsystemCompletedCalibrationItemType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SubsystemCompletedCalibrationType sequence accessor class */
      class SubsystemCompletedCalibrationType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SubsystemCompletedCalibrationType()
         { }

         /** Returns this accessor's type constant, i.e. SubsystemCompletedCalibrationType
           *
           * @return This accessor's type constant, i.e. SubsystemCompletedCalibrationType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::subsystemCompletedCalibrationType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SubsystemCompletedCalibrationType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates the results of an item tested by the Calibration. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SubsystemCompletedCalibrationItemType, uci::type::accessorType::subsystemCompletedCalibrationItemType> CalibrationItem;

         /** Returns the accessor that provides access to the complex content that is identified by the CalibrationID.
           *
           * @return The acecssor that provides access to the complex content that is identified by CalibrationID.
           */
         virtual const uci::type::CalibrationID_Type& getCalibrationID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CalibrationID.
           *
           * @return The acecssor that provides access to the complex content that is identified by CalibrationID.
           */
         virtual uci::type::CalibrationID_Type& getCalibrationID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the CalibrationID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by CalibrationID
           */
         virtual void setCalibrationID(const uci::type::CalibrationID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Timetag.
           *
           * @return The value of the simple primitive data type identified by Timetag.
           */
         virtual uci::type::DateTimeTypeValue getTimetag() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Timetag.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setTimetag(uci::type::DateTimeTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Result.
           *
           * @return The value of the enumeration identified by Result.
           */
         virtual const uci::type::SubsystemCalibrationResultEnum& getResult() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Result.
           *
           * @return The value of the enumeration identified by Result.
           */
         virtual uci::type::SubsystemCalibrationResultEnum& getResult()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Result.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setResult(const uci::type::SubsystemCalibrationResultEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Result.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setResult(uci::type::SubsystemCalibrationResultEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the FailReason.
           *
           * @return The value of the string data type identified by FailReason.
           */
         virtual const uci::type::VisibleString256Type& getFailReason() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the FailReason.
           *
           * @return The value of the string data type identified by FailReason.
           */
         virtual uci::type::VisibleString256Type& getFailReason()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setFailReason(const uci::type::VisibleString256Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setFailReason(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setFailReason(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by FailReason exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by FailReason is emabled or not
           */
         virtual bool hasFailReason() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by FailReason
           *
           * @param type = uci::type::accessorType::visibleString256Type This Accessor's accessor type
           */
         virtual void enableFailReason(uci::base::accessorType::AccessorType type = uci::type::accessorType::visibleString256Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by FailReason */
         virtual void clearFailReason()
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CalibrationItem.
           *
           * @return The bounded list identified by CalibrationItem.
           */
         virtual const uci::type::SubsystemCompletedCalibrationType::CalibrationItem& getCalibrationItem() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CalibrationItem.
           *
           * @return The bounded list identified by CalibrationItem.
           */
         virtual uci::type::SubsystemCompletedCalibrationType::CalibrationItem& getCalibrationItem()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the CalibrationItem.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCalibrationItem(const uci::type::SubsystemCompletedCalibrationType::CalibrationItem& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SubsystemCompletedCalibrationType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SubsystemCompletedCalibrationType to copy from
           */
         SubsystemCompletedCalibrationType(const SubsystemCompletedCalibrationType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SubsystemCompletedCalibrationType to the contents of the
           * SubsystemCompletedCalibrationType on the right hand side (rhs) of the assignment
           * operator.SubsystemCompletedCalibrationType [only available to derived classes]
           *
           * @param rhs The SubsystemCompletedCalibrationType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::SubsystemCompletedCalibrationType
           * @return A constant reference to this SubsystemCompletedCalibrationType.
           */
         const SubsystemCompletedCalibrationType& operator=(const SubsystemCompletedCalibrationType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SubsystemCompletedCalibrationType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SubsystemCompletedCalibrationType_h

