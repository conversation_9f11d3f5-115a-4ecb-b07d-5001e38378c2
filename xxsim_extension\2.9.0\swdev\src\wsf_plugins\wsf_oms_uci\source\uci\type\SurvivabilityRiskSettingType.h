// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SurvivabilityRiskSettingType_h
#define Uci__Type__SurvivabilityRiskSettingType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__RiskPrioritizationEnum_h)
# include "uci/type/RiskPrioritizationEnum.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__RiskSettingType_h)
# include "uci/type/RiskSettingType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SurvivabilityRiskSettingType sequence accessor class */
      class SurvivabilityRiskSettingType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SurvivabilityRiskSettingType()
         { }

         /** Returns this accessor's type constant, i.e. SurvivabilityRiskSettingType
           *
           * @return This accessor's type constant, i.e. SurvivabilityRiskSettingType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::survivabilityRiskSettingType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SurvivabilityRiskSettingType& accessor)
            throw(uci::base::UCIException) = 0;


         /** This is the allowable level of exposure risk that is permitted per the prioritization category. [Maximum occurrences:
           * 7]
           */
         typedef uci::base::BoundedList<uci::type::RiskSettingType, uci::type::accessorType::riskSettingType> RiskLevel;

         /** Returns the value of the enumeration that is identified by the RiskPrioritization.
           *
           * @return The value of the enumeration identified by RiskPrioritization.
           */
         virtual const uci::type::RiskPrioritizationEnum& getRiskPrioritization() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the RiskPrioritization.
           *
           * @return The value of the enumeration identified by RiskPrioritization.
           */
         virtual uci::type::RiskPrioritizationEnum& getRiskPrioritization()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the RiskPrioritization.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setRiskPrioritization(const uci::type::RiskPrioritizationEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the RiskPrioritization.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setRiskPrioritization(uci::type::RiskPrioritizationEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the RiskLevel.
           *
           * @return The bounded list identified by RiskLevel.
           */
         virtual const uci::type::SurvivabilityRiskSettingType::RiskLevel& getRiskLevel() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the RiskLevel.
           *
           * @return The bounded list identified by RiskLevel.
           */
         virtual uci::type::SurvivabilityRiskSettingType::RiskLevel& getRiskLevel()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the RiskLevel.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setRiskLevel(const uci::type::SurvivabilityRiskSettingType::RiskLevel& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SurvivabilityRiskSettingType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SurvivabilityRiskSettingType to copy from
           */
         SurvivabilityRiskSettingType(const SurvivabilityRiskSettingType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SurvivabilityRiskSettingType to the contents of the
           * SurvivabilityRiskSettingType on the right hand side (rhs) of the assignment operator.SurvivabilityRiskSettingType
           * [only available to derived classes]
           *
           * @param rhs The SurvivabilityRiskSettingType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::SurvivabilityRiskSettingType
           * @return A constant reference to this SurvivabilityRiskSettingType.
           */
         const SurvivabilityRiskSettingType& operator=(const SurvivabilityRiskSettingType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SurvivabilityRiskSettingType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SurvivabilityRiskSettingType_h

