// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__UncertaintyType_h
#define Uci__Type__UncertaintyType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__EllipseType_h)
# include "uci/type/EllipseType.h"
#endif

#if !defined(Uci__Type__DistanceType_h)
# include "uci/type/DistanceType.h"
#endif

#if !defined(Uci__Type__AngleHalfType_h)
# include "uci/type/AngleHalfType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the UncertaintyType sequence accessor class */
      class UncertaintyType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~UncertaintyType()
         { }

         /** Returns this accessor's type constant, i.e. UncertaintyType
           *
           * @return This accessor's type constant, i.e. UncertaintyType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::uncertaintyType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const UncertaintyType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the UncertaintyEllipse.
           *
           * @return The acecssor that provides access to the complex content that is identified by UncertaintyEllipse.
           */
         virtual const uci::type::EllipseType& getUncertaintyEllipse() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the UncertaintyEllipse.
           *
           * @return The acecssor that provides access to the complex content that is identified by UncertaintyEllipse.
           */
         virtual uci::type::EllipseType& getUncertaintyEllipse()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the UncertaintyEllipse to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by UncertaintyEllipse
           */
         virtual void setUncertaintyEllipse(const uci::type::EllipseType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the UncertaintyVertical.
           *
           * @return The value of the simple primitive data type identified by UncertaintyVertical.
           */
         virtual uci::type::DistanceTypeValue getUncertaintyVertical() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the UncertaintyVertical.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setUncertaintyVertical(uci::type::DistanceTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by UncertaintyVertical exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by UncertaintyVertical is emabled or not
           */
         virtual bool hasUncertaintyVertical() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by UncertaintyVertical
           *
           * @param type = uci::type::accessorType::distanceType This Accessor's accessor type
           */
         virtual void enableUncertaintyVertical(uci::base::accessorType::AccessorType type = uci::type::accessorType::distanceType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by UncertaintyVertical */
         virtual void clearUncertaintyVertical()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the UncertaintyTilt.
           *
           * @return The value of the simple primitive data type identified by UncertaintyTilt.
           */
         virtual uci::type::AngleHalfTypeValue getUncertaintyTilt() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the UncertaintyTilt.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setUncertaintyTilt(uci::type::AngleHalfTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by UncertaintyTilt exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by UncertaintyTilt is emabled or not
           */
         virtual bool hasUncertaintyTilt() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by UncertaintyTilt
           *
           * @param type = uci::type::accessorType::angleHalfType This Accessor's accessor type
           */
         virtual void enableUncertaintyTilt(uci::base::accessorType::AccessorType type = uci::type::accessorType::angleHalfType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by UncertaintyTilt */
         virtual void clearUncertaintyTilt()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         UncertaintyType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The UncertaintyType to copy from
           */
         UncertaintyType(const UncertaintyType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this UncertaintyType to the contents of the UncertaintyType on the
           * right hand side (rhs) of the assignment operator.UncertaintyType [only available to derived classes]
           *
           * @param rhs The UncertaintyType on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::UncertaintyType
           * @return A constant reference to this UncertaintyType.
           */
         const UncertaintyType& operator=(const UncertaintyType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // UncertaintyType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__UncertaintyType_h

