
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>output_required_files &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="qt_wrap_cpp" href="qt_wrap_cpp.html" />
    <link rel="prev" title="make_directory" href="make_directory.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="qt_wrap_cpp.html" title="qt_wrap_cpp"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="make_directory.html" title="make_directory"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">output_required_files</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="output-required-files">
<span id="command:output_required_files"></span><h1>output_required_files<a class="headerlink" href="#output-required-files" title="Permalink to this heading">¶</a></h1>
<p>Disallowed since version 3.0.  See CMake Policy <span class="target" id="index-0-policy:CMP0032"></span><a class="reference internal" href="../policy/CMP0032.html#policy:CMP0032" title="CMP0032"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0032</span></code></a>.</p>
<p>Approximate C preprocessor dependency scanning.</p>
<p>This command exists only because ancient CMake versions provided it.
CMake handles preprocessor dependency scanning automatically using a
more advanced scanner.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">output_required_files(</span><span class="nb">srcfile</span><span class="w"> </span><span class="nb">outputfile</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Outputs a list of all the source files that are required by the
specified <code class="docutils literal notranslate"><span class="pre">srcfile</span></code>.  This list is written into <code class="docutils literal notranslate"><span class="pre">outputfile</span></code>.  This is
similar to writing out the dependencies for <code class="docutils literal notranslate"><span class="pre">srcfile</span></code> except that it
jumps from <code class="docutils literal notranslate"><span class="pre">.h</span></code> files into <code class="docutils literal notranslate"><span class="pre">.cxx</span></code>, <code class="docutils literal notranslate"><span class="pre">.c</span></code> and <code class="docutils literal notranslate"><span class="pre">.cpp</span></code> files if possible.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="make_directory.html"
                          title="previous chapter">make_directory</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="qt_wrap_cpp.html"
                          title="next chapter">qt_wrap_cpp</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/output_required_files.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="qt_wrap_cpp.html" title="qt_wrap_cpp"
             >next</a> |</li>
        <li class="right" >
          <a href="make_directory.html" title="make_directory"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">output_required_files</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>