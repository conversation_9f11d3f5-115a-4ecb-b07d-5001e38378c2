// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__VelocityOrientationCovarianceType_h
#define Uci__Type__VelocityOrientationCovarianceType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__DoubleAccessor_h)
# include "uci/base/DoubleAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the VelocityOrientationCovarianceType sequence accessor class */
      class VelocityOrientationCovarianceType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~VelocityOrientationCovarianceType()
         { }

         /** Returns this accessor's type constant, i.e. VelocityOrientationCovarianceType
           *
           * @return This accessor's type constant, i.e. VelocityOrientationCovarianceType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::velocityOrientationCovarianceType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const VelocityOrientationCovarianceType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VnRr.
           *
           * @return The value of the simple primitive data type identified by VnRr.
           */
         virtual xs::Double getVnRr() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VnRr.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVnRr(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VnRp.
           *
           * @return The value of the simple primitive data type identified by VnRp.
           */
         virtual xs::Double getVnRp() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VnRp.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVnRp(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VnRy.
           *
           * @return The value of the simple primitive data type identified by VnRy.
           */
         virtual xs::Double getVnRy() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VnRy.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVnRy(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VnRy exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VnRy is emabled or not
           */
         virtual bool hasVnRy() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VnRy
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableVnRy(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VnRy */
         virtual void clearVnRy()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VeRr.
           *
           * @return The value of the simple primitive data type identified by VeRr.
           */
         virtual xs::Double getVeRr() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VeRr.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVeRr(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VeRp.
           *
           * @return The value of the simple primitive data type identified by VeRp.
           */
         virtual xs::Double getVeRp() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VeRp.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVeRp(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VeRy.
           *
           * @return The value of the simple primitive data type identified by VeRy.
           */
         virtual xs::Double getVeRy() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VeRy.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVeRy(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VeRy exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VeRy is emabled or not
           */
         virtual bool hasVeRy() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VeRy
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableVeRy(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VeRy */
         virtual void clearVeRy()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VdRr.
           *
           * @return The value of the simple primitive data type identified by VdRr.
           */
         virtual xs::Double getVdRr() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VdRr.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVdRr(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VdRp.
           *
           * @return The value of the simple primitive data type identified by VdRp.
           */
         virtual xs::Double getVdRp() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VdRp.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVdRp(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VdRy.
           *
           * @return The value of the simple primitive data type identified by VdRy.
           */
         virtual xs::Double getVdRy() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VdRy.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVdRy(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VdRy exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VdRy is emabled or not
           */
         virtual bool hasVdRy() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VdRy
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableVdRy(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VdRy */
         virtual void clearVdRy()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         VelocityOrientationCovarianceType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The VelocityOrientationCovarianceType to copy from
           */
         VelocityOrientationCovarianceType(const VelocityOrientationCovarianceType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this VelocityOrientationCovarianceType to the contents of the
           * VelocityOrientationCovarianceType on the right hand side (rhs) of the assignment
           * operator.VelocityOrientationCovarianceType [only available to derived classes]
           *
           * @param rhs The VelocityOrientationCovarianceType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::VelocityOrientationCovarianceType
           * @return A constant reference to this VelocityOrientationCovarianceType.
           */
         const VelocityOrientationCovarianceType& operator=(const VelocityOrientationCovarianceType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // VelocityOrientationCovarianceType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__VelocityOrientationCovarianceType_h

