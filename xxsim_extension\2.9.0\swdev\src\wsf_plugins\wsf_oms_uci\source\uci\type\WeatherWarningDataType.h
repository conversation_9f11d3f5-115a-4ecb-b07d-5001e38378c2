// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WeatherWarningDataType_h
#define Uci__Type__WeatherWarningDataType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__WeatherWarningEnum_h)
# include "uci/type/WeatherWarningEnum.h"
#endif

#if !defined(Uci__Type__ZoneType_h)
# include "uci/type/ZoneType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__DateTimeRangeType_h)
# include "uci/type/DateTimeRangeType.h"
#endif

#if !defined(Uci__Type__VisibleString1024Type_h)
# include "uci/type/VisibleString1024Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WeatherWarningDataType sequence accessor class */
      class WeatherWarningDataType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WeatherWarningDataType()
         { }

         /** Returns this accessor's type constant, i.e. WeatherWarningDataType
           *
           * @return This accessor's type constant, i.e. WeatherWarningDataType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::weatherWarningDataType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WeatherWarningDataType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates the time interval over which the warning applies. [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::type::DateTimeRangeType, uci::type::accessorType::dateTimeRangeType> Schedule;

         /** Returns the value of the enumeration that is identified by the WeatherWarningType.
           *
           * @return The value of the enumeration identified by WeatherWarningType.
           */
         virtual const uci::type::WeatherWarningEnum& getWeatherWarningType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the WeatherWarningType.
           *
           * @return The value of the enumeration identified by WeatherWarningType.
           */
         virtual uci::type::WeatherWarningEnum& getWeatherWarningType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the WeatherWarningType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWeatherWarningType(const uci::type::WeatherWarningEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the WeatherWarningType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWeatherWarningType(uci::type::WeatherWarningEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherArea.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherArea.
           */
         virtual const uci::type::ZoneType& getWeatherArea() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherArea.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherArea.
           */
         virtual uci::type::ZoneType& getWeatherArea()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WeatherArea to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WeatherArea
           */
         virtual void setWeatherArea(const uci::type::ZoneType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Schedule.
           *
           * @return The bounded list identified by Schedule.
           */
         virtual const uci::type::WeatherWarningDataType::Schedule& getSchedule() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Schedule.
           *
           * @return The bounded list identified by Schedule.
           */
         virtual uci::type::WeatherWarningDataType::Schedule& getSchedule()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Schedule.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSchedule(const uci::type::WeatherWarningDataType::Schedule& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Remarks.
           *
           * @return The value of the string data type identified by Remarks.
           */
         virtual const uci::type::VisibleString1024Type& getRemarks() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Remarks.
           *
           * @return The value of the string data type identified by Remarks.
           */
         virtual uci::type::VisibleString1024Type& getRemarks()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Remarks to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setRemarks(const uci::type::VisibleString1024Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Remarks to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setRemarks(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Remarks to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setRemarks(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Remarks exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Remarks is emabled or not
           */
         virtual bool hasRemarks() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Remarks
           *
           * @param type = uci::type::accessorType::visibleString1024Type This Accessor's accessor type
           */
         virtual void enableRemarks(uci::base::accessorType::AccessorType type = uci::type::accessorType::visibleString1024Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Remarks */
         virtual void clearRemarks()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WeatherWarningDataType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WeatherWarningDataType to copy from
           */
         WeatherWarningDataType(const WeatherWarningDataType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WeatherWarningDataType to the contents of the
           * WeatherWarningDataType on the right hand side (rhs) of the assignment operator.WeatherWarningDataType [only available
           * to derived classes]
           *
           * @param rhs The WeatherWarningDataType on the right hand side (rhs) of the assignment operator whose contents are used
           *      to set the contents of this uci::type::WeatherWarningDataType
           * @return A constant reference to this WeatherWarningDataType.
           */
         const WeatherWarningDataType& operator=(const WeatherWarningDataType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WeatherWarningDataType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WeatherWarningDataType_h

