// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StrikeTaskType_h
#define Uci__Type__StrikeTaskType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__TargetType_h)
# include "uci/type/TargetType.h"
#endif

#if !defined(Uci__Type__TargetInformationType_h)
# include "uci/type/TargetInformationType.h"
#endif

#if !defined(Uci__Type__AnglePairType_h)
# include "uci/type/AnglePairType.h"
#endif

#if !defined(Uci__Type__Point3D_Type_h)
# include "uci/type/Point3D_Type.h"
#endif

#if !defined(Uci__Type__StrikeTaskReleaseConstraintsType_h)
# include "uci/type/StrikeTaskReleaseConstraintsType.h"
#endif

#if !defined(Uci__Type__StrikeTaskWeaponListType_h)
# include "uci/type/StrikeTaskWeaponListType.h"
#endif

#if !defined(Uci__Type__DateTimeRangeType_h)
# include "uci/type/DateTimeRangeType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the StrikeTaskType sequence accessor class */
      class StrikeTaskType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~StrikeTaskType()
         { }

         /** Returns this accessor's type constant, i.e. StrikeTaskType
           *
           * @return This accessor's type constant, i.e. StrikeTaskType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::strikeTaskType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StrikeTaskType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Target.
           *
           * @return The acecssor that provides access to the complex content that is identified by Target.
           */
         virtual const uci::type::TargetType& getTarget() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Target.
           *
           * @return The acecssor that provides access to the complex content that is identified by Target.
           */
         virtual uci::type::TargetType& getTarget()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Target to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Target
           */
         virtual void setTarget(const uci::type::TargetType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TargetInformation.
           *
           * @return The acecssor that provides access to the complex content that is identified by TargetInformation.
           */
         virtual const uci::type::TargetInformationType& getTargetInformation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TargetInformation.
           *
           * @return The acecssor that provides access to the complex content that is identified by TargetInformation.
           */
         virtual uci::type::TargetInformationType& getTargetInformation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TargetInformation to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TargetInformation
           */
         virtual void setTargetInformation(const uci::type::TargetInformationType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TargetInformation exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TargetInformation is emabled or not
           */
         virtual bool hasTargetInformation() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TargetInformation
           *
           * @param type = uci::type::accessorType::targetInformationType This Accessor's accessor type
           */
         virtual void enableTargetInformation(uci::base::accessorType::AccessorType type = uci::type::accessorType::targetInformationType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TargetInformation */
         virtual void clearTargetInformation()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the IngressConstraint.
           *
           * @return The acecssor that provides access to the complex content that is identified by IngressConstraint.
           */
         virtual const uci::type::AnglePairType& getIngressConstraint() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the IngressConstraint.
           *
           * @return The acecssor that provides access to the complex content that is identified by IngressConstraint.
           */
         virtual uci::type::AnglePairType& getIngressConstraint()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the IngressConstraint to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by IngressConstraint
           */
         virtual void setIngressConstraint(const uci::type::AnglePairType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by IngressConstraint exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by IngressConstraint is emabled or not
           */
         virtual bool hasIngressConstraint() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by IngressConstraint
           *
           * @param type = uci::type::accessorType::anglePairType This Accessor's accessor type
           */
         virtual void enableIngressConstraint(uci::base::accessorType::AccessorType type = uci::type::accessorType::anglePairType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by IngressConstraint */
         virtual void clearIngressConstraint()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EgressConstraint.
           *
           * @return The acecssor that provides access to the complex content that is identified by EgressConstraint.
           */
         virtual const uci::type::AnglePairType& getEgressConstraint() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EgressConstraint.
           *
           * @return The acecssor that provides access to the complex content that is identified by EgressConstraint.
           */
         virtual uci::type::AnglePairType& getEgressConstraint()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the EgressConstraint to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by EgressConstraint
           */
         virtual void setEgressConstraint(const uci::type::AnglePairType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by EgressConstraint exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by EgressConstraint is emabled or not
           */
         virtual bool hasEgressConstraint() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by EgressConstraint
           *
           * @param type = uci::type::accessorType::anglePairType This Accessor's accessor type
           */
         virtual void enableEgressConstraint(uci::base::accessorType::AccessorType type = uci::type::accessorType::anglePairType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by EgressConstraint */
         virtual void clearEgressConstraint()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the InitialPoint.
           *
           * @return The acecssor that provides access to the complex content that is identified by InitialPoint.
           */
         virtual const uci::type::Point3D_Type& getInitialPoint() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the InitialPoint.
           *
           * @return The acecssor that provides access to the complex content that is identified by InitialPoint.
           */
         virtual uci::type::Point3D_Type& getInitialPoint()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the InitialPoint to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by InitialPoint
           */
         virtual void setInitialPoint(const uci::type::Point3D_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by InitialPoint exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by InitialPoint is emabled or not
           */
         virtual bool hasInitialPoint() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by InitialPoint
           *
           * @param type = uci::type::accessorType::point3D_Type This Accessor's accessor type
           */
         virtual void enableInitialPoint(uci::base::accessorType::AccessorType type = uci::type::accessorType::point3D_Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by InitialPoint */
         virtual void clearInitialPoint()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the ReleaseConstraints.
           *
           * @return The acecssor that provides access to the complex content that is identified by ReleaseConstraints.
           */
         virtual const uci::type::StrikeTaskReleaseConstraintsType& getReleaseConstraints() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the ReleaseConstraints.
           *
           * @return The acecssor that provides access to the complex content that is identified by ReleaseConstraints.
           */
         virtual uci::type::StrikeTaskReleaseConstraintsType& getReleaseConstraints()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the ReleaseConstraints to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by ReleaseConstraints
           */
         virtual void setReleaseConstraints(const uci::type::StrikeTaskReleaseConstraintsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by ReleaseConstraints exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by ReleaseConstraints is emabled or not
           */
         virtual bool hasReleaseConstraints() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by ReleaseConstraints
           *
           * @param type = uci::type::accessorType::strikeTaskReleaseConstraintsType This Accessor's accessor type
           */
         virtual void enableReleaseConstraints(uci::base::accessorType::AccessorType type = uci::type::accessorType::strikeTaskReleaseConstraintsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by ReleaseConstraints */
         virtual void clearReleaseConstraints()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeaponList.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeaponList.
           */
         virtual const uci::type::StrikeTaskWeaponListType& getWeaponList() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeaponList.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeaponList.
           */
         virtual uci::type::StrikeTaskWeaponListType& getWeaponList()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WeaponList to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WeaponList
           */
         virtual void setWeaponList(const uci::type::StrikeTaskWeaponListType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by WeaponList exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by WeaponList is emabled or not
           */
         virtual bool hasWeaponList() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by WeaponList
           *
           * @param type = uci::type::accessorType::strikeTaskWeaponListType This Accessor's accessor type
           */
         virtual void enableWeaponList(uci::base::accessorType::AccessorType type = uci::type::accessorType::strikeTaskWeaponListType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by WeaponList */
         virtual void clearWeaponList()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EffectTime.
           *
           * @return The acecssor that provides access to the complex content that is identified by EffectTime.
           */
         virtual const uci::type::DateTimeRangeType& getEffectTime() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EffectTime.
           *
           * @return The acecssor that provides access to the complex content that is identified by EffectTime.
           */
         virtual uci::type::DateTimeRangeType& getEffectTime()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the EffectTime to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by EffectTime
           */
         virtual void setEffectTime(const uci::type::DateTimeRangeType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by EffectTime exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by EffectTime is emabled or not
           */
         virtual bool hasEffectTime() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by EffectTime
           *
           * @param type = uci::type::accessorType::dateTimeRangeType This Accessor's accessor type
           */
         virtual void enableEffectTime(uci::base::accessorType::AccessorType type = uci::type::accessorType::dateTimeRangeType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by EffectTime */
         virtual void clearEffectTime()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StrikeTaskType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StrikeTaskType to copy from
           */
         StrikeTaskType(const StrikeTaskType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StrikeTaskType to the contents of the StrikeTaskType on the right
           * hand side (rhs) of the assignment operator.StrikeTaskType [only available to derived classes]
           *
           * @param rhs The StrikeTaskType on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::StrikeTaskType
           * @return A constant reference to this StrikeTaskType.
           */
         const StrikeTaskType& operator=(const StrikeTaskType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StrikeTaskType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StrikeTaskType_h

