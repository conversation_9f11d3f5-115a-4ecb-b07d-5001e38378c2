// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StoreCarriageStatusType_h
#define Uci__Type__StoreCarriageStatusType_h 1

#if !defined(Uci__Type__StoreStatusType_h)
# include "uci/type/StoreStatusType.h"
#endif

#if !defined(Uci__Base__DoubleAccessor_h)
# include "uci/base/DoubleAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Possible store settings that could be used to id the state or extra data related to the attached Mission or Carriage
        * Store
        */
      class StoreCarriageStatusType : public virtual uci::type::StoreStatusType {
      public:

         /** The destructor */
         virtual ~StoreCarriageStatusType()
         { }

         /** Returns this accessor's type constant, i.e. StoreCarriageStatusType
           *
           * @return This accessor's type constant, i.e. StoreCarriageStatusType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::storeCarriageStatusType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StoreCarriageStatusType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Configuration.
           *
           * @return The value of the simple primitive data type identified by Configuration.
           */
         virtual xs::Double getConfiguration() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Configuration.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setConfiguration(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Configuration exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Configuration is emabled or not
           */
         virtual bool hasConfiguration() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Configuration
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableConfiguration(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Configuration */
         virtual void clearConfiguration()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StoreCarriageStatusType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StoreCarriageStatusType to copy from
           */
         StoreCarriageStatusType(const StoreCarriageStatusType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StoreCarriageStatusType to the contents of the
           * StoreCarriageStatusType on the right hand side (rhs) of the assignment operator.StoreCarriageStatusType [only
           * available to derived classes]
           *
           * @param rhs The StoreCarriageStatusType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::StoreCarriageStatusType
           * @return A constant reference to this StoreCarriageStatusType.
           */
         const StoreCarriageStatusType& operator=(const StoreCarriageStatusType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StoreCarriageStatusType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StoreCarriageStatusType_h

