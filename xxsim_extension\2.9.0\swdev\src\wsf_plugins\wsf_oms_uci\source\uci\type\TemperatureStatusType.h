// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TemperatureStatusType_h
#define Uci__Type__TemperatureStatusType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__TemperatureType_h)
# include "uci/type/TemperatureType.h"
#endif

#if !defined(Uci__Type__TemperatureStateEnum_h)
# include "uci/type/TemperatureStateEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TemperatureStatusType sequence accessor class */
      class TemperatureStatusType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TemperatureStatusType()
         { }

         /** Returns this accessor's type constant, i.e. TemperatureStatusType
           *
           * @return This accessor's type constant, i.e. TemperatureStatusType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::temperatureStatusType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TemperatureStatusType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Temperature.
           *
           * @return The value of the simple primitive data type identified by Temperature.
           */
         virtual uci::type::TemperatureTypeValue getTemperature() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Temperature.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setTemperature(uci::type::TemperatureTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Temperature exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Temperature is emabled or not
           */
         virtual bool hasTemperature() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Temperature
           *
           * @param type = uci::type::accessorType::temperatureType This Accessor's accessor type
           */
         virtual void enableTemperature(uci::base::accessorType::AccessorType type = uci::type::accessorType::temperatureType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Temperature */
         virtual void clearTemperature()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the TemperatureState.
           *
           * @return The value of the enumeration identified by TemperatureState.
           */
         virtual const uci::type::TemperatureStateEnum& getTemperatureState() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the TemperatureState.
           *
           * @return The value of the enumeration identified by TemperatureState.
           */
         virtual uci::type::TemperatureStateEnum& getTemperatureState()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the TemperatureState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setTemperatureState(const uci::type::TemperatureStateEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the TemperatureState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setTemperatureState(uci::type::TemperatureStateEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield TemperatureStateis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasTemperatureState() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getTemperatureState will return the optional field and not throw an exception
           * when invoked.
           *
           * @param type = uci::type::accessorType::temperatureStateEnum This Accessor's accessor type
           */
         virtual void enableTemperatureState(uci::base::accessorType::AccessorType type = uci::type::accessorType::temperatureStateEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearTemperatureState()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TemperatureStatusType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TemperatureStatusType to copy from
           */
         TemperatureStatusType(const TemperatureStatusType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TemperatureStatusType to the contents of the TemperatureStatusType
           * on the right hand side (rhs) of the assignment operator.TemperatureStatusType [only available to derived classes]
           *
           * @param rhs The TemperatureStatusType on the right hand side (rhs) of the assignment operator whose contents are used
           *      to set the contents of this uci::type::TemperatureStatusType
           * @return A constant reference to this TemperatureStatusType.
           */
         const TemperatureStatusType& operator=(const TemperatureStatusType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TemperatureStatusType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TemperatureStatusType_h

