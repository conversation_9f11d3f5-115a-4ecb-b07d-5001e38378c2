
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>export_library_dependencies &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="install_files" href="install_files.html" />
    <link rel="prev" title="exec_program" href="exec_program.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="install_files.html" title="install_files"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="exec_program.html" title="exec_program"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">export_library_dependencies</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="export-library-dependencies">
<span id="command:export_library_dependencies"></span><h1>export_library_dependencies<a class="headerlink" href="#export-library-dependencies" title="Permalink to this heading">¶</a></h1>
<p>Disallowed since version 3.0.  See CMake Policy <span class="target" id="index-0-policy:CMP0033"></span><a class="reference internal" href="../policy/CMP0033.html#policy:CMP0033" title="CMP0033"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0033</span></code></a>.</p>
<p>Use <span class="target" id="index-0-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a> or <span class="target" id="index-0-command:export"></span><a class="reference internal" href="export.html#command:export" title="export"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">export()</span></code></a> command.</p>
<p>This command generates an old-style library dependencies file.
Projects requiring CMake 2.6 or later should not use the command.  Use
instead the <span class="target" id="index-1-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a> command to help export targets from an
installation tree and the <span class="target" id="index-1-command:export"></span><a class="reference internal" href="export.html#command:export" title="export"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">export()</span></code></a> command to export targets from a
build tree.</p>
<p>The old-style library dependencies file does not take into account
per-configuration names of libraries or the
<span class="target" id="index-0-prop_tgt:LINK_INTERFACE_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/LINK_INTERFACE_LIBRARIES.html#prop_tgt:LINK_INTERFACE_LIBRARIES" title="LINK_INTERFACE_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_INTERFACE_LIBRARIES</span></code></a> target property.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">export_library_dependencies(</span><span class="nv">&lt;file&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">APPEND</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Create a file named <code class="docutils literal notranslate"><span class="pre">&lt;file&gt;</span></code> that can be included into a CMake listfile
with the INCLUDE command.  The file will contain a number of SET
commands that will set all the variables needed for library dependency
information.  This should be the last command in the top level
CMakeLists.txt file of the project.  If the <code class="docutils literal notranslate"><span class="pre">APPEND</span></code> option is
specified, the SET commands will be appended to the given file instead
of replacing it.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="exec_program.html"
                          title="previous chapter">exec_program</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="install_files.html"
                          title="next chapter">install_files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/export_library_dependencies.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="install_files.html" title="install_files"
             >next</a> |</li>
        <li class="right" >
          <a href="exec_program.html" title="exec_program"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">export_library_dependencies</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>