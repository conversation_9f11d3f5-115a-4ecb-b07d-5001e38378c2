# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************

/*
PURPOSE  Ingress behavior node  
AUTHOR   Snyder
Classification: UNCLASSIFIED//FOUO

Technical Description:

*/
advanced_behavior ingress

   script_debug_writes disable


   script_variables 
      extern string faz_desired;
      extern bool change_desired;
      extern WsfPlatform iacid;
      extern string reason;
      extern WsfCommandChain iflite;
      extern WsfCommand<PERSON>hain ielement;
      extern bool pitch_up;
      extern double pr_altitude;
      extern double pr_speed;
      extern double pr_heading;
      extern WsfSA_EntityPerception ppmjid;
      extern Atmosphere atmos;
      extern bool sam_threatnd;
      extern double rng_cls_sam;
      extern double rng_cls_hst;
      extern bool stiff_arm_all;
      extern WsfCommandChain FORM_FLY_CHAIN;
#      extern WsfBrawlerProcessor BRAWLER;
      extern double t_phase;
      extern Array<double> VECTOR_FORMATION;
      extern Array<int> PLAYBOOK;
   end_script_variables

   precondition 
      ////// Evaluate conditions that would prevent behavior alternative from running
      if (!PROCESSOR.IsA_TypeOf("WSF_SA_PROCESSOR"))
      {
         writeln_d("not a brawler processor!");
         return Failure("behavior not attached to a WSF_SA_PROCESSOR");
      } 

      if (PLATFORM->rule_type == "ftr" && PLATFORM->faz == "ingress")
      { return Success(); }
      else
      { return Failure(reason); }
 
   end_precondition
   
   execute 
      writeln_d("T = ",TIME_NOW," ",iacid.Name()," ingress");   
      bool BrawlMover;
      WsfBrawlerProcessor BRAWLER;
      if (iacid.Mover().Type() == "WSF_BRAWLER_MOVER")
      {
         for (int i=0; i<iacid.ProcessorCount();i+=1)
         {
            if (iacid.ProcessorEntry(i).Type() == "WSF_BRAWLER_PROCESSOR")
            {
               BrawlMover = true;
               BRAWLER = (WsfBrawlerProcessor)iacid.ProcessorEntry(i);
               break;
            }
         }
      }   
      int plan_1 = PLAYBOOK[0]; 
      int plan_2 = PLAYBOOK[1]; // create local version of these to manipulate in this routine
      int plan_3 = PLAYBOOK[2]; 
      faz_desired = "ingress"; // will change desired faz if criteria met
      reason = "INGRESSING";
      pr_heading = iacid.Heading();
      if (!ppmjid.IsNull() && ppmjid.Track().IsValid() && ppmjid.Track().BearingValid())
      {
         pr_heading = iacid.TrueBearingTo(ppmjid.Track());
      }
      pr_speed = iacid->COMMIT_SPD * atmos.SonicVelocity(iacid.Altitude());
      pr_altitude = iacid->COMMIT_ALT;

      // formation fly if PRDATA says to, else fly vector towards highest prioritized target
      if (iacid->MISSION_TYPE == "ESCORT")
      {
         pr_escort_formation(iacid);
      }
      else if (VECTOR_FORMATION[0] == 0)
      {
         extern double pr_gees;
         if (BrawlMover)
         {
            pr_gees = BRAWLER.MaxSustainedGs();
         }
         else
         {
            pr_gees = 3;
         }
         pr_vector(iacid,pr_heading,pr_speed,pr_altitude,pr_gees); 
      }
      else
      {
         pr_formation(iacid,pr_heading,pr_speed,pr_altitude,FORM_FLY_CHAIN);
      }
      
      // start faz transition logic
      if (rng_cls_hst <= iacid->COMMIT_RNG && !stiff_arm_all)
      {
         iacid.SetAuxData("IADS_engage",false);
         if ( plan_1 <= 3 )
         {  
            faz_desired = "direct"; 
         }
         else if ( plan_1 == 4 )
         {  
            faz_desired = "vectoring"; 
         }
         else 
         { 
            writeln("WARNING...PLAYBOOK 1 invalid");  
         }
         reason = " ".Join({"WITHIN COMMIT RANGE...PLAYBOOK[0]=",(string)plan_1});
         t_phase=TIME_NOW + 10;
      }
      else if (ppmjid.IsValid() && !ppmjid.IsNull() && ppmjid.Track().IsValid() && !ppmjid.Track().IsNull() && ppmjid.Track().LandDomain())
      {
         faz_desired = "sam_vctr";
         reason = "ENGAGING SAM";
         t_phase=TIME_NOW + 10;
      }
      return Success(reason);      
   end_execute

end_advanced_behavior
