// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StoreCarriageCapabilityType_h
#define Uci__Type__StoreCarriageCapabilityType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SupportCapabilityID_Type_h)
# include "uci/type/SupportCapabilityID_Type.h"
#endif

#if !defined(Uci__Type__StoreInformationBaseType_h)
# include "uci/type/StoreInformationBaseType.h"
#endif

#if !defined(Uci__Type__StoreVerificationStatusType_h)
# include "uci/type/StoreVerificationStatusType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This reports the Carriage Capability Type. */
      class StoreCarriageCapabilityType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~StoreCarriageCapabilityType()
         { }

         /** Returns this accessor's type constant, i.e. StoreCarriageCapabilityType
           *
           * @return This accessor's type constant, i.e. StoreCarriageCapabilityType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::storeCarriageCapabilityType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StoreCarriageCapabilityType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SupportCapabilityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SupportCapabilityID.
           */
         virtual const uci::type::SupportCapabilityID_Type& getSupportCapabilityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SupportCapabilityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SupportCapabilityID.
           */
         virtual uci::type::SupportCapabilityID_Type& getSupportCapabilityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SupportCapabilityID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SupportCapabilityID
           */
         virtual void setSupportCapabilityID(const uci::type::SupportCapabilityID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CarriageInformation.
           *
           * @return The acecssor that provides access to the complex content that is identified by CarriageInformation.
           */
         virtual const uci::type::StoreInformationBaseType& getCarriageInformation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CarriageInformation.
           *
           * @return The acecssor that provides access to the complex content that is identified by CarriageInformation.
           */
         virtual uci::type::StoreInformationBaseType& getCarriageInformation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the CarriageInformation to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by CarriageInformation
           */
         virtual void setCarriageInformation(const uci::type::StoreInformationBaseType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Verification.
           *
           * @return The acecssor that provides access to the complex content that is identified by Verification.
           */
         virtual const uci::type::StoreVerificationStatusType& getVerification() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Verification.
           *
           * @return The acecssor that provides access to the complex content that is identified by Verification.
           */
         virtual uci::type::StoreVerificationStatusType& getVerification()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Verification to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Verification
           */
         virtual void setVerification(const uci::type::StoreVerificationStatusType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Verification exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Verification is emabled or not
           */
         virtual bool hasVerification() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Verification
           *
           * @param type = uci::type::accessorType::storeVerificationStatusType This Accessor's accessor type
           */
         virtual void enableVerification(uci::base::accessorType::AccessorType type = uci::type::accessorType::storeVerificationStatusType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Verification */
         virtual void clearVerification()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StoreCarriageCapabilityType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StoreCarriageCapabilityType to copy from
           */
         StoreCarriageCapabilityType(const StoreCarriageCapabilityType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StoreCarriageCapabilityType to the contents of the
           * StoreCarriageCapabilityType on the right hand side (rhs) of the assignment operator.StoreCarriageCapabilityType [only
           * available to derived classes]
           *
           * @param rhs The StoreCarriageCapabilityType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::StoreCarriageCapabilityType
           * @return A constant reference to this StoreCarriageCapabilityType.
           */
         const StoreCarriageCapabilityType& operator=(const StoreCarriageCapabilityType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StoreCarriageCapabilityType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StoreCarriageCapabilityType_h

