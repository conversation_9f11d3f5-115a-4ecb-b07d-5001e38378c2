// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__ThresholdVulnerabilityType_h
#define Uci__Type__ThresholdVulnerabilityType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__VulnerabilityLevelsCombinedType_h)
# include "uci/type/VulnerabilityLevelsCombinedType.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the ThresholdVulnerabilityType sequence accessor class */
      class ThresholdVulnerabilityType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~ThresholdVulnerabilityType()
         { }

         /** Returns this accessor's type constant, i.e. ThresholdVulnerabilityType
           *
           * @return This accessor's type constant, i.e. ThresholdVulnerabilityType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::thresholdVulnerabilityType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const ThresholdVulnerabilityType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the
           * VulnerabilityWithoutSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by
           *      VulnerabilityWithoutSuppression.
           */
         virtual const uci::type::VulnerabilityLevelsCombinedType& getVulnerabilityWithoutSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the
           * VulnerabilityWithoutSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by
           *      VulnerabilityWithoutSuppression.
           */
         virtual uci::type::VulnerabilityLevelsCombinedType& getVulnerabilityWithoutSuppression()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the VulnerabilityWithoutSuppression to the contents of the complex
           * content that is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by VulnerabilityWithoutSuppression
           */
         virtual void setVulnerabilityWithoutSuppression(const uci::type::VulnerabilityLevelsCombinedType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VulnerabilityWithoutSuppression exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VulnerabilityWithoutSuppression is emabled or not
           */
         virtual bool hasVulnerabilityWithoutSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VulnerabilityWithoutSuppression
           *
           * @param type = uci::type::accessorType::vulnerabilityLevelsCombinedType This Accessor's accessor type
           */
         virtual void enableVulnerabilityWithoutSuppression(uci::base::accessorType::AccessorType type = uci::type::accessorType::vulnerabilityLevelsCombinedType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VulnerabilityWithoutSuppression */
         virtual void clearVulnerabilityWithoutSuppression()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the
           * VulnerabilityWithSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by VulnerabilityWithSuppression.
           */
         virtual const uci::type::VulnerabilityLevelsCombinedType& getVulnerabilityWithSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the
           * VulnerabilityWithSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by VulnerabilityWithSuppression.
           */
         virtual uci::type::VulnerabilityLevelsCombinedType& getVulnerabilityWithSuppression()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the VulnerabilityWithSuppression to the contents of the complex
           * content that is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by VulnerabilityWithSuppression
           */
         virtual void setVulnerabilityWithSuppression(const uci::type::VulnerabilityLevelsCombinedType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VulnerabilityWithSuppression exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VulnerabilityWithSuppression is emabled or not
           */
         virtual bool hasVulnerabilityWithSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VulnerabilityWithSuppression
           *
           * @param type = uci::type::accessorType::vulnerabilityLevelsCombinedType This Accessor's accessor type
           */
         virtual void enableVulnerabilityWithSuppression(uci::base::accessorType::AccessorType type = uci::type::accessorType::vulnerabilityLevelsCombinedType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VulnerabilityWithSuppression */
         virtual void clearVulnerabilityWithSuppression()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the ExposureEventProbabilityThreshold.
           *
           * @return The value of the simple primitive data type identified by ExposureEventProbabilityThreshold.
           */
         virtual uci::type::PercentTypeValue getExposureEventProbabilityThreshold() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the ExposureEventProbabilityThreshold.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setExposureEventProbabilityThreshold(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by ExposureEventProbabilityThreshold exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by ExposureEventProbabilityThreshold is emabled or not
           */
         virtual bool hasExposureEventProbabilityThreshold() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by ExposureEventProbabilityThreshold
           *
           * @param type = uci::type::accessorType::percentType This Accessor's accessor type
           */
         virtual void enableExposureEventProbabilityThreshold(uci::base::accessorType::AccessorType type = uci::type::accessorType::percentType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by ExposureEventProbabilityThreshold */
         virtual void clearExposureEventProbabilityThreshold()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         ThresholdVulnerabilityType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The ThresholdVulnerabilityType to copy from
           */
         ThresholdVulnerabilityType(const ThresholdVulnerabilityType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this ThresholdVulnerabilityType to the contents of the
           * ThresholdVulnerabilityType on the right hand side (rhs) of the assignment operator.ThresholdVulnerabilityType [only
           * available to derived classes]
           *
           * @param rhs The ThresholdVulnerabilityType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::ThresholdVulnerabilityType
           * @return A constant reference to this ThresholdVulnerabilityType.
           */
         const ThresholdVulnerabilityType& operator=(const ThresholdVulnerabilityType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // ThresholdVulnerabilityType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__ThresholdVulnerabilityType_h

