// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TimeErrorType_h
#define Uci__Type__TimeErrorType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__DurationType_h)
# include "uci/type/DurationType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TimeErrorType sequence accessor class */
      class TimeErrorType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TimeErrorType()
         { }

         /** Returns this accessor's type constant, i.e. TimeErrorType
           *
           * @return This accessor's type constant, i.e. TimeErrorType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::timeErrorType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TimeErrorType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Early.
           *
           * @return The value of the simple primitive data type identified by Early.
           */
         virtual uci::type::DurationTypeValue getEarly() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Early.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setEarly(uci::type::DurationTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Early exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Early is emabled or not
           */
         virtual bool hasEarly() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Early
           *
           * @param type = uci::type::accessorType::durationType This Accessor's accessor type
           */
         virtual void enableEarly(uci::base::accessorType::AccessorType type = uci::type::accessorType::durationType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Early */
         virtual void clearEarly()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Late.
           *
           * @return The value of the simple primitive data type identified by Late.
           */
         virtual uci::type::DurationTypeValue getLate() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Late.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setLate(uci::type::DurationTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Late exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Late is emabled or not
           */
         virtual bool hasLate() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Late
           *
           * @param type = uci::type::accessorType::durationType This Accessor's accessor type
           */
         virtual void enableLate(uci::base::accessorType::AccessorType type = uci::type::accessorType::durationType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Late */
         virtual void clearLate()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TimeErrorType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TimeErrorType to copy from
           */
         TimeErrorType(const TimeErrorType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TimeErrorType to the contents of the TimeErrorType on the right
           * hand side (rhs) of the assignment operator.TimeErrorType [only available to derived classes]
           *
           * @param rhs The TimeErrorType on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::TimeErrorType
           * @return A constant reference to this TimeErrorType.
           */
         const TimeErrorType& operator=(const TimeErrorType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TimeErrorType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TimeErrorType_h

