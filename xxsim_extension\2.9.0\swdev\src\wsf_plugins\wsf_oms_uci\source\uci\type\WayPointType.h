// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WayPointType_h
#define Uci__Type__WayPointType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__Point2D_Type_h)
# include "uci/type/Point2D_Type.h"
#endif

#if !defined(Uci__Type__WaypointTypeEnum_h)
# include "uci/type/WaypointTypeEnum.h"
#endif

#if !defined(Uci__Type__DMPI_ID_Type_h)
# include "uci/type/DMPI_ID_Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WayPointType sequence accessor class */
      class WayPointType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WayPointType()
         { }

         /** Returns this accessor's type constant, i.e. WayPointType
           *
           * @return This accessor's type constant, i.e. WayPointType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::wayPointType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WayPointType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Point2D.
           *
           * @return The acecssor that provides access to the complex content that is identified by Point2D.
           */
         virtual const uci::type::Point2D_Type& getPoint2D() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Point2D.
           *
           * @return The acecssor that provides access to the complex content that is identified by Point2D.
           */
         virtual uci::type::Point2D_Type& getPoint2D()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Point2D to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Point2D
           */
         virtual void setPoint2D(const uci::type::Point2D_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the WaypointType.
           *
           * @return The value of the enumeration identified by WaypointType.
           */
         virtual const uci::type::WaypointTypeEnum& getWaypointType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the WaypointType.
           *
           * @return The value of the enumeration identified by WaypointType.
           */
         virtual uci::type::WaypointTypeEnum& getWaypointType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the WaypointType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWaypointType(const uci::type::WaypointTypeEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the WaypointType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWaypointType(uci::type::WaypointTypeEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the DMPI_PointID.
           *
           * @return The acecssor that provides access to the complex content that is identified by DMPI_PointID.
           */
         virtual const uci::type::DMPI_ID_Type& getDMPI_PointID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the DMPI_PointID.
           *
           * @return The acecssor that provides access to the complex content that is identified by DMPI_PointID.
           */
         virtual uci::type::DMPI_ID_Type& getDMPI_PointID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the DMPI_PointID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by DMPI_PointID
           */
         virtual void setDMPI_PointID(const uci::type::DMPI_ID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by DMPI_PointID exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by DMPI_PointID is emabled or not
           */
         virtual bool hasDMPI_PointID() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by DMPI_PointID
           *
           * @param type = uci::type::accessorType::dMPI_ID_Type This Accessor's accessor type
           */
         virtual void enableDMPI_PointID(uci::base::accessorType::AccessorType type = uci::type::accessorType::dMPI_ID_Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by DMPI_PointID */
         virtual void clearDMPI_PointID()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WayPointType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WayPointType to copy from
           */
         WayPointType(const WayPointType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WayPointType to the contents of the WayPointType on the right hand
           * side (rhs) of the assignment operator.WayPointType [only available to derived classes]
           *
           * @param rhs The WayPointType on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::WayPointType
           * @return A constant reference to this WayPointType.
           */
         const WayPointType& operator=(const WayPointType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WayPointType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WayPointType_h

