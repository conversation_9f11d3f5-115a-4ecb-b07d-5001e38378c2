// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TimeCoverageType_h
#define Uci__Type__TimeCoverageType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__DateTimeRangeType_h)
# include "uci/type/DateTimeRangeType.h"
#endif

#if !defined(Uci__Type__ZoneType_h)
# include "uci/type/ZoneType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TimeCoverageType sequence accessor class */
      class TimeCoverageType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TimeCoverageType()
         { }

         /** Returns this accessor's type constant, i.e. TimeCoverageType
           *
           * @return This accessor's type constant, i.e. TimeCoverageType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::timeCoverageType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TimeCoverageType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TimeSpan.
           *
           * @return The acecssor that provides access to the complex content that is identified by TimeSpan.
           */
         virtual const uci::type::DateTimeRangeType& getTimeSpan() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TimeSpan.
           *
           * @return The acecssor that provides access to the complex content that is identified by TimeSpan.
           */
         virtual uci::type::DateTimeRangeType& getTimeSpan()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TimeSpan to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TimeSpan
           */
         virtual void setTimeSpan(const uci::type::DateTimeRangeType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CoverageArea.
           *
           * @return The acecssor that provides access to the complex content that is identified by CoverageArea.
           */
         virtual const uci::type::ZoneType& getCoverageArea() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CoverageArea.
           *
           * @return The acecssor that provides access to the complex content that is identified by CoverageArea.
           */
         virtual uci::type::ZoneType& getCoverageArea()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the CoverageArea to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by CoverageArea
           */
         virtual void setCoverageArea(const uci::type::ZoneType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by CoverageArea exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by CoverageArea is emabled or not
           */
         virtual bool hasCoverageArea() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by CoverageArea
           *
           * @param type = uci::type::accessorType::zoneType This Accessor's accessor type
           */
         virtual void enableCoverageArea(uci::base::accessorType::AccessorType type = uci::type::accessorType::zoneType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by CoverageArea */
         virtual void clearCoverageArea()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TimeCoverageType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TimeCoverageType to copy from
           */
         TimeCoverageType(const TimeCoverageType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TimeCoverageType to the contents of the TimeCoverageType on the
           * right hand side (rhs) of the assignment operator.TimeCoverageType [only available to derived classes]
           *
           * @param rhs The TimeCoverageType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::TimeCoverageType
           * @return A constant reference to this TimeCoverageType.
           */
         const TimeCoverageType& operator=(const TimeCoverageType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TimeCoverageType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TimeCoverageType_h

