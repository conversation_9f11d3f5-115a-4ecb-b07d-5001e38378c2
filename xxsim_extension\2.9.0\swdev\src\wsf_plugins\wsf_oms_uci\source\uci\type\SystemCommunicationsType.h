// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SystemCommunicationsType_h
#define Uci__Type__SystemCommunicationsType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__MissionCommunicationsStateEnum_h)
# include "uci/type/MissionCommunicationsStateEnum.h"
#endif

#if !defined(Uci__Type__DurationType_h)
# include "uci/type/DurationType.h"
#endif

#if !defined(Uci__Type__DateTimeType_h)
# include "uci/type/DateTimeType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__CommSystemUsageType_h)
# include "uci/type/CommSystemUsageType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SystemCommunicationsType sequence accessor class */
      class SystemCommunicationsType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SystemCommunicationsType()
         { }

         /** Returns this accessor's type constant, i.e. SystemCommunicationsType
           *
           * @return This accessor's type constant, i.e. SystemCommunicationsType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::systemCommunicationsType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SystemCommunicationsType& accessor)
            throw(uci::base::UCIException) = 0;


         /** This element represents a specific communication system that this system utilizes for operations. This information is
           * used to ensure sufficient communications have been acquired and are ready to be used in addition to de-conflicting
           * with other systems which may utilize the same communications system. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::CommSystemUsageType, uci::type::accessorType::commSystemUsageType> CommUsage;

         /** Returns the value of the enumeration that is identified by the MissionCommunicationsState.
           *
           * @return The value of the enumeration identified by MissionCommunicationsState.
           */
         virtual const uci::type::MissionCommunicationsStateEnum& getMissionCommunicationsState() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the MissionCommunicationsState.
           *
           * @return The value of the enumeration identified by MissionCommunicationsState.
           */
         virtual uci::type::MissionCommunicationsStateEnum& getMissionCommunicationsState()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the MissionCommunicationsState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setMissionCommunicationsState(const uci::type::MissionCommunicationsStateEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the MissionCommunicationsState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setMissionCommunicationsState(uci::type::MissionCommunicationsStateEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the CommTimeout.
           *
           * @return The value of the simple primitive data type identified by CommTimeout.
           */
         virtual uci::type::DurationTypeValue getCommTimeout() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the CommTimeout.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setCommTimeout(uci::type::DurationTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by CommTimeout exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by CommTimeout is emabled or not
           */
         virtual bool hasCommTimeout() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by CommTimeout
           *
           * @param type = uci::type::accessorType::durationType This Accessor's accessor type
           */
         virtual void enableCommTimeout(uci::base::accessorType::AccessorType type = uci::type::accessorType::durationType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by CommTimeout */
         virtual void clearCommTimeout()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the PrePlannedCommReturnTime.
           *
           * @return The value of the simple primitive data type identified by PrePlannedCommReturnTime.
           */
         virtual uci::type::DateTimeTypeValue getPrePlannedCommReturnTime() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the PrePlannedCommReturnTime.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setPrePlannedCommReturnTime(uci::type::DateTimeTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by PrePlannedCommReturnTime exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by PrePlannedCommReturnTime is emabled or not
           */
         virtual bool hasPrePlannedCommReturnTime() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by PrePlannedCommReturnTime
           *
           * @param type = uci::type::accessorType::dateTimeType This Accessor's accessor type
           */
         virtual void enablePrePlannedCommReturnTime(uci::base::accessorType::AccessorType type = uci::type::accessorType::dateTimeType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by PrePlannedCommReturnTime */
         virtual void clearPrePlannedCommReturnTime()
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CommUsage.
           *
           * @return The bounded list identified by CommUsage.
           */
         virtual const uci::type::SystemCommunicationsType::CommUsage& getCommUsage() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CommUsage.
           *
           * @return The bounded list identified by CommUsage.
           */
         virtual uci::type::SystemCommunicationsType::CommUsage& getCommUsage()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the CommUsage.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCommUsage(const uci::type::SystemCommunicationsType::CommUsage& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SystemCommunicationsType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SystemCommunicationsType to copy from
           */
         SystemCommunicationsType(const SystemCommunicationsType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SystemCommunicationsType to the contents of the
           * SystemCommunicationsType on the right hand side (rhs) of the assignment operator.SystemCommunicationsType [only
           * available to derived classes]
           *
           * @param rhs The SystemCommunicationsType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SystemCommunicationsType
           * @return A constant reference to this SystemCommunicationsType.
           */
         const SystemCommunicationsType& operator=(const SystemCommunicationsType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SystemCommunicationsType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SystemCommunicationsType_h

