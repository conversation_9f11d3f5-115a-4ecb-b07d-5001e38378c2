
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>install &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="link_directories" href="link_directories.html" />
    <link rel="prev" title="include_regular_expression" href="include_regular_expression.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="link_directories.html" title="link_directories"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="include_regular_expression.html" title="include_regular_expression"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">install</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="install">
<span id="command:install"></span><h1>install<a class="headerlink" href="#install" title="Permalink to this heading">¶</a></h1>
<p>Specify rules to run at install time.</p>
<section id="synopsis">
<h2>Synopsis<a class="headerlink" href="#synopsis" title="Permalink to this heading">¶</a></h2>
<pre class="literal-block">install(<a class="reference internal" href="#targets">TARGETS</a> &lt;target&gt;... [...])
install(<a class="reference internal" href="#imported-runtime-artifacts">IMPORTED_RUNTIME_ARTIFACTS</a> &lt;target&gt;... [...])
install({<a class="reference internal" href="#files">FILES</a> | <a class="reference internal" href="#programs">PROGRAMS</a>} &lt;file&gt;... [...])
install(<a class="reference internal" href="#directory">DIRECTORY</a> &lt;dir&gt;... [...])
install(<a class="reference internal" href="#script">SCRIPT</a> &lt;file&gt; [...])
install(<a class="reference internal" href="#code">CODE</a> &lt;code&gt; [...])
install(<a class="reference internal" href="#export">EXPORT</a> &lt;export-name&gt; [...])
install(<a class="reference internal" href="#runtime-dependency-set">RUNTIME_DEPENDENCY_SET</a> &lt;set-name&gt; [...])</pre>
</section>
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Permalink to this heading">¶</a></h2>
<p>This command generates installation rules for a project.  Install rules
specified by calls to the <code class="docutils literal notranslate"><span class="pre">install()</span></code> command within a source directory
are executed in order during installation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.14: </span>Install rules in subdirectories
added by calls to the <span class="target" id="index-0-command:add_subdirectory"></span><a class="reference internal" href="add_subdirectory.html#command:add_subdirectory" title="add_subdirectory"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_subdirectory()</span></code></a> command are interleaved
with those in the parent directory to run in the order declared (see
policy <span class="target" id="index-0-policy:CMP0082"></span><a class="reference internal" href="../policy/CMP0082.html#policy:CMP0082" title="CMP0082"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0082</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.22: </span>The environment variable <span class="target" id="index-0-envvar:CMAKE_INSTALL_MODE"></span><a class="reference internal" href="../envvar/CMAKE_INSTALL_MODE.html#envvar:CMAKE_INSTALL_MODE" title="CMAKE_INSTALL_MODE"><code class="xref cmake cmake-envvar docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MODE</span></code></a> can override the
default copying behavior of <code class="docutils literal notranslate"><span class="pre">install()</span></code>.</p>
</div>
<p>There are multiple signatures for this command.  Some of them define
installation options for files and targets.  Options common to
multiple signatures are covered here but they are valid only for
signatures that specify them.  The common options are:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code></dt><dd><p>Specify the directory on disk to which a file will be installed.
Arguments can be relative or absolute paths.</p>
<p>If a relative path is given it is interpreted relative to the value
of the <span class="target" id="index-0-variable:CMAKE_INSTALL_PREFIX"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_PREFIX.html#variable:CMAKE_INSTALL_PREFIX" title="CMAKE_INSTALL_PREFIX"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_PREFIX</span></code></a> variable.
The prefix can be relocated at install time using the <code class="docutils literal notranslate"><span class="pre">DESTDIR</span></code>
mechanism explained in the <span class="target" id="index-1-variable:CMAKE_INSTALL_PREFIX"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_PREFIX.html#variable:CMAKE_INSTALL_PREFIX" title="CMAKE_INSTALL_PREFIX"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_PREFIX</span></code></a> variable
documentation.</p>
<p>If an absolute path (with a leading slash or drive letter) is given
it is used verbatim.</p>
<p>As absolute paths are not supported by <span class="target" id="index-0-manual:cpack(1)"></span><a class="reference internal" href="../manual/cpack.1.html#manual:cpack(1)" title="cpack(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cpack</span></code></a> installer
generators, it is preferable to use relative paths throughout.
In particular, there is no need to make paths absolute by prepending
<span class="target" id="index-2-variable:CMAKE_INSTALL_PREFIX"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_PREFIX.html#variable:CMAKE_INSTALL_PREFIX" title="CMAKE_INSTALL_PREFIX"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_PREFIX</span></code></a>; this prefix is used by default if
the DESTINATION is a relative path.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PERMISSIONS</span></code></dt><dd><p>Specify permissions for installed files.  Valid permissions are
<code class="docutils literal notranslate"><span class="pre">OWNER_READ</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNER_WRITE</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNER_EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">GROUP_READ</span></code>,
<code class="docutils literal notranslate"><span class="pre">GROUP_WRITE</span></code>, <code class="docutils literal notranslate"><span class="pre">GROUP_EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">WORLD_READ</span></code>, <code class="docutils literal notranslate"><span class="pre">WORLD_WRITE</span></code>,
<code class="docutils literal notranslate"><span class="pre">WORLD_EXECUTE</span></code>, <code class="docutils literal notranslate"><span class="pre">SETUID</span></code>, and <code class="docutils literal notranslate"><span class="pre">SETGID</span></code>.  Permissions that do
not make sense on certain platforms are ignored on those platforms.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CONFIGURATIONS</span></code></dt><dd><p>Specify a list of build configurations for which the install rule
applies (Debug, Release, etc.). Note that the values specified for
this option only apply to options listed AFTER the <code class="docutils literal notranslate"><span class="pre">CONFIGURATIONS</span></code>
option. For example, to set separate install paths for the Debug and
Release configurations, do the following:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">target</span><span class="w"></span>
<span class="w">        </span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="nb">Debug</span><span class="w"></span>
<span class="w">        </span><span class="no">RUNTIME</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">Debug/bin</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">target</span><span class="w"></span>
<span class="w">        </span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="nb">Release</span><span class="w"></span>
<span class="w">        </span><span class="no">RUNTIME</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">Release/bin</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Note that <code class="docutils literal notranslate"><span class="pre">CONFIGURATIONS</span></code> appears BEFORE <code class="docutils literal notranslate"><span class="pre">RUNTIME</span> <span class="pre">DESTINATION</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code></dt><dd><p>Specify an installation component name with which the install rule
is associated, such as <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> or <code class="docutils literal notranslate"><span class="pre">Development</span></code>.  During
component-specific installation only install rules associated with
the given component name will be executed.  During a full installation
all components are installed unless marked with <code class="docutils literal notranslate"><span class="pre">EXCLUDE_FROM_ALL</span></code>.
If <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> is not provided a default component &quot;Unspecified&quot; is
created.  The default component name may be controlled with the
<span class="target" id="index-0-variable:CMAKE_INSTALL_DEFAULT_COMPONENT_NAME"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_DEFAULT_COMPONENT_NAME.html#variable:CMAKE_INSTALL_DEFAULT_COMPONENT_NAME" title="CMAKE_INSTALL_DEFAULT_COMPONENT_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_DEFAULT_COMPONENT_NAME</span></code></a> variable.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXCLUDE_FROM_ALL</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>Specify that the file is excluded from a full installation and only
installed as part of a component-specific installation</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RENAME</span></code></dt><dd><p>Specify a name for an installed file that may be different from the
original file.  Renaming is allowed only when a single file is
installed by the command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OPTIONAL</span></code></dt><dd><p>Specify that it is not an error if the file to be installed does
not exist.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>Command signatures that install files may print messages during
installation.  Use the <span class="target" id="index-0-variable:CMAKE_INSTALL_MESSAGE"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_MESSAGE.html#variable:CMAKE_INSTALL_MESSAGE" title="CMAKE_INSTALL_MESSAGE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MESSAGE</span></code></a> variable
to control which messages are printed.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>Many of the <code class="docutils literal notranslate"><span class="pre">install()</span></code> variants implicitly create the directories
containing the installed files. If
<span class="target" id="index-0-variable:CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS.html#variable:CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS" title="CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS</span></code></a> is set, these
directories will be created with the permissions specified. Otherwise,
they will be created according to the uname rules on Unix-like platforms.
Windows platforms are unaffected.</p>
</div>
</section>
<section id="installing-targets">
<h2>Installing Targets<a class="headerlink" href="#installing-targets" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate" id="targets"><span id="install-targets"></span><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">targets...</span><span class="w"> </span><span class="p">[</span><span class="no">EXPORT</span><span class="w"> </span><span class="nv">&lt;export-name&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">RUNTIME_DEPENDENCIES</span><span class="w"> </span><span class="nb">args...</span><span class="p">|</span><span class="no">RUNTIME_DEPENDENCY_SET</span><span class="w"> </span><span class="nv">&lt;set-name&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[[</span><span class="no">ARCHIVE</span><span class="p">|</span><span class="no">LIBRARY</span><span class="p">|</span><span class="no">RUNTIME</span><span class="p">|</span><span class="no">OBJECTS</span><span class="p">|</span><span class="no">FRAMEWORK</span><span class="p">|</span><span class="no">BUNDLE</span><span class="p">|</span><span class="w"></span>
<span class="w">          </span><span class="no">PRIVATE_HEADER</span><span class="p">|</span><span class="no">PUBLIC_HEADER</span><span class="p">|</span><span class="no">RESOURCE</span><span class="p">|</span><span class="no">FILE_SET</span><span class="w"> </span><span class="nv">&lt;set-name&gt;</span><span class="p">|</span><span class="no">CXX_MODULES_BMI</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="p">[</span><span class="nb">Debug</span><span class="p">|</span><span class="nb">Release</span><span class="p">|...]]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">NAMELINK_COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">OPTIONAL</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">NAMELINK_ONLY</span><span class="p">|</span><span class="no">NAMELINK_SKIP</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">]</span><span class="w"> </span><span class="p">[...]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">INCLUDES</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="p">...]]</span><span class="w"></span>
<span class="w">        </span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">TARGETS</span></code> form specifies rules for installing targets from a
project.  There are several kinds of target <a class="reference internal" href="../manual/cmake-buildsystem.7.html#output-artifacts"><span class="std std-ref">Output Artifacts</span></a>
that may be installed:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code></dt><dd><p>Target artifacts of this kind include:</p>
<ul class="simple">
<li><p><em>Static libraries</em>
(except on macOS when marked as <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>, see below);</p></li>
<li><p><em>DLL import libraries</em>
(on all Windows-based systems including Cygwin; they have extension
<code class="docutils literal notranslate"><span class="pre">.lib</span></code>, in contrast to the <code class="docutils literal notranslate"><span class="pre">.dll</span></code> libraries that go to <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code>);</p></li>
<li><p>On AIX, the <em>linker import file</em> created for executables with
<span class="target" id="index-0-prop_tgt:ENABLE_EXPORTS"></span><a class="reference internal" href="../prop_tgt/ENABLE_EXPORTS.html#prop_tgt:ENABLE_EXPORTS" title="ENABLE_EXPORTS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ENABLE_EXPORTS</span></code></a> enabled.</p></li>
</ul>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code></dt><dd><p>Target artifacts of this kind include:</p>
<ul class="simple">
<li><p><em>Shared libraries</em>, except</p>
<ul>
<li><p>DLLs (these go to <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code>, see below),</p></li>
<li><p>on macOS when marked as <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> (see below).</p></li>
</ul>
</li>
</ul>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code></dt><dd><p>Target artifacts of this kind include:</p>
<ul class="simple">
<li><p><em>Executables</em>
(except on macOS when marked as <code class="docutils literal notranslate"><span class="pre">MACOSX_BUNDLE</span></code>, see <code class="docutils literal notranslate"><span class="pre">BUNDLE</span></code> below);</p></li>
<li><p>DLLs (on all Windows-based systems including Cygwin; note that the
accompanying import libraries are of kind <code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code>).</p></li>
</ul>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OBJECTS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Object files associated with <em>object libraries</em>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code></dt><dd><p>Both static and shared libraries marked with the <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>
property are treated as <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> targets on macOS.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">BUNDLE</span></code></dt><dd><p>Executables marked with the <span class="target" id="index-0-prop_tgt:MACOSX_BUNDLE"></span><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE.html#prop_tgt:MACOSX_BUNDLE" title="MACOSX_BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MACOSX_BUNDLE</span></code></a> property are treated as
<code class="docutils literal notranslate"><span class="pre">BUNDLE</span></code> targets on macOS.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code></dt><dd><p>Any <span class="target" id="index-0-prop_tgt:PUBLIC_HEADER"></span><a class="reference internal" href="../prop_tgt/PUBLIC_HEADER.html#prop_tgt:PUBLIC_HEADER" title="PUBLIC_HEADER"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code></a> files associated with a library are installed in
the destination specified by the <code class="docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code> argument on non-Apple
platforms. Rules defined by this argument are ignored for <span class="target" id="index-0-prop_tgt:FRAMEWORK"></span><a class="reference internal" href="../prop_tgt/FRAMEWORK.html#prop_tgt:FRAMEWORK" title="FRAMEWORK"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">FRAMEWORK</span></code></a>
libraries on Apple platforms because the associated files are installed
into the appropriate locations inside the framework folder. See
<span class="target" id="index-1-prop_tgt:PUBLIC_HEADER"></span><a class="reference internal" href="../prop_tgt/PUBLIC_HEADER.html#prop_tgt:PUBLIC_HEADER" title="PUBLIC_HEADER"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code></a> for details.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PRIVATE_HEADER</span></code></dt><dd><p>Similar to <code class="docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code>, but for <code class="docutils literal notranslate"><span class="pre">PRIVATE_HEADER</span></code> files. See
<span class="target" id="index-0-prop_tgt:PRIVATE_HEADER"></span><a class="reference internal" href="../prop_tgt/PRIVATE_HEADER.html#prop_tgt:PRIVATE_HEADER" title="PRIVATE_HEADER"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PRIVATE_HEADER</span></code></a> for details.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RESOURCE</span></code></dt><dd><p>Similar to <code class="docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code> and <code class="docutils literal notranslate"><span class="pre">PRIVATE_HEADER</span></code>, but for
<code class="docutils literal notranslate"><span class="pre">RESOURCE</span></code> files. See <span class="target" id="index-0-prop_tgt:RESOURCE"></span><a class="reference internal" href="../prop_tgt/RESOURCE.html#prop_tgt:RESOURCE" title="RESOURCE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">RESOURCE</span></code></a> for details.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FILE_SET</span> <span class="pre">&lt;set&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>File sets are defined by the <span class="target" id="index-0-command:target_sources"></span><a class="reference internal" href="target_sources.html#command:target_sources" title="target_sources"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_sources(FILE_SET)</span></code></a> command.
If the file set <code class="docutils literal notranslate"><span class="pre">&lt;set&gt;</span></code> exists and is <code class="docutils literal notranslate"><span class="pre">PUBLIC</span></code> or <code class="docutils literal notranslate"><span class="pre">INTERFACE</span></code>, any
files in the set are installed under the destination (see below).
The directory structure relative to the file set's base directories is
preserved. For example, a file added to the file set as
<code class="docutils literal notranslate"><span class="pre">/blah/include/myproj/here.h</span></code> with a base directory <code class="docutils literal notranslate"><span class="pre">/blah/include</span></code>
would be installed to <code class="docutils literal notranslate"><span class="pre">myproj/here.h</span></code> below the destination.</p>
</dd>
</dl>
<p><code class="docutils literal notranslate"><span class="pre">CXX_MODULES_BMI</span></code></p>
<blockquote>
<div><div class="admonition note">
<p class="admonition-title">Note</p>
<p>Experimental. Gated by <code class="docutils literal notranslate"><span class="pre">CMAKE_EXPERIMENTAL_CXX_MODULE_CMAKE_API</span></code></p>
</div>
<p>Any module files from C++ modules from <code class="docutils literal notranslate"><span class="pre">PUBLIC</span></code> sources in a file set of
type <code class="docutils literal notranslate"><span class="pre">CXX_MODULES</span></code> will be installed to the given <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>. All
modules are placed directly in the destination as no directory structure is
derived from the names of the modules. An empty <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> may be used
to suppress installing these files (for use in generic code).</p>
</div></blockquote>
<p>For each of these arguments given, the arguments following them only apply
to the target or file type specified in the argument. If none is given, the
installation properties apply to all target types.</p>
<p>For regular executables, static libraries and shared libraries, the
<code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> argument is not required.  For these target types, when
<code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> is omitted, a default destination will be taken from the
appropriate variable from <span class="target" id="index-0-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a>, or set to a built-in
default value if that variable is not defined.  The same is true for file
sets, and the public and private headers associated with the installed
targets through the <span class="target" id="index-2-prop_tgt:PUBLIC_HEADER"></span><a class="reference internal" href="../prop_tgt/PUBLIC_HEADER.html#prop_tgt:PUBLIC_HEADER" title="PUBLIC_HEADER"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code></a> and <span class="target" id="index-1-prop_tgt:PRIVATE_HEADER"></span><a class="reference internal" href="../prop_tgt/PRIVATE_HEADER.html#prop_tgt:PRIVATE_HEADER" title="PRIVATE_HEADER"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PRIVATE_HEADER</span></code></a>
target properties. A destination must always be provided for module libraries,
Apple bundles and frameworks.  A destination can be omitted for interface and
object libraries, but they are handled differently (see the discussion of this
topic toward the end of this section).</p>
<p>For shared libraries on DLL platforms, if neither <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code> nor <code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code>
destinations are specified, both the <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code> and <code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code> components are
installed to their default destinations. If either a <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code> or <code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code>
destination is specified, the component is installed to that destination, and
the other component is not installed. If both <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code> and <code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code>
destinations are specified, then both components are installed to their
respective destinations.</p>
<p>The following table shows the target types with their associated variables and
built-in defaults that apply when no destination is given:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Target Type</p></th>
<th class="head"><p>GNUInstallDirs Variable</p></th>
<th class="head"><p>Built-In Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_BINDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bin</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LIBDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">lib</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">ARCHIVE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LIBDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">lib</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">PRIVATE_HEADER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INCLUDEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">include</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">PUBLIC_HEADER</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INCLUDEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">include</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">FILE_SET</span></code> (type <code class="docutils literal notranslate"><span class="pre">HEADERS</span></code>)</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INCLUDEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">include</span></code></p></td>
</tr>
</tbody>
</table>
<p>Projects wishing to follow the common practice of installing headers into a
project-specific subdirectory may prefer using file sets with appropriate
paths and base directories. Otherwise, they must provide a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>
instead of being able to rely on the above (see next example below).</p>
<p>To make packages compliant with distribution filesystem layout policies, if
projects must specify a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>, it is recommended that they use a
path that begins with the appropriate <span class="target" id="index-1-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a> variable.
This allows package maintainers to control the install destination by setting
the appropriate cache variables.  The following example shows a static library
being installed to the default destination provided by
<span class="target" id="index-2-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a>, but with its headers installed to a project-specific
subdirectory without using file sets:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">mylib</span><span class="w"> </span><span class="no">STATIC</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
<span class="nf">set_target_properties(</span><span class="nb">mylib</span><span class="w"> </span><span class="no">PROPERTIES</span><span class="w"> </span><span class="no">PUBLIC_HEADER</span><span class="w"> </span><span class="nb">mylib.h</span><span class="nf">)</span><span class="w"></span>
<span class="nf">include(</span><span class="nb">GNUInstallDirs</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">mylib</span><span class="w"></span>
<span class="w">        </span><span class="no">PUBLIC_HEADER</span><span class="w"></span>
<span class="w">          </span><span class="no">DESTINATION</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_INSTALL_INCLUDEDIR</span><span class="o">}</span><span class="na">/myproj</span><span class="w"></span>
<span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>In addition to the common options listed above, each target can accept
the following additional arguments:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">NAMELINK_COMPONENT</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>On some platforms a versioned shared library has a symbolic link such
as:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>lib&lt;name&gt;.so -&gt; lib&lt;name&gt;.so.1
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">lib&lt;name&gt;.so.1</span></code> is the soname of the library and <code class="docutils literal notranslate"><span class="pre">lib&lt;name&gt;.so</span></code>
is a &quot;namelink&quot; allowing linkers to find the library when given
<code class="docutils literal notranslate"><span class="pre">-l&lt;name&gt;</span></code>. The <code class="docutils literal notranslate"><span class="pre">NAMELINK_COMPONENT</span></code> option is similar to the
<code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> option, but it changes the installation component of a shared
library namelink if one is generated. If not specified, this defaults to the
value of <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code>. It is an error to use this parameter outside of a
<code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code> block.</p>
<p>Consider the following example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">mylib</span><span class="w"></span>
<span class="w">        </span><span class="no">LIBRARY</span><span class="w"></span>
<span class="w">          </span><span class="no">COMPONENT</span><span class="w"> </span><span class="nb">Libraries</span><span class="w"></span>
<span class="w">          </span><span class="no">NAMELINK_COMPONENT</span><span class="w"> </span><span class="nb">Development</span><span class="w"></span>
<span class="w">        </span><span class="no">PUBLIC_HEADER</span><span class="w"></span>
<span class="w">          </span><span class="no">COMPONENT</span><span class="w"> </span><span class="nb">Development</span><span class="w"></span>
<span class="w">       </span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>In this scenario, if you choose to install only the <code class="docutils literal notranslate"><span class="pre">Development</span></code>
component, both the headers and namelink will be installed without the
library. (If you don't also install the <code class="docutils literal notranslate"><span class="pre">Libraries</span></code> component, the
namelink will be a dangling symlink, and projects that link to the library
will have build errors.) If you install only the <code class="docutils literal notranslate"><span class="pre">Libraries</span></code> component,
only the library will be installed, without the headers and namelink.</p>
<p>This option is typically used for package managers that have separate
runtime and development packages. For example, on Debian systems, the
library is expected to be in the runtime package, and the headers and
namelink are expected to be in the development package.</p>
<p>See the <span class="target" id="index-0-prop_tgt:VERSION"></span><a class="reference internal" href="../prop_tgt/VERSION.html#prop_tgt:VERSION" title="VERSION"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">VERSION</span></code></a> and <span class="target" id="index-0-prop_tgt:SOVERSION"></span><a class="reference internal" href="../prop_tgt/SOVERSION.html#prop_tgt:SOVERSION" title="SOVERSION"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">SOVERSION</span></code></a> target properties for
details on creating versioned shared libraries.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NAMELINK_ONLY</span></code></dt><dd><p>This option causes the installation of only the namelink when a library
target is installed. On platforms where versioned shared libraries do not
have namelinks or when a library is not versioned, the <code class="docutils literal notranslate"><span class="pre">NAMELINK_ONLY</span></code>
option installs nothing. It is an error to use this parameter outside of a
<code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code> block.</p>
<p>When <code class="docutils literal notranslate"><span class="pre">NAMELINK_ONLY</span></code> is given, either <code class="docutils literal notranslate"><span class="pre">NAMELINK_COMPONENT</span></code> or
<code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> may be used to specify the installation component of the
namelink, but <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> should generally be preferred.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NAMELINK_SKIP</span></code></dt><dd><p>Similar to <code class="docutils literal notranslate"><span class="pre">NAMELINK_ONLY</span></code>, but it has the opposite effect: it causes the
installation of library files other than the namelink when a library target
is installed. When neither <code class="docutils literal notranslate"><span class="pre">NAMELINK_ONLY</span></code> or <code class="docutils literal notranslate"><span class="pre">NAMELINK_SKIP</span></code> are given,
both portions are installed. On platforms where versioned shared libraries
do not have symlinks or when a library is not versioned, <code class="docutils literal notranslate"><span class="pre">NAMELINK_SKIP</span></code>
installs the library. It is an error to use this parameter outside of a
<code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code> block.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">NAMELINK_SKIP</span></code> is specified, <code class="docutils literal notranslate"><span class="pre">NAMELINK_COMPONENT</span></code> has no effect. It
is not recommended to use <code class="docutils literal notranslate"><span class="pre">NAMELINK_SKIP</span></code> in conjunction with
<code class="docutils literal notranslate"><span class="pre">NAMELINK_COMPONENT</span></code>.</p>
</dd>
</dl>
<p>The <a class="reference internal" href="#install-targets">install(TARGETS)</a> command can also accept the following options at the
top level:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">EXPORT</span></code></dt><dd><p>This option associates the installed target files with an export called
<code class="docutils literal notranslate"><span class="pre">&lt;export-name&gt;</span></code>.  It must appear before any target options.  To actually
install the export file itself, call <a class="reference internal" href="#install-export">install(EXPORT)</a>, documented below.
See documentation of the <span class="target" id="index-0-prop_tgt:EXPORT_NAME"></span><a class="reference internal" href="../prop_tgt/EXPORT_NAME.html#prop_tgt:EXPORT_NAME" title="EXPORT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">EXPORT_NAME</span></code></a> target property to change
the name of the exported target.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">EXPORT</span></code> is used and the targets include <code class="docutils literal notranslate"><span class="pre">PUBLIC</span></code> or <code class="docutils literal notranslate"><span class="pre">INTERFACE</span></code>
file sets, all of them must be specified with <code class="docutils literal notranslate"><span class="pre">FILE_SET</span></code> arguments. All
<code class="docutils literal notranslate"><span class="pre">PUBLIC</span></code> or <code class="docutils literal notranslate"><span class="pre">INTERFACE</span></code> file sets associated with a target are included
in the export.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INCLUDES</span> <span class="pre">DESTINATION</span></code></dt><dd><p>This option specifies a list of directories which will be added to the
<span class="target" id="index-0-prop_tgt:INTERFACE_INCLUDE_DIRECTORIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_INCLUDE_DIRECTORIES.html#prop_tgt:INTERFACE_INCLUDE_DIRECTORIES" title="INTERFACE_INCLUDE_DIRECTORIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_INCLUDE_DIRECTORIES</span></code></a> target property of the
<code class="docutils literal notranslate"><span class="pre">&lt;targets&gt;</span></code> when exported by the <a class="reference internal" href="#install-export">install(EXPORT)</a> command. If a
relative path is specified, it is treated as relative to the
<span class="target" id="index-0-genex:INSTALL_PREFIX"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#genex:INSTALL_PREFIX" title="INSTALL_PREFIX"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;INSTALL_PREFIX&gt;</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCY_SET</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>This option causes all runtime dependencies of installed executable, shared
library, and module targets to be added to the specified runtime dependency
set. This set can then be installed with an
<a class="reference internal" href="#install-runtime-dependency-set">install(RUNTIME_DEPENDENCY_SET)</a> command.</p>
<p>This keyword and the <code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCIES</span></code> keyword are mutually
exclusive.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCIES</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>This option causes all runtime dependencies of installed executable, shared
library, and module targets to be installed along with the targets
themselves. The <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code>, <code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code>, <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>, and generic
arguments are used to determine the properties (<code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>,
<code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code>, etc.) of the installation of these dependencies.</p>
<p><code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCIES</span></code> is semantically equivalent to the following pair
of calls:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="no">RUNTIME_DEPENDENCY_SET</span><span class="w"> </span><span class="nv">&lt;set-name&gt;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">RUNTIME_DEPENDENCY_SET</span><span class="w"> </span><span class="nv">&lt;set-name&gt;</span><span class="w"> </span><span class="nb">args...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">&lt;set-name&gt;</span></code> will be a randomly generated set name.
The <code class="docutils literal notranslate"><span class="pre">args...</span></code> may include any of the following keywords supported by
the <a class="reference internal" href="#install-runtime-dependency-set">install(RUNTIME_DEPENDENCY_SET)</a> command:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">DIRECTORIES</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PRE_INCLUDE_REGEXES</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PRE_EXCLUDE_REGEXES</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_INCLUDE_REGEXES</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_EXCLUDE_REGEXES</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_INCLUDE_FILES</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_EXCLUDE_FILES</span></code></p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCIES</span></code> and <code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCY_SET</span></code> keywords are
mutually exclusive.</p>
</dd>
</dl>
<p>One or more groups of properties may be specified in a single call to
the <code class="docutils literal notranslate"><span class="pre">TARGETS</span></code> form of this command.  A target may be installed more than
once to different locations.  Consider hypothetical targets <code class="docutils literal notranslate"><span class="pre">myExe</span></code>,
<code class="docutils literal notranslate"><span class="pre">mySharedLib</span></code>, and <code class="docutils literal notranslate"><span class="pre">myStaticLib</span></code>.  The code:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">myExe</span><span class="w"> </span><span class="nb">mySharedLib</span><span class="w"> </span><span class="nb">myStaticLib</span><span class="w"></span>
<span class="w">        </span><span class="no">RUNTIME</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nb">bin</span><span class="w"></span>
<span class="w">        </span><span class="no">LIBRARY</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nb">lib</span><span class="w"></span>
<span class="w">        </span><span class="no">ARCHIVE</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">lib/static</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">mySharedLib</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">/some/full/path</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>will install <code class="docutils literal notranslate"><span class="pre">myExe</span></code> to <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/bin</span></code> and <code class="docutils literal notranslate"><span class="pre">myStaticLib</span></code> to
<code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/lib/static</span></code>.  On non-DLL platforms <code class="docutils literal notranslate"><span class="pre">mySharedLib</span></code> will be
installed to <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/lib</span></code> and <code class="docutils literal notranslate"><span class="pre">/some/full/path</span></code>.  On DLL platforms
the <code class="docutils literal notranslate"><span class="pre">mySharedLib</span></code> DLL will be installed to <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/bin</span></code> and
<code class="docutils literal notranslate"><span class="pre">/some/full/path</span></code> and its import library will be installed to
<code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/lib/static</span></code> and <code class="docutils literal notranslate"><span class="pre">/some/full/path</span></code>.</p>
<p><a class="reference internal" href="../manual/cmake-buildsystem.7.html#interface-libraries"><span class="std std-ref">Interface Libraries</span></a> may be listed among the targets to install.
They install no artifacts but will be included in an associated <code class="docutils literal notranslate"><span class="pre">EXPORT</span></code>.
If <a class="reference internal" href="../manual/cmake-buildsystem.7.html#object-libraries"><span class="std std-ref">Object Libraries</span></a> are listed but given no destination for their
object files, they will be exported as <a class="reference internal" href="../manual/cmake-buildsystem.7.html#interface-libraries"><span class="std std-ref">Interface Libraries</span></a>.
This is sufficient to satisfy transitive usage requirements of other
targets that link to the object libraries in their implementation.</p>
<p>Installing a target with the <span class="target" id="index-0-prop_tgt:EXCLUDE_FROM_ALL"></span><a class="reference internal" href="../prop_tgt/EXCLUDE_FROM_ALL.html#prop_tgt:EXCLUDE_FROM_ALL" title="EXCLUDE_FROM_ALL"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">EXCLUDE_FROM_ALL</span></code></a> target property
set to <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> has undefined behavior.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3: </span>An install destination given as a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> argument may
use &quot;generator expressions&quot; with the syntax <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>.  See the
<span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a> manual for available expressions.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span><a class="reference internal" href="#install-targets">install(TARGETS)</a> can install targets that were created in
other directories.  When using such cross-directory install rules, running
<code class="docutils literal notranslate"><span class="pre">make</span> <span class="pre">install</span></code> (or similar) from a subdirectory will not guarantee that
targets from other directories are up-to-date.  You can use
<span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a> or <span class="target" id="index-0-command:add_dependencies"></span><a class="reference internal" href="add_dependencies.html#command:add_dependencies" title="add_dependencies"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_dependencies()</span></code></a>
to ensure that such out-of-directory targets are built before the
subdirectory-specific install rules are run.</p>
</div>
</section>
<section id="installing-imported-runtime-artifacts">
<h2>Installing Imported Runtime Artifacts<a class="headerlink" href="#installing-imported-runtime-artifacts" title="Permalink to this heading">¶</a></h2>
<div class="versionadded" id="imported-runtime-artifacts">
<span id="install-imported-runtime-artifacts"></span><p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">IMPORTED_RUNTIME_ARTIFACTS</span><span class="w"> </span><span class="nb">targets...</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">RUNTIME_DEPENDENCY_SET</span><span class="w"> </span><span class="nv">&lt;set-name&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[[</span><span class="no">LIBRARY</span><span class="p">|</span><span class="no">RUNTIME</span><span class="p">|</span><span class="no">FRAMEWORK</span><span class="p">|</span><span class="no">BUNDLE</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="p">[</span><span class="nb">Debug</span><span class="p">|</span><span class="nb">Release</span><span class="p">|...]]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">OPTIONAL</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">]</span><span class="w"> </span><span class="p">[...]</span><span class="w"></span>
<span class="w">        </span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">IMPORTED_RUNTIME_ARTIFACTS</span></code> form specifies rules for installing the
runtime artifacts of imported targets. Projects may do this if they want to
bundle outside executables or modules inside their installation. The
<code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code>, <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code>, <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>, and <code class="docutils literal notranslate"><span class="pre">BUNDLE</span></code> arguments have the
same semantics that they do in the <a class="reference internal" href="#targets">TARGETS</a> mode. Only the runtime artifacts
of imported targets are installed (except in the case of <span class="target" id="index-1-prop_tgt:FRAMEWORK"></span><a class="reference internal" href="../prop_tgt/FRAMEWORK.html#prop_tgt:FRAMEWORK" title="FRAMEWORK"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">FRAMEWORK</span></code></a>
libraries, <span class="target" id="index-1-prop_tgt:MACOSX_BUNDLE"></span><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE.html#prop_tgt:MACOSX_BUNDLE" title="MACOSX_BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MACOSX_BUNDLE</span></code></a> executables, and <span class="target" id="index-0-prop_tgt:BUNDLE"></span><a class="reference internal" href="../prop_tgt/BUNDLE.html#prop_tgt:BUNDLE" title="BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">BUNDLE</span></code></a>
CFBundles.) For example, headers and import libraries associated with DLLs are
not installed. In the case of <span class="target" id="index-2-prop_tgt:FRAMEWORK"></span><a class="reference internal" href="../prop_tgt/FRAMEWORK.html#prop_tgt:FRAMEWORK" title="FRAMEWORK"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">FRAMEWORK</span></code></a> libraries,
<span class="target" id="index-2-prop_tgt:MACOSX_BUNDLE"></span><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE.html#prop_tgt:MACOSX_BUNDLE" title="MACOSX_BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MACOSX_BUNDLE</span></code></a> executables, and <span class="target" id="index-1-prop_tgt:BUNDLE"></span><a class="reference internal" href="../prop_tgt/BUNDLE.html#prop_tgt:BUNDLE" title="BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">BUNDLE</span></code></a> CFBundles, the
entire directory is installed.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">RUNTIME_DEPENDENCY_SET</span></code> option causes the runtime artifacts of the
imported executable, shared library, and module library <code class="docutils literal notranslate"><span class="pre">targets</span></code> to be
added to the <code class="docutils literal notranslate"><span class="pre">&lt;set-name&gt;</span></code> runtime dependency set. This set can then be
installed with an <a class="reference internal" href="#install-runtime-dependency-set">install(RUNTIME_DEPENDENCY_SET)</a> command.</p>
</section>
<section id="installing-files">
<h2>Installing Files<a class="headerlink" href="#installing-files" title="Permalink to this heading">¶</a></h2>
<div class="admonition note" id="programs">
<span id="files"></span><span id="install-programs"></span><span id="install-files"></span><p class="admonition-title">Note</p>
<p>If installing header files, consider using file sets defined by
<span class="target" id="index-1-command:target_sources"></span><a class="reference internal" href="target_sources.html#command:target_sources" title="target_sources"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_sources(FILE_SET)</span></code></a> instead. File sets associate
headers with a target and they install as part of the target.</p>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="o">&lt;</span><span class="no">FILES</span><span class="p">|</span><span class="no">PROGRAMS</span><span class="o">&gt;</span><span class="w"> </span><span class="nb">files...</span><span class="w"></span>
<span class="w">        </span><span class="no">TYPE</span><span class="w"> </span><span class="nv">&lt;type&gt;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="p">[</span><span class="nb">Debug</span><span class="p">|</span><span class="nb">Release</span><span class="p">|...]]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">RENAME</span><span class="w"> </span><span class="nv">&lt;name&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">OPTIONAL</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">FILES</span></code> form specifies rules for installing files for a project.
File names given as relative paths are interpreted with respect to the
current source directory.  Files installed by this form are by default
given permissions <code class="docutils literal notranslate"><span class="pre">OWNER_WRITE</span></code>, <code class="docutils literal notranslate"><span class="pre">OWNER_READ</span></code>, <code class="docutils literal notranslate"><span class="pre">GROUP_READ</span></code>, and
<code class="docutils literal notranslate"><span class="pre">WORLD_READ</span></code> if no <code class="docutils literal notranslate"><span class="pre">PERMISSIONS</span></code> argument is given.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">PROGRAMS</span></code> form is identical to the <code class="docutils literal notranslate"><span class="pre">FILES</span></code> form except that the
default permissions for the installed file also include <code class="docutils literal notranslate"><span class="pre">OWNER_EXECUTE</span></code>,
<code class="docutils literal notranslate"><span class="pre">GROUP_EXECUTE</span></code>, and <code class="docutils literal notranslate"><span class="pre">WORLD_EXECUTE</span></code>.  This form is intended to install
programs that are not targets, such as shell scripts.  Use the <code class="docutils literal notranslate"><span class="pre">TARGETS</span></code>
form to install targets built within the project.</p>
<p>The list of <code class="docutils literal notranslate"><span class="pre">files...</span></code> given to <code class="docutils literal notranslate"><span class="pre">FILES</span></code> or <code class="docutils literal notranslate"><span class="pre">PROGRAMS</span></code> may use
&quot;generator expressions&quot; with the syntax <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>.  See the
<span class="target" id="index-1-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a> manual for available expressions.
However, if any item begins in a generator expression it must evaluate
to a full path.</p>
<p>Either a <code class="docutils literal notranslate"><span class="pre">TYPE</span></code> or a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> must be provided, but not both.
A <code class="docutils literal notranslate"><span class="pre">TYPE</span></code> argument specifies the generic file type of the files being
installed.  A destination will then be set automatically by taking the
corresponding variable from <span class="target" id="index-3-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a>, or by using a
built-in default if that variable is not defined.  See the table below for
the supported file types and their corresponding variables and built-in
defaults.  Projects can provide a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> argument instead of a
file type if they wish to explicitly define the install destination.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p><code class="docutils literal notranslate"><span class="pre">TYPE</span></code> Argument</p></th>
<th class="head"><p>GNUInstallDirs Variable</p></th>
<th class="head"><p>Built-In Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_BINDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bin</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SBIN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_SBINDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">sbin</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LIB</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LIBDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">lib</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INCLUDE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INCLUDEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">include</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SYSCONF</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_SYSCONFDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">etc</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SHAREDSTATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_SHARESTATEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">com</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LOCALSTATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LOCALSTATEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">var</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">RUNSTATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_RUNSTATEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;LOCALSTATE</span> <span class="pre">dir&gt;/run</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATA</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_DATADIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INFO</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INFODIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/info</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LOCALE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LOCALEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/locale</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_MANDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/man</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOC</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_DOCDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/doc</span></code></p></td>
</tr>
</tbody>
</table>
<p>Projects wishing to follow the common practice of installing headers into a
project-specific subdirectory will need to provide a destination rather than
rely on the above. Using file sets for headers instead of <code class="docutils literal notranslate"><span class="pre">install(FILES)</span></code>
would be even better (see <span class="target" id="index-2-command:target_sources"></span><a class="reference internal" href="target_sources.html#command:target_sources" title="target_sources"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_sources(FILE_SET)</span></code></a>).</p>
<p>Note that some of the types' built-in defaults use the <code class="docutils literal notranslate"><span class="pre">DATAROOT</span></code> directory as
a prefix. The <code class="docutils literal notranslate"><span class="pre">DATAROOT</span></code> prefix is calculated similarly to the types, with
<code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_DATAROOTDIR</span></code> as the variable and <code class="docutils literal notranslate"><span class="pre">share</span></code> as the built-in
default. You cannot use <code class="docutils literal notranslate"><span class="pre">DATAROOT</span></code> as a <code class="docutils literal notranslate"><span class="pre">TYPE</span></code> parameter; please use
<code class="docutils literal notranslate"><span class="pre">DATA</span></code> instead.</p>
<p>To make packages compliant with distribution filesystem layout policies, if
projects must specify a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>, it is recommended that they use a
path that begins with the appropriate <span class="target" id="index-4-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a> variable.
This allows package maintainers to control the install destination by setting
the appropriate cache variables.  The following example shows how to follow
this advice while installing an image to a project-specific documentation
subdirectory:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include(</span><span class="nb">GNUInstallDirs</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">FILES</span><span class="w"> </span><span class="nb">logo.png</span><span class="w"></span>
<span class="w">        </span><span class="no">DESTINATION</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_INSTALL_DOCDIR</span><span class="o">}</span><span class="na">/myproj</span><span class="w"></span>
<span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>An install destination given as a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> argument may
use &quot;generator expressions&quot; with the syntax <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>.  See the
<span class="target" id="index-2-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a> manual for available expressions.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.20: </span>An install rename given as a <code class="docutils literal notranslate"><span class="pre">RENAME</span></code> argument may
use &quot;generator expressions&quot; with the syntax <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>.  See the
<span class="target" id="index-3-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a> manual for available expressions.</p>
</div>
</section>
<section id="installing-directories">
<h2>Installing Directories<a class="headerlink" href="#installing-directories" title="Permalink to this heading">¶</a></h2>
<div class="admonition note" id="directory">
<span id="install-directory"></span><p class="admonition-title">Note</p>
<p>To install a directory sub-tree of headers, consider using file sets
defined by <span class="target" id="index-3-command:target_sources"></span><a class="reference internal" href="target_sources.html#command:target_sources" title="target_sources"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_sources(FILE_SET)</span></code></a> instead. File sets not only
preserve directory structure, they also associate headers with a target
and install as part of the target.</p>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">DIRECTORY</span><span class="w"> </span><span class="nb">dirs...</span><span class="w"></span>
<span class="w">        </span><span class="no">TYPE</span><span class="w"> </span><span class="nv">&lt;type&gt;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">FILE_PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">DIRECTORY_PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">USE_SOURCE_PERMISSIONS</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">OPTIONAL</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">MESSAGE_NEVER</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="p">[</span><span class="nb">Debug</span><span class="p">|</span><span class="nb">Release</span><span class="p">|...]]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">FILES_MATCHING</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[[</span><span class="no">PATTERN</span><span class="w"> </span><span class="nv">&lt;pattern&gt;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">REGEX</span><span class="w"> </span><span class="nv">&lt;regex&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">EXCLUDE</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]]</span><span class="w"> </span><span class="p">[...]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">DIRECTORY</span></code> form installs contents of one or more directories to a
given destination.  The directory structure is copied verbatim to the
destination.  The last component of each directory name is appended to
the destination directory but a trailing slash may be used to avoid
this because it leaves the last component empty.  Directory names
given as relative paths are interpreted with respect to the current
source directory.  If no input directory names are given the
destination directory will be created but nothing will be installed
into it.  The <code class="docutils literal notranslate"><span class="pre">FILE_PERMISSIONS</span></code> and <code class="docutils literal notranslate"><span class="pre">DIRECTORY_PERMISSIONS</span></code> options
specify permissions given to files and directories in the destination.
If <code class="docutils literal notranslate"><span class="pre">USE_SOURCE_PERMISSIONS</span></code> is specified and <code class="docutils literal notranslate"><span class="pre">FILE_PERMISSIONS</span></code> is not,
file permissions will be copied from the source directory structure.
If no permissions are specified files will be given the default
permissions specified in the <code class="docutils literal notranslate"><span class="pre">FILES</span></code> form of the command, and the
directories will be given the default permissions specified in the
<code class="docutils literal notranslate"><span class="pre">PROGRAMS</span></code> form of the command.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>The <code class="docutils literal notranslate"><span class="pre">MESSAGE_NEVER</span></code> option disables file installation status output.</p>
</div>
<p>Installation of directories may be controlled with fine granularity
using the <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code> or <code class="docutils literal notranslate"><span class="pre">REGEX</span></code> options.  These &quot;match&quot; options specify a
globbing pattern or regular expression to match directories or files
encountered within input directories.  They may be used to apply
certain options (see below) to a subset of the files and directories
encountered.  The full path to each input file or directory (with
forward slashes) is matched against the expression.  A <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code> will
match only complete file names: the portion of the full path matching
the pattern must occur at the end of the file name and be preceded by
a slash.  A <code class="docutils literal notranslate"><span class="pre">REGEX</span></code> will match any portion of the full path but it may
use <code class="docutils literal notranslate"><span class="pre">/</span></code> and <code class="docutils literal notranslate"><span class="pre">$</span></code> to simulate the <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code> behavior.  By default all
files and directories are installed whether or not they are matched.
The <code class="docutils literal notranslate"><span class="pre">FILES_MATCHING</span></code> option may be given before the first match option
to disable installation of files (but not directories) not matched by
any expression.  For example, the code</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">DIRECTORY</span><span class="w"> </span><span class="na">src/</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">doc/myproj</span><span class="w"></span>
<span class="w">        </span><span class="no">FILES_MATCHING</span><span class="w"> </span><span class="no">PATTERN</span><span class="w"> </span><span class="s">&quot;*.png&quot;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>will extract and install images from a source tree.</p>
<p>Some options may follow a <code class="docutils literal notranslate"><span class="pre">PATTERN</span></code> or <code class="docutils literal notranslate"><span class="pre">REGEX</span></code> expression as described
under <a class="reference internal" href="string.html#regex-specification"><span class="std std-ref">string(REGEX)</span></a> and are applied
only to files or directories matching them.  The <code class="docutils literal notranslate"><span class="pre">EXCLUDE</span></code> option will
skip the matched file or directory.  The <code class="docutils literal notranslate"><span class="pre">PERMISSIONS</span></code> option overrides
the permissions setting for the matched file or directory.  For
example the code</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">DIRECTORY</span><span class="w"> </span><span class="nb">icons</span><span class="w"> </span><span class="na">scripts/</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">share/myproj</span><span class="w"></span>
<span class="w">        </span><span class="no">PATTERN</span><span class="w"> </span><span class="s">&quot;CVS&quot;</span><span class="w"> </span><span class="no">EXCLUDE</span><span class="w"></span>
<span class="w">        </span><span class="no">PATTERN</span><span class="w"> </span><span class="s">&quot;scripts/*&quot;</span><span class="w"></span>
<span class="w">        </span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="no">OWNER_EXECUTE</span><span class="w"> </span><span class="no">OWNER_WRITE</span><span class="w"> </span><span class="no">OWNER_READ</span><span class="w"></span>
<span class="w">                    </span><span class="no">GROUP_EXECUTE</span><span class="w"> </span><span class="no">GROUP_READ</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>will install the <code class="docutils literal notranslate"><span class="pre">icons</span></code> directory to <code class="docutils literal notranslate"><span class="pre">share/myproj/icons</span></code> and the
<code class="docutils literal notranslate"><span class="pre">scripts</span></code> directory to <code class="docutils literal notranslate"><span class="pre">share/myproj</span></code>.  The icons will get default
file permissions, the scripts will be given specific permissions, and any
<code class="docutils literal notranslate"><span class="pre">CVS</span></code> directories will be excluded.</p>
<p>Either a <code class="docutils literal notranslate"><span class="pre">TYPE</span></code> or a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> must be provided, but not both.
A <code class="docutils literal notranslate"><span class="pre">TYPE</span></code> argument specifies the generic file type of the files within the
listed directories being installed.  A destination will then be set
automatically by taking the corresponding variable from
<span class="target" id="index-5-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a>, or by using a built-in default if that variable
is not defined.  See the table below for the supported file types and their
corresponding variables and built-in defaults.  Projects can provide a
<code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> argument instead of a file type if they wish to explicitly
define the install destination.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p><code class="docutils literal notranslate"><span class="pre">TYPE</span></code> Argument</p></th>
<th class="head"><p>GNUInstallDirs Variable</p></th>
<th class="head"><p>Built-In Default</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BIN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_BINDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bin</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SBIN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_SBINDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">sbin</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LIB</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LIBDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">lib</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INCLUDE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INCLUDEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">include</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SYSCONF</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_SYSCONFDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">etc</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">SHAREDSTATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_SHARESTATEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">com</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LOCALSTATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LOCALSTATEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">var</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">RUNSTATE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_RUNSTATEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;LOCALSTATE</span> <span class="pre">dir&gt;/run</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DATA</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_DATADIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INFO</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_INFODIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/info</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">LOCALE</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_LOCALEDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/locale</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">MAN</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_MANDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/man</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOC</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_DOCDIR}</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;DATAROOT</span> <span class="pre">dir&gt;/doc</span></code></p></td>
</tr>
</tbody>
</table>
<p>Note that some of the types' built-in defaults use the <code class="docutils literal notranslate"><span class="pre">DATAROOT</span></code> directory as
a prefix. The <code class="docutils literal notranslate"><span class="pre">DATAROOT</span></code> prefix is calculated similarly to the types, with
<code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_DATAROOTDIR</span></code> as the variable and <code class="docutils literal notranslate"><span class="pre">share</span></code> as the built-in
default. You cannot use <code class="docutils literal notranslate"><span class="pre">DATAROOT</span></code> as a <code class="docutils literal notranslate"><span class="pre">TYPE</span></code> parameter; please use
<code class="docutils literal notranslate"><span class="pre">DATA</span></code> instead.</p>
<p>To make packages compliant with distribution filesystem layout policies, if
projects must specify a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>, it is recommended that they use a
path that begins with the appropriate <span class="target" id="index-6-module:GNUInstallDirs"></span><a class="reference internal" href="../module/GNUInstallDirs.html#module:GNUInstallDirs" title="GNUInstallDirs"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">GNUInstallDirs</span></code></a> variable.
This allows package maintainers to control the install destination by setting
the appropriate cache variables.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>An install destination given as a <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> argument may
use &quot;generator expressions&quot; with the syntax <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>.  See the
<span class="target" id="index-4-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a> manual for available expressions.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>The list of <code class="docutils literal notranslate"><span class="pre">dirs...</span></code> given to <code class="docutils literal notranslate"><span class="pre">DIRECTORY</span></code> may use
&quot;generator expressions&quot; too.</p>
</div>
</section>
<section id="custom-installation-logic">
<h2>Custom Installation Logic<a class="headerlink" href="#custom-installation-logic" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate" id="script"><span id="code"></span><span id="install-script"></span><span id="install-code"></span><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="p">[[</span><span class="no">SCRIPT</span><span class="w"> </span><span class="nv">&lt;file&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">CODE</span><span class="w"> </span><span class="nv">&lt;code&gt;</span><span class="p">]]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">ALL_COMPONENTS</span><span class="w"> </span><span class="p">|</span><span class="w"> </span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="w"> </span><span class="p">[...]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">SCRIPT</span></code> form will invoke the given CMake script files during
installation.  If the script file name is a relative path it will be
interpreted with respect to the current source directory.  The <code class="docutils literal notranslate"><span class="pre">CODE</span></code>
form will invoke the given CMake code during installation.  Code is
specified as a single argument inside a double-quoted string.  For
example, the code</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">CODE</span><span class="w"> </span><span class="s">&quot;MESSAGE(\&quot;Sample install message.\&quot;)&quot;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>will print a message during installation.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.21: </span>When the <code class="docutils literal notranslate"><span class="pre">ALL_COMPONENTS</span></code> option is given, the custom installation
script code will be executed for every component of a component-specific
installation.  This option is mutually exclusive with the <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code>
option.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.14: </span><code class="docutils literal notranslate"><span class="pre">&lt;file&gt;</span></code> or <code class="docutils literal notranslate"><span class="pre">&lt;code&gt;</span></code> may use &quot;generator expressions&quot; with the syntax
<code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code> (in the case of <code class="docutils literal notranslate"><span class="pre">&lt;file&gt;</span></code>, this refers to their use in the file
name, not the file's contents).  See the
<span class="target" id="index-5-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a> manual for available expressions.</p>
</div>
</section>
<section id="installing-exports">
<h2>Installing Exports<a class="headerlink" href="#installing-exports" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate" id="export"><span id="install-export"></span><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">EXPORT</span><span class="w"> </span><span class="nv">&lt;export-name&gt;</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">NAMESPACE</span><span class="w"> </span><span class="nv">&lt;namespace&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">FILE</span><span class="w"> </span><span class="nv">&lt;name&gt;</span><span class="p">.</span><span class="nb">cmake</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="p">[</span><span class="nb">Debug</span><span class="p">|</span><span class="nb">Release</span><span class="p">|...]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">CXX_MODULES_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;directory&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">EXPORT_LINK_INTERFACE_LIBRARIES</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">EXPORT_ANDROID_MK</span><span class="w"> </span><span class="nv">&lt;export-name&gt;</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="p">[...]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">EXPORT</span></code> form generates and installs a CMake file containing code to
import targets from the installation tree into another project.
Target installations are associated with the export <code class="docutils literal notranslate"><span class="pre">&lt;export-name&gt;</span></code>
using the <code class="docutils literal notranslate"><span class="pre">EXPORT</span></code> option of the <a class="reference internal" href="#install-targets">install(TARGETS)</a> signature
documented above.  The <code class="docutils literal notranslate"><span class="pre">NAMESPACE</span></code> option will prepend <code class="docutils literal notranslate"><span class="pre">&lt;namespace&gt;</span></code> to
the target names as they are written to the import file.  By default
the generated file will be called <code class="docutils literal notranslate"><span class="pre">&lt;export-name&gt;.cmake</span></code> but the <code class="docutils literal notranslate"><span class="pre">FILE</span></code>
option may be used to specify a different name.  The value given to
the <code class="docutils literal notranslate"><span class="pre">FILE</span></code> option must be a file name with the <code class="docutils literal notranslate"><span class="pre">.cmake</span></code> extension.
If a <code class="docutils literal notranslate"><span class="pre">CONFIGURATIONS</span></code> option is given then the file will only be installed
when one of the named configurations is installed.  Additionally, the
generated import file will reference only the matching target
configurations.  The <code class="docutils literal notranslate"><span class="pre">EXPORT_LINK_INTERFACE_LIBRARIES</span></code> keyword, if
present, causes the contents of the properties matching
<code class="docutils literal notranslate"><span class="pre">(IMPORTED_)?LINK_INTERFACE_LIBRARIES(_&lt;CONFIG&gt;)?</span></code> to be exported, when
policy <span class="target" id="index-0-policy:CMP0022"></span><a class="reference internal" href="../policy/CMP0022.html#policy:CMP0022" title="CMP0022"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0022</span></code></a> is <code class="docutils literal notranslate"><span class="pre">NEW</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The installed <code class="docutils literal notranslate"><span class="pre">&lt;export-name&gt;.cmake</span></code> file may come with additional
per-configuration <code class="docutils literal notranslate"><span class="pre">&lt;export-name&gt;-*.cmake</span></code> files to be loaded by
globbing.  Do not use an export name that is the same as the package
name in combination with installing a <code class="docutils literal notranslate"><span class="pre">&lt;package-name&gt;-config.cmake</span></code>
file or the latter may be incorrectly matched by the glob and loaded.</p>
</div>
<p>When a <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> option is given, the listed <code class="docutils literal notranslate"><span class="pre">&lt;component&gt;</span></code> implicitly
depends on all components mentioned in the export set. The exported
<code class="docutils literal notranslate"><span class="pre">&lt;name&gt;.cmake</span></code> file will require each of the exported components to be
present in order for dependent projects to build properly. For example, a
project may define components <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> and <code class="docutils literal notranslate"><span class="pre">Development</span></code>, with shared
libraries going into the <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> component and static libraries and
headers going into the <code class="docutils literal notranslate"><span class="pre">Development</span></code> component. The export set would also
typically be part of the <code class="docutils literal notranslate"><span class="pre">Development</span></code> component, but it would export
targets from both the <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> and <code class="docutils literal notranslate"><span class="pre">Development</span></code> components. Therefore,
the <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> component would need to be installed if the <code class="docutils literal notranslate"><span class="pre">Development</span></code>
component was installed, but not vice versa. If the <code class="docutils literal notranslate"><span class="pre">Development</span></code> component
was installed without the <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> component, dependent projects that try
to link against it would have build errors. Package managers, such as APT and
RPM, typically handle this by listing the <code class="docutils literal notranslate"><span class="pre">Runtime</span></code> component as a dependency
of the <code class="docutils literal notranslate"><span class="pre">Development</span></code> component in the package metadata, ensuring that the
library is always installed if the headers and CMake export file are present.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>In addition to cmake language files, the <code class="docutils literal notranslate"><span class="pre">EXPORT_ANDROID_MK</span></code> mode may be
used to specify an export to the android ndk build system.  This mode
accepts the same options as the normal export mode.  The Android
NDK supports the use of prebuilt libraries, both static and shared. This
allows cmake to build the libraries of a project and make them available
to an ndk build system complete with transitive dependencies, include flags
and defines required to use the libraries.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">CXX_MODULES_DIRECTORY</span></code></p>
<blockquote>
<div><div class="admonition note">
<p class="admonition-title">Note</p>
<p>Experimental. Gated by <code class="docutils literal notranslate"><span class="pre">CMAKE_EXPERIMENTAL_CXX_MODULE_CMAKE_API</span></code></p>
</div>
<p>Specify a subdirectory to store C++ module information for targets in the
export set. This directory will be populated with files which add the
necessary target property information to the relevant targets. Note that
without this information, none of the C++ modules which are part of the
targets in the export set will support being imported in consuming targets.</p>
</div></blockquote>
<p>The <code class="docutils literal notranslate"><span class="pre">EXPORT</span></code> form is useful to help outside projects use targets built
and installed by the current project.  For example, the code</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">myexe</span><span class="w"> </span><span class="no">EXPORT</span><span class="w"> </span><span class="nb">myproj</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nb">bin</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">EXPORT</span><span class="w"> </span><span class="nb">myproj</span><span class="w"> </span><span class="no">NAMESPACE</span><span class="w"> </span><span class="nb">mp_</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">lib/myproj</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install(</span><span class="no">EXPORT_ANDROID_MK</span><span class="w"> </span><span class="nb">myproj</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">share/ndk-modules</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>will install the executable <code class="docutils literal notranslate"><span class="pre">myexe</span></code> to <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/bin</span></code> and code to import
it in the file <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/lib/myproj/myproj.cmake</span></code> and
<code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;/share/ndk-modules/Android.mk</span></code>.  An outside project
may load this file with the include command and reference the <code class="docutils literal notranslate"><span class="pre">myexe</span></code>
executable from the installation tree using the imported target name
<code class="docutils literal notranslate"><span class="pre">mp_myexe</span></code> as if the target were built in its own tree.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This command supersedes the <span class="target" id="index-0-command:install_targets"></span><a class="reference internal" href="install_targets.html#command:install_targets" title="install_targets"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install_targets()</span></code></a> command and
the <span class="target" id="index-0-prop_tgt:PRE_INSTALL_SCRIPT"></span><a class="reference internal" href="../prop_tgt/PRE_INSTALL_SCRIPT.html#prop_tgt:PRE_INSTALL_SCRIPT" title="PRE_INSTALL_SCRIPT"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PRE_INSTALL_SCRIPT</span></code></a> and <span class="target" id="index-0-prop_tgt:POST_INSTALL_SCRIPT"></span><a class="reference internal" href="../prop_tgt/POST_INSTALL_SCRIPT.html#prop_tgt:POST_INSTALL_SCRIPT" title="POST_INSTALL_SCRIPT"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">POST_INSTALL_SCRIPT</span></code></a>
target properties.  It also replaces the <code class="docutils literal notranslate"><span class="pre">FILES</span></code> forms of the
<span class="target" id="index-0-command:install_files"></span><a class="reference internal" href="install_files.html#command:install_files" title="install_files"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install_files()</span></code></a> and <span class="target" id="index-0-command:install_programs"></span><a class="reference internal" href="install_programs.html#command:install_programs" title="install_programs"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install_programs()</span></code></a> commands.
The processing order of these install rules relative to
those generated by <span class="target" id="index-1-command:install_targets"></span><a class="reference internal" href="install_targets.html#command:install_targets" title="install_targets"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install_targets()</span></code></a>,
<span class="target" id="index-1-command:install_files"></span><a class="reference internal" href="install_files.html#command:install_files" title="install_files"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install_files()</span></code></a>, and <span class="target" id="index-1-command:install_programs"></span><a class="reference internal" href="install_programs.html#command:install_programs" title="install_programs"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install_programs()</span></code></a> commands
is not defined.</p>
</div>
</section>
<section id="installing-runtime-dependencies">
<h2>Installing Runtime Dependencies<a class="headerlink" href="#installing-runtime-dependencies" title="Permalink to this heading">¶</a></h2>
<div class="versionadded" id="runtime-dependency-set">
<span id="install-runtime-dependency-set"></span><p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install(</span><span class="no">RUNTIME_DEPENDENCY_SET</span><span class="w"> </span><span class="nv">&lt;set-name&gt;</span><span class="w"></span>
<span class="w">        </span><span class="p">[[</span><span class="no">LIBRARY</span><span class="p">|</span><span class="no">RUNTIME</span><span class="p">|</span><span class="no">FRAMEWORK</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">DESTINATION</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">PERMISSIONS</span><span class="w"> </span><span class="nb">permissions...</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="p">[</span><span class="nb">Debug</span><span class="p">|</span><span class="nb">Release</span><span class="p">|...]]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">NAMELINK_COMPONENT</span><span class="w"> </span><span class="nv">&lt;component&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">OPTIONAL</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">EXCLUDE_FROM_ALL</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">]</span><span class="w"> </span><span class="p">[...]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">PRE_INCLUDE_REGEXES</span><span class="w"> </span><span class="nb">regexes...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">PRE_EXCLUDE_REGEXES</span><span class="w"> </span><span class="nb">regexes...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">POST_INCLUDE_REGEXES</span><span class="w"> </span><span class="nb">regexes...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">POST_EXCLUDE_REGEXES</span><span class="w"> </span><span class="nb">regexes...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">POST_INCLUDE_FILES</span><span class="w"> </span><span class="nb">files...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">POST_EXCLUDE_FILES</span><span class="w"> </span><span class="nb">files...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="p">[</span><span class="no">DIRECTORIES</span><span class="w"> </span><span class="nb">directories...</span><span class="p">]</span><span class="w"></span>
<span class="w">        </span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Installs a runtime dependency set previously created by one or more
<a class="reference internal" href="#install-targets">install(TARGETS)</a> or <a class="reference internal" href="#install-imported-runtime-artifacts">install(IMPORTED_RUNTIME_ARTIFACTS)</a> commands. The
dependencies of targets belonging to a runtime dependency set are installed in
the <code class="docutils literal notranslate"><span class="pre">RUNTIME</span></code> destination and component on DLL platforms, and in the
<code class="docutils literal notranslate"><span class="pre">LIBRARY</span></code> destination and component on non-DLL platforms. macOS frameworks
are installed in the <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> destination and component.
Targets built within the build tree will never be installed as runtime
dependencies, nor will their own dependencies, unless the targets themselves
are installed with <a class="reference internal" href="#install-targets">install(TARGETS)</a>.</p>
<p>The generated install script calls <span class="target" id="index-0-command:file"></span><a class="reference internal" href="file.html#command:file" title="file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(GET_RUNTIME_DEPENDENCIES)</span></code></a>
on the build-tree files to calculate the runtime dependencies. The build-tree
executable files are passed as the <code class="docutils literal notranslate"><span class="pre">EXECUTABLES</span></code> argument, the build-tree
shared libraries as the <code class="docutils literal notranslate"><span class="pre">LIBRARIES</span></code> argument, and the build-tree modules as
the <code class="docutils literal notranslate"><span class="pre">MODULES</span></code> argument. On macOS, if one of the executables is a
<span class="target" id="index-3-prop_tgt:MACOSX_BUNDLE"></span><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE.html#prop_tgt:MACOSX_BUNDLE" title="MACOSX_BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MACOSX_BUNDLE</span></code></a>, that executable is passed as the
<code class="docutils literal notranslate"><span class="pre">BUNDLE_EXECUTABLE</span></code> argument. At most one such bundle executable may be in
the runtime dependency set on macOS. The <span class="target" id="index-4-prop_tgt:MACOSX_BUNDLE"></span><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE.html#prop_tgt:MACOSX_BUNDLE" title="MACOSX_BUNDLE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MACOSX_BUNDLE</span></code></a> property
has no effect on other platforms. Note that
<span class="target" id="index-1-command:file"></span><a class="reference internal" href="file.html#command:file" title="file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(GET_RUNTIME_DEPENDENCIES)</span></code></a> only supports collecting the runtime
dependencies for Windows, Linux and macOS platforms, so
<code class="docutils literal notranslate"><span class="pre">install(RUNTIME_DEPENDENCY_SET)</span></code> has the same limitation.</p>
<p>The following sub-arguments are forwarded through as the corresponding
arguments to <span class="target" id="index-2-command:file"></span><a class="reference internal" href="file.html#command:file" title="file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(GET_RUNTIME_DEPENDENCIES)</span></code></a> (for those that provide
a non-empty list of directories, regular expressions or files).  They all
support <span class="target" id="index-6-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">DIRECTORIES</span> <span class="pre">&lt;directories&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PRE_INCLUDE_REGEXES</span> <span class="pre">&lt;regexes&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PRE_EXCLUDE_REGEXES</span> <span class="pre">&lt;regexes&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_INCLUDE_REGEXES</span> <span class="pre">&lt;regexes&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_EXCLUDE_REGEXES</span> <span class="pre">&lt;regexes&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_INCLUDE_FILES</span> <span class="pre">&lt;files&gt;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">POST_EXCLUDE_FILES</span> <span class="pre">&lt;files&gt;</span></code></p></li>
</ul>
</section>
<section id="generated-installation-script">
<h2>Generated Installation Script<a class="headerlink" href="#generated-installation-script" title="Permalink to this heading">¶</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Use of this feature is not recommended. Please consider using the
<a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-install"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">--install</span></code></a> instead.</p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">install()</span></code> command generates a file, <code class="docutils literal notranslate"><span class="pre">cmake_install.cmake</span></code>, inside
the build directory, which is used internally by the generated install target
and by CPack. You can also invoke this script manually with
<a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-P"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-P</span></code></a>. This script accepts several variables:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code></dt><dd><p>Set this variable to install only a single CPack component as opposed to all
of them. For example, if you only want to install the <code class="docutils literal notranslate"><span class="pre">Development</span></code>
component, run <code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-DCOMPONENT=Development</span> <span class="pre">-P</span> <span class="pre">cmake_install.cmake</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">BUILD_TYPE</span></code></dt><dd><p>Set this variable to change the build type if you are using a multi-config
generator. For example, to install with the <code class="docutils literal notranslate"><span class="pre">Debug</span></code> configuration, run
<code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-DBUILD_TYPE=Debug</span> <span class="pre">-P</span> <span class="pre">cmake_install.cmake</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DESTDIR</span></code></dt><dd><p>This is an environment variable rather than a CMake variable. It allows you
to change the installation prefix on UNIX systems. See <span class="target" id="index-0-envvar:DESTDIR"></span><a class="reference internal" href="../envvar/DESTDIR.html#envvar:DESTDIR" title="DESTDIR"><code class="xref cmake cmake-envvar docutils literal notranslate"><span class="pre">DESTDIR</span></code></a> for
details.</p>
</dd>
</dl>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">install</a><ul>
<li><a class="reference internal" href="#synopsis">Synopsis</a></li>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#installing-targets">Installing Targets</a></li>
<li><a class="reference internal" href="#installing-imported-runtime-artifacts">Installing Imported Runtime Artifacts</a></li>
<li><a class="reference internal" href="#installing-files">Installing Files</a></li>
<li><a class="reference internal" href="#installing-directories">Installing Directories</a></li>
<li><a class="reference internal" href="#custom-installation-logic">Custom Installation Logic</a></li>
<li><a class="reference internal" href="#installing-exports">Installing Exports</a></li>
<li><a class="reference internal" href="#installing-runtime-dependencies">Installing Runtime Dependencies</a></li>
<li><a class="reference internal" href="#generated-installation-script">Generated Installation Script</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="include_regular_expression.html"
                          title="previous chapter">include_regular_expression</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="link_directories.html"
                          title="next chapter">link_directories</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/install.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="link_directories.html" title="link_directories"
             >next</a> |</li>
        <li class="right" >
          <a href="include_regular_expression.html" title="include_regular_expression"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">install</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>