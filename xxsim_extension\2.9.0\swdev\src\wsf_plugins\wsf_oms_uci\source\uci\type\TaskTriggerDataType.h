// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TaskTriggerDataType_h
#define Uci__Type__TaskTriggerDataType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__TaskTriggerPrecedenceType_h)
# include "uci/type/TaskTriggerPrecedenceType.h"
#endif

#if !defined(Uci__Type__TaskTriggerTaskType_h)
# include "uci/type/TaskTriggerTaskType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TaskTriggerDataType sequence accessor class */
      class TaskTriggerDataType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TaskTriggerDataType()
         { }

         /** Returns this accessor's type constant, i.e. TaskTriggerDataType
           *
           * @return This accessor's type constant, i.e. TaskTriggerDataType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::taskTriggerDataType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TaskTriggerDataType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Precedence.
           *
           * @return The acecssor that provides access to the complex content that is identified by Precedence.
           */
         virtual const uci::type::TaskTriggerPrecedenceType& getPrecedence() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Precedence.
           *
           * @return The acecssor that provides access to the complex content that is identified by Precedence.
           */
         virtual uci::type::TaskTriggerPrecedenceType& getPrecedence()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Precedence to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Precedence
           */
         virtual void setPrecedence(const uci::type::TaskTriggerPrecedenceType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Precedence exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Precedence is emabled or not
           */
         virtual bool hasPrecedence() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Precedence
           *
           * @param type = uci::type::accessorType::taskTriggerPrecedenceType This Accessor's accessor type
           */
         virtual void enablePrecedence(uci::base::accessorType::AccessorType type = uci::type::accessorType::taskTriggerPrecedenceType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Precedence */
         virtual void clearPrecedence()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TaskType.
           *
           * @return The acecssor that provides access to the complex content that is identified by TaskType.
           */
         virtual const uci::type::TaskTriggerTaskType& getTaskType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TaskType.
           *
           * @return The acecssor that provides access to the complex content that is identified by TaskType.
           */
         virtual uci::type::TaskTriggerTaskType& getTaskType()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TaskType to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TaskType
           */
         virtual void setTaskType(const uci::type::TaskTriggerTaskType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TaskType exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TaskType is emabled or not
           */
         virtual bool hasTaskType() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TaskType
           *
           * @param type = uci::type::accessorType::taskTriggerTaskType This Accessor's accessor type
           */
         virtual void enableTaskType(uci::base::accessorType::AccessorType type = uci::type::accessorType::taskTriggerTaskType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TaskType */
         virtual void clearTaskType()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TaskTriggerDataType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TaskTriggerDataType to copy from
           */
         TaskTriggerDataType(const TaskTriggerDataType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TaskTriggerDataType to the contents of the TaskTriggerDataType on
           * the right hand side (rhs) of the assignment operator.TaskTriggerDataType [only available to derived classes]
           *
           * @param rhs The TaskTriggerDataType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::TaskTriggerDataType
           * @return A constant reference to this TaskTriggerDataType.
           */
         const TaskTriggerDataType& operator=(const TaskTriggerDataType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TaskTriggerDataType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TaskTriggerDataType_h

