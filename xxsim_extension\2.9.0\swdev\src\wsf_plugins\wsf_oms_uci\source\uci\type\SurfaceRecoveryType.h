// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SurfaceRecoveryType_h
#define Uci__Type__SurfaceRecoveryType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__FlightDeckStatusEnum_h)
# include "uci/type/FlightDeckStatusEnum.h"
#endif

#if !defined(Uci__Type__ApproachConditionStatusEnum_h)
# include "uci/type/ApproachConditionStatusEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Describes the status and approach condition of a flight deck. */
      class SurfaceRecoveryType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SurfaceRecoveryType()
         { }

         /** Returns this accessor's type constant, i.e. SurfaceRecoveryType
           *
           * @return This accessor's type constant, i.e. SurfaceRecoveryType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::surfaceRecoveryType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SurfaceRecoveryType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the FlightDeckStatus.
           *
           * @return The value of the enumeration identified by FlightDeckStatus.
           */
         virtual const uci::type::FlightDeckStatusEnum& getFlightDeckStatus() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the FlightDeckStatus.
           *
           * @return The value of the enumeration identified by FlightDeckStatus.
           */
         virtual uci::type::FlightDeckStatusEnum& getFlightDeckStatus()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the FlightDeckStatus.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setFlightDeckStatus(const uci::type::FlightDeckStatusEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the FlightDeckStatus.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setFlightDeckStatus(uci::type::FlightDeckStatusEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the ApproachConditionStatus.
           *
           * @return The value of the enumeration identified by ApproachConditionStatus.
           */
         virtual const uci::type::ApproachConditionStatusEnum& getApproachConditionStatus() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the ApproachConditionStatus.
           *
           * @return The value of the enumeration identified by ApproachConditionStatus.
           */
         virtual uci::type::ApproachConditionStatusEnum& getApproachConditionStatus()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the ApproachConditionStatus.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setApproachConditionStatus(const uci::type::ApproachConditionStatusEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the ApproachConditionStatus.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setApproachConditionStatus(uci::type::ApproachConditionStatusEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SurfaceRecoveryType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SurfaceRecoveryType to copy from
           */
         SurfaceRecoveryType(const SurfaceRecoveryType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SurfaceRecoveryType to the contents of the SurfaceRecoveryType on
           * the right hand side (rhs) of the assignment operator.SurfaceRecoveryType [only available to derived classes]
           *
           * @param rhs The SurfaceRecoveryType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::SurfaceRecoveryType
           * @return A constant reference to this SurfaceRecoveryType.
           */
         const SurfaceRecoveryType& operator=(const SurfaceRecoveryType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SurfaceRecoveryType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SurfaceRecoveryType_h

