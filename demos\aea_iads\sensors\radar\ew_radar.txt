# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
# * * ************************************** * *
# *   ****** Demonstration input file ******   *
# *   ******      UNCLASSIFIED        ******   *
# * * ************************************** * *

antenna_pattern EW_RADAR_ANTENNA
    cosecant_squared_pattern 
      peak_gain 20 db
      minimum_gain -10.0 db
      azimuth_beamwidth 10 deg
      elevation_beamwidth 20 deg
      minimum_elevation_for_peak_gain -2 deg
      elevation_of_peak/csc2_boundary 7 deg
      maximum_elevation_for_csc2 10 deg
end_antenna_pattern

antenna_pattern EW_RADAR_AUX_ANTENNA
  uniform_pattern
     peak_gain  0 dB
     beamwidth 360 deg
  end_uniform_pattern
end_antenna_pattern

# **********************************************

electronic_warfare EW_RADAR_EP WSF_ELECTRONIC_PROTECT
   technique sidelobe_canceling WSF_EP_TECHNIQUE
      externally_controlled
      mitigated_technique_classes
         noise_jamming
      end_mitigated_technique_classes
      effect sidelobe_cancelation WSF_SLC_EFFECT
         number_canceler_channels 2
         cancelation_lock_ratio   2 dB
         auxiliary_antenna_pattern EW_RADAR_AUX_ANTENNA
         cancelation_ratios
            jammer_to_noise         0.0 db   0.0 db
            jammer_to_noise        15.0 db  10.0 db
            jammer_to_noise        30.0 db  20.0 db
            saturation_ratio       10.0 dB
         end_cancelation_ratios
        receiver_noise_power_gain 3.0 db
      end_effect
   end_technique

   technique sidelobe_blanking WSF_EP_TECHNIQUE
      externally_controlled
      mitigated_technique_classes
         false_target_jamming
         pulse_jamming
      end_mitigated_technique_classes
      effect sidelobe_blanking WSF_SLB_EFFECT
         auxiliary_antenna_pattern EW_RADAR_AUX_ANTENNA
         blanking_threshold    0 dB
         saturation_effect
           duty_cycle_limit_effect
           duty_cycle_limit    0.50
        end_saturation_effect
        target_blanking_effect
           duty_cycle_probability_effect
        end_target_blanking_effect
      end_effect
   end_technique

   technique frequency_agility WSF_EP_TECHNIQUE
      mitigated_technique_classes
         noise_jamming
         pulse_jamming
      end_mitigated_technique_classes
      effect frequency_agility WSF_AGILITY_EFFECT
         agility_type
            frequency_changing
         end_agility_type
      end_effect
   end_technique
end_electronic_warfare

# **********************************************

sensor EW_RADAR WSF_RADAR_SENSOR
   one_m2_detect_range            100.0 nm

   antenna_height                 6.0 m

   frame_time                     20.0 sec

   scan_mode                      azimuth
   azimuth_scan_limits            -180 deg 180 deg

   false_target_screener SCREENER WSF_FT_SCREENER
      track_capacity     500
      plot_capacity      100
      transfer_capacity  100
      allow_track_reporting_when_flooded false
      force_target_tracks
         range_force  inside_blip_range
         #sector_force 45 deg 3
      end_force_target_tracks
   end_false_target_screener

   transmitter
      antenna_pattern             EW_RADAR_ANTENNA
      polarization                horizontal
      beam_tilt                   10.0 deg
      power                       1000.0 kw
      frequency                   200 mhz
      frequency_channels          150 mhz 10 mhz 200 mhz
      pulse_width                   50 usec
      pulse_repetition_interval   1250 usec
      internal_loss               2 db
   end_transmitter

   receiver
      antenna_pattern             EW_RADAR_ANTENNA
      beam_tilt                   10.0 deg
      bandwidth                   2.0 mhz
      noise_power                 -160 dBw  # will be calibrated for 1 m^2
      internal_loss               7 dB

      electronic_protect EW_RADAR_EP end_electronic_protect
   end_receiver

   compute_measurement_errors true

   continuous_jamming_perception_threshold 15 db
   pulsed_jamming_perception_threshold     15 db

   swerling_case                  1
   probability_of_false_alarm     1.0e-6
   required_pd                    0.5
   number_of_pulses_integrated    32

   hits_to_establish_track        3 5
   hits_to_maintain_track         1 3

   track_quality 0.5         # This sensor produces a relatively low fidelity track.

   reports_range
   reports_bearing
   reports_iff
end_sensor
