// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SubCapabilitySelectionType_h
#define Uci__Type__SubCapabilitySelectionType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__ESM_SubCapabilityEnum_h)
# include "uci/type/ESM_SubCapabilityEnum.h"
#endif

#if !defined(Uci__Type__SubCapabilityDetailsType_h)
# include "uci/type/SubCapabilityDetailsType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SubCapabilitySelectionType sequence accessor class */
      class SubCapabilitySelectionType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SubCapabilitySelectionType()
         { }

         /** Returns this accessor's type constant, i.e. SubCapabilitySelectionType
           *
           * @return This accessor's type constant, i.e. SubCapabilitySelectionType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::subCapabilitySelectionType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SubCapabilitySelectionType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates a specific ESM SubCapability being invoked by the command. If omitted, all SubCapabilities of the
           * CapabilityID are to be invoked. If specified, only those SubCapabilities specified are required in order to satisfy
           * the command. See enumeration annotations for further details. This element is required, in order to qualify the
           * affected SubCapability for the ResourceAccessPriority (and other antenna/resource attributes) in the sibling
           * SubCapabilityDetails. [Minimum occurrences: 0] [Maximum occurrences: 12]
           */
         typedef uci::base::BoundedList<uci::type::ESM_SubCapabilityEnum, uci::type::accessorType::eSM_SubCapabilityEnum> SubCapability;

         /** indicates the details required for a subcapability. at least one of the data sets need to be populated. [Maximum
           * occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SubCapabilityDetailsType, uci::type::accessorType::subCapabilityDetailsType> SubCapabilityDetails;

         /** Returns the bounded list that is identified by the SubCapability.
           *
           * @return The bounded list identified by SubCapability.
           */
         virtual const uci::type::SubCapabilitySelectionType::SubCapability& getSubCapability() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SubCapability.
           *
           * @return The bounded list identified by SubCapability.
           */
         virtual uci::type::SubCapabilitySelectionType::SubCapability& getSubCapability()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SubCapability.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSubCapability(const uci::type::SubCapabilitySelectionType::SubCapability& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SubCapabilityDetails.
           *
           * @return The bounded list identified by SubCapabilityDetails.
           */
         virtual const uci::type::SubCapabilitySelectionType::SubCapabilityDetails& getSubCapabilityDetails() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SubCapabilityDetails.
           *
           * @return The bounded list identified by SubCapabilityDetails.
           */
         virtual uci::type::SubCapabilitySelectionType::SubCapabilityDetails& getSubCapabilityDetails()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SubCapabilityDetails.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSubCapabilityDetails(const uci::type::SubCapabilitySelectionType::SubCapabilityDetails& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SubCapabilitySelectionType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SubCapabilitySelectionType to copy from
           */
         SubCapabilitySelectionType(const SubCapabilitySelectionType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SubCapabilitySelectionType to the contents of the
           * SubCapabilitySelectionType on the right hand side (rhs) of the assignment operator.SubCapabilitySelectionType [only
           * available to derived classes]
           *
           * @param rhs The SubCapabilitySelectionType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SubCapabilitySelectionType
           * @return A constant reference to this SubCapabilitySelectionType.
           */
         const SubCapabilitySelectionType& operator=(const SubCapabilitySelectionType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SubCapabilitySelectionType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SubCapabilitySelectionType_h

