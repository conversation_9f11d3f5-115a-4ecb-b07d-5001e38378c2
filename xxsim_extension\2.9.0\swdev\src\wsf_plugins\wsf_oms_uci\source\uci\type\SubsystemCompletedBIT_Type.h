// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SubsystemCompletedBIT_Type_h
#define Uci__Type__SubsystemCompletedBIT_Type_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__BIT_ID_Type_h)
# include "uci/type/BIT_ID_Type.h"
#endif

#if !defined(Uci__Type__DateTimeType_h)
# include "uci/type/DateTimeType.h"
#endif

#if !defined(Uci__Type__SubsystemBIT_ResultEnum_h)
# include "uci/type/SubsystemBIT_ResultEnum.h"
#endif

#if !defined(Uci__Type__VisibleString256Type_h)
# include "uci/type/VisibleString256Type.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SubsystemCompletedBIT_ItemType_h)
# include "uci/type/SubsystemCompletedBIT_ItemType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SubsystemCompletedBIT_Type sequence accessor class */
      class SubsystemCompletedBIT_Type : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SubsystemCompletedBIT_Type()
         { }

         /** Returns this accessor's type constant, i.e. SubsystemCompletedBIT_Type
           *
           * @return This accessor's type constant, i.e. SubsystemCompletedBIT_Type
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::subsystemCompletedBIT_Type;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SubsystemCompletedBIT_Type& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates the results of an item tested by the BIT. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SubsystemCompletedBIT_ItemType, uci::type::accessorType::subsystemCompletedBIT_ItemType> BIT_Item;

         /** Returns the accessor that provides access to the complex content that is identified by the BIT_ID.
           *
           * @return The acecssor that provides access to the complex content that is identified by BIT_ID.
           */
         virtual const uci::type::BIT_ID_Type& getBIT_ID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BIT_ID.
           *
           * @return The acecssor that provides access to the complex content that is identified by BIT_ID.
           */
         virtual uci::type::BIT_ID_Type& getBIT_ID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the BIT_ID to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by BIT_ID
           */
         virtual void setBIT_ID(const uci::type::BIT_ID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Timetag.
           *
           * @return The value of the simple primitive data type identified by Timetag.
           */
         virtual uci::type::DateTimeTypeValue getTimetag() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Timetag.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setTimetag(uci::type::DateTimeTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Result.
           *
           * @return The value of the enumeration identified by Result.
           */
         virtual const uci::type::SubsystemBIT_ResultEnum& getResult() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Result.
           *
           * @return The value of the enumeration identified by Result.
           */
         virtual uci::type::SubsystemBIT_ResultEnum& getResult()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Result.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setResult(const uci::type::SubsystemBIT_ResultEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Result.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setResult(uci::type::SubsystemBIT_ResultEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the FailReason.
           *
           * @return The value of the string data type identified by FailReason.
           */
         virtual const uci::type::VisibleString256Type& getFailReason() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the FailReason.
           *
           * @return The value of the string data type identified by FailReason.
           */
         virtual uci::type::VisibleString256Type& getFailReason()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setFailReason(const uci::type::VisibleString256Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setFailReason(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setFailReason(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by FailReason exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by FailReason is emabled or not
           */
         virtual bool hasFailReason() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by FailReason
           *
           * @param type = uci::type::accessorType::visibleString256Type This Accessor's accessor type
           */
         virtual void enableFailReason(uci::base::accessorType::AccessorType type = uci::type::accessorType::visibleString256Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by FailReason */
         virtual void clearFailReason()
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the BIT_Item.
           *
           * @return The bounded list identified by BIT_Item.
           */
         virtual const uci::type::SubsystemCompletedBIT_Type::BIT_Item& getBIT_Item() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the BIT_Item.
           *
           * @return The bounded list identified by BIT_Item.
           */
         virtual uci::type::SubsystemCompletedBIT_Type::BIT_Item& getBIT_Item()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the BIT_Item.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setBIT_Item(const uci::type::SubsystemCompletedBIT_Type::BIT_Item& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SubsystemCompletedBIT_Type()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SubsystemCompletedBIT_Type to copy from
           */
         SubsystemCompletedBIT_Type(const SubsystemCompletedBIT_Type& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SubsystemCompletedBIT_Type to the contents of the
           * SubsystemCompletedBIT_Type on the right hand side (rhs) of the assignment operator.SubsystemCompletedBIT_Type [only
           * available to derived classes]
           *
           * @param rhs The SubsystemCompletedBIT_Type on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SubsystemCompletedBIT_Type
           * @return A constant reference to this SubsystemCompletedBIT_Type.
           */
         const SubsystemCompletedBIT_Type& operator=(const SubsystemCompletedBIT_Type& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SubsystemCompletedBIT_Type


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SubsystemCompletedBIT_Type_h

