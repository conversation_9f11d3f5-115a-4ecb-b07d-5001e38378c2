// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__ThreatExposureType_h
#define Uci__Type__ThreatExposureType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__EntityID_Type_h)
# include "uci/type/EntityID_Type.h"
#endif

#if !defined(Uci__Type__DurationType_h)
# include "uci/type/DurationType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__DateTimeRangeType_h)
# include "uci/type/DateTimeRangeType.h"
#endif

#if !defined(Uci__Type__ThreatExposureProbabilityType_h)
# include "uci/type/ThreatExposureProbabilityType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the ThreatExposureType sequence accessor class */
      class ThreatExposureType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~ThreatExposureType()
         { }

         /** Returns this accessor's type constant, i.e. ThreatExposureType
           *
           * @return This accessor's type constant, i.e. ThreatExposureType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::threatExposureType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const ThreatExposureType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Provides a method for defining exposure events along a route path or segment into time segments against a particular
           * threat. An exposure event is defined as a time ranges during which exposure to a particular threat occurs. [Minimum
           * occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::DateTimeRangeType, uci::type::accessorType::dateTimeRangeType> ExposureEvent;

         /** Provides a method for breaking the exposure probabilities along a route path or segment into time segments against a
           * particular threat. [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::ThreatExposureProbabilityType, uci::type::accessorType::threatExposureProbabilityType> ProbabilityMetrics;

         /** Returns the accessor that provides access to the complex content that is identified by the EntityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityID.
           */
         virtual const uci::type::EntityID_Type& getEntityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityID.
           */
         virtual uci::type::EntityID_Type& getEntityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the EntityID to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by EntityID
           */
         virtual void setEntityID(const uci::type::EntityID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the ExposureTime.
           *
           * @return The value of the simple primitive data type identified by ExposureTime.
           */
         virtual uci::type::DurationTypeValue getExposureTime() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the ExposureTime.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setExposureTime(uci::type::DurationTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ExposureEvent.
           *
           * @return The bounded list identified by ExposureEvent.
           */
         virtual const uci::type::ThreatExposureType::ExposureEvent& getExposureEvent() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ExposureEvent.
           *
           * @return The bounded list identified by ExposureEvent.
           */
         virtual uci::type::ThreatExposureType::ExposureEvent& getExposureEvent()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the ExposureEvent.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setExposureEvent(const uci::type::ThreatExposureType::ExposureEvent& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ProbabilityMetrics.
           *
           * @return The bounded list identified by ProbabilityMetrics.
           */
         virtual const uci::type::ThreatExposureType::ProbabilityMetrics& getProbabilityMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ProbabilityMetrics.
           *
           * @return The bounded list identified by ProbabilityMetrics.
           */
         virtual uci::type::ThreatExposureType::ProbabilityMetrics& getProbabilityMetrics()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the ProbabilityMetrics.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setProbabilityMetrics(const uci::type::ThreatExposureType::ProbabilityMetrics& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         ThreatExposureType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The ThreatExposureType to copy from
           */
         ThreatExposureType(const ThreatExposureType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this ThreatExposureType to the contents of the ThreatExposureType on
           * the right hand side (rhs) of the assignment operator.ThreatExposureType [only available to derived classes]
           *
           * @param rhs The ThreatExposureType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::ThreatExposureType
           * @return A constant reference to this ThreatExposureType.
           */
         const ThreatExposureType& operator=(const ThreatExposureType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // ThreatExposureType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__ThreatExposureType_h

