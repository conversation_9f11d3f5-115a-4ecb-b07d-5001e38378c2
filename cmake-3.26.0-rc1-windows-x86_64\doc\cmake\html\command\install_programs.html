
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>install_programs &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="install_targets" href="install_targets.html" />
    <link rel="prev" title="install_files" href="install_files.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="install_targets.html" title="install_targets"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="install_files.html" title="install_files"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">install_programs</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="install-programs">
<span id="command:install_programs"></span><h1>install_programs<a class="headerlink" href="#install-programs" title="Permalink to this heading">¶</a></h1>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.0: </span>Use the <span class="target" id="index-0-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(PROGRAMS)</span></code></a> command instead.</p>
</div>
<p>This command has been superseded by the <span class="target" id="index-1-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a> command.  It is
provided for compatibility with older CMake code.  The <code class="docutils literal notranslate"><span class="pre">FILES</span></code> form is
directly replaced by the <code class="docutils literal notranslate"><span class="pre">PROGRAMS</span></code> form of the <span class="target" id="index-2-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a>
command.  The regexp form can be expressed more clearly using the <code class="docutils literal notranslate"><span class="pre">GLOB</span></code>
form of the <span class="target" id="index-0-command:file"></span><a class="reference internal" href="file.html#command:file" title="file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file()</span></code></a> command.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install_programs(</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="nb">file1</span><span class="w"> </span><span class="nb">file2</span><span class="w"> </span><span class="p">[</span><span class="nb">file3</span><span class="w"> </span><span class="p">...]</span><span class="nf">)</span><span class="w"></span>
<span class="nf">install_programs(</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="no">FILES</span><span class="w"> </span><span class="nb">file1</span><span class="w"> </span><span class="p">[</span><span class="nb">file2</span><span class="w"> </span><span class="p">...]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Create rules to install the listed programs into the given directory.
Use the <code class="docutils literal notranslate"><span class="pre">FILES</span></code> argument to guarantee that the file list version of the
command will be used even when there is only one argument.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install_programs(</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="nb">regexp</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>In the second form any program in the current source directory that
matches the regular expression will be installed.</p>
<p>This command is intended to install programs that are not built by
cmake, such as shell scripts.  See the <code class="docutils literal notranslate"><span class="pre">TARGETS</span></code> form of the
<span class="target" id="index-3-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a> command to create installation rules for targets built
by cmake.</p>
<p>The directory <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> is relative to the installation prefix, which is
stored in the variable <span class="target" id="index-0-variable:CMAKE_INSTALL_PREFIX"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_PREFIX.html#variable:CMAKE_INSTALL_PREFIX" title="CMAKE_INSTALL_PREFIX"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_PREFIX</span></code></a>.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="install_files.html"
                          title="previous chapter">install_files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="install_targets.html"
                          title="next chapter">install_targets</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/install_programs.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="install_targets.html" title="install_targets"
             >next</a> |</li>
        <li class="right" >
          <a href="install_files.html" title="install_files"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">install_programs</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>