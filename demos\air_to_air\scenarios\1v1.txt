# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************

include_once platforms/lte_fighter.txt

route blue_route
   position 00:00:36.035n 01:47:21.156e altitude 30000 ft msl speed 900 ft/s
   position 00:00:36.035n 01:57:21.156e altitude 30000 ft msl speed 900 ft/s
end_route

route red_route
   position 00:00:36.035n 01:47:21.156w altitude 30000 ft msl speed 900 ft/s
   position 00:00:36.035n 01:57:21.156w altitude 30000 ft msl speed 900 ft/s
end_route

platform Talon_1_1 BLUE_FIGHTER
   side blue
   commander SELF
   command_chain IFLITE SELF
   command_chain ELEMENT SELF
   heading 90 deg
   position 00:05:00.00n 01:10:00.00w  altitude 30000.00 ft
#  edit fuel
#     bingo_quantity 4000 lbs
#  end_fuel
   script_variables
      START_TYPE = "route";
      # RISK_WPN = 0.0;
      MISSION_TYPE = "SWEEP";
      ENG_TGT = "";
      RPEAK_LOC ={1.0,0.8,0.95,1.0,1.0};
      ROUTE.Append("blue_route");
      WINCHESTER = {-1,-1,-1,-1,-1};
   end_script_variables

// if you desire no weapons to be launched
#  edit weapon fox3
#     quantity 0 
#  end_weapon 
#  edit weapon fox2
#     quantity 0 
#  end_weapon

   edit processor assessment
      enemy_side    red
      enemy_type    RED_FIGHTER
      friendly_type BLUE_FIGHTER
      flight_id     1
      id_flag       1
      mission_task  SWEEP      
   end_processor
   
end_platform

platform red_1 RED_FIGHTER 
   side red
   commander SELF
   command_chain IFLITE SELF
   command_chain ELEMENT SELF
   heading 270 deg
   position 00:00:00.00n 01:10:00.00e  altitude 30000.00 ft

   script_variables
      START_TYPE = "route";
      # RISK_WPN = 0.0;
      MISSION_TYPE = "SWEEP";
      ENG_TGT = "";
      RPEAK_LOC ={1.0,0.8,0.95,1.0,1.0};
      ROUTE.Append("red_route");
      WINCHESTER = {-1,-1,-1,-1,-1};
   end_script_variables

// if you desire no weapons to be launched
#  edit weapon fox3
#     quantity 0 
#  end_weapon 
#  edit weapon fox2
#     quantity 0 
#  end_weapon
   
   edit processor assessment
      enemy_side    blue
      enemy_type    BLUE_FIGHTER
      friendly_type RED_FIGHTER
      flight_id     202
      id_flag       2
      mission_task  SWEEP 
   end_processor
   
end_platform