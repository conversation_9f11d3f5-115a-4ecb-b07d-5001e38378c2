// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WorkingSOB_MDT_h
#define Uci__Type__WorkingSOB_MDT_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__WorkingSOB_ID_Type_h)
# include "uci/type/WorkingSOB_ID_Type.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__OrbitAltitudeEnum_h)
# include "uci/type/OrbitAltitudeEnum.h"
#endif

#if !defined(Uci__Type__OrderOfBattleTimestampsType_h)
# include "uci/type/OrderOfBattleTimestampsType.h"
#endif

#if !defined(Uci__Type__SOB_SatelliteRecordType_h)
# include "uci/type/SOB_SatelliteRecordType.h"
#endif

#if !defined(Uci__Type__SOB_C2_RecordType_h)
# include "uci/type/SOB_C2_RecordType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WorkingSOB_MDT sequence accessor class */
      class WorkingSOB_MDT : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WorkingSOB_MDT()
         { }

         /** Returns this accessor's type constant, i.e. WorkingSOB_MDT
           *
           * @return This accessor's type constant, i.e. WorkingSOB_MDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::workingSOB_MDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WorkingSOB_MDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates the name of an orbit altitude range. [Minimum occurrences: 0] [Maximum occurrences: 5] */
         typedef uci::base::BoundedList<uci::type::OrbitAltitudeEnum, uci::type::accessorType::orbitAltitudeEnum> Orbit;

         /** Indicates a subset of satellite records from the catalog. This subset could include updates to the records that a
           * planning service would need. This record describes the characteristics of a particular satellite. [Maximum
           * occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SOB_SatelliteRecordType, uci::type::accessorType::sOB_SatelliteRecordType> SatelliteRecord;

         /** Indicates the attributes of the C2 node associated with the satellite(s). An example of a C2 node would be a ground
           * station for the satellite(s). [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SOB_C2_RecordType, uci::type::accessorType::sOB_C2_RecordType> C2_Record;

         /** Returns the accessor that provides access to the complex content that is identified by the WorkingSOB_ID.
           *
           * @return The acecssor that provides access to the complex content that is identified by WorkingSOB_ID.
           */
         virtual const uci::type::WorkingSOB_ID_Type& getWorkingSOB_ID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WorkingSOB_ID.
           *
           * @return The acecssor that provides access to the complex content that is identified by WorkingSOB_ID.
           */
         virtual uci::type::WorkingSOB_ID_Type& getWorkingSOB_ID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WorkingSOB_ID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WorkingSOB_ID
           */
         virtual void setWorkingSOB_ID(const uci::type::WorkingSOB_ID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Orbit.
           *
           * @return The bounded list identified by Orbit.
           */
         virtual const uci::type::WorkingSOB_MDT::Orbit& getOrbit() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Orbit.
           *
           * @return The bounded list identified by Orbit.
           */
         virtual uci::type::WorkingSOB_MDT::Orbit& getOrbit()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Orbit.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setOrbit(const uci::type::WorkingSOB_MDT::Orbit& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Timestamps.
           *
           * @return The acecssor that provides access to the complex content that is identified by Timestamps.
           */
         virtual const uci::type::OrderOfBattleTimestampsType& getTimestamps() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Timestamps.
           *
           * @return The acecssor that provides access to the complex content that is identified by Timestamps.
           */
         virtual uci::type::OrderOfBattleTimestampsType& getTimestamps()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Timestamps to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Timestamps
           */
         virtual void setTimestamps(const uci::type::OrderOfBattleTimestampsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SatelliteRecord.
           *
           * @return The bounded list identified by SatelliteRecord.
           */
         virtual const uci::type::WorkingSOB_MDT::SatelliteRecord& getSatelliteRecord() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SatelliteRecord.
           *
           * @return The bounded list identified by SatelliteRecord.
           */
         virtual uci::type::WorkingSOB_MDT::SatelliteRecord& getSatelliteRecord()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SatelliteRecord.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSatelliteRecord(const uci::type::WorkingSOB_MDT::SatelliteRecord& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the C2_Record.
           *
           * @return The bounded list identified by C2_Record.
           */
         virtual const uci::type::WorkingSOB_MDT::C2_Record& getC2_Record() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the C2_Record.
           *
           * @return The bounded list identified by C2_Record.
           */
         virtual uci::type::WorkingSOB_MDT::C2_Record& getC2_Record()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the C2_Record.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setC2_Record(const uci::type::WorkingSOB_MDT::C2_Record& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WorkingSOB_MDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WorkingSOB_MDT to copy from
           */
         WorkingSOB_MDT(const WorkingSOB_MDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WorkingSOB_MDT to the contents of the WorkingSOB_MDT on the right
           * hand side (rhs) of the assignment operator.WorkingSOB_MDT [only available to derived classes]
           *
           * @param rhs The WorkingSOB_MDT on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::WorkingSOB_MDT
           * @return A constant reference to this WorkingSOB_MDT.
           */
         const WorkingSOB_MDT& operator=(const WorkingSOB_MDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WorkingSOB_MDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WorkingSOB_MDT_h

