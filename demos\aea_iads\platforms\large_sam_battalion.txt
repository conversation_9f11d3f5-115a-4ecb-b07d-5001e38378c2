# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
# * * ************************************** * *
# *   ****** Demonstration input file ******   *
# *   ******      UNCLASSIFIED        ******   *
# * * ************************************** * *

include_once processors/red_sam_battery_tactics.txt
include_once platforms/large_sam_ttr.txt
include_once platforms/large_sam_launcher.txt

processor LARGE_SAM_BATTALION_TASK_MGR RED_SAM_BATTERY_TASK_MGR
   number_of_servers            2

   #script_debug_writes on
   #show_state_transitions
   #show_task_messages

   on_initialize
      SENSOR_NAME             = "ttr";
      REQUIRED_TRACK_QUALITY  = 0.75;
      SENSOR_ACQUIRE_MODE     = "ACQUIRE";
      SENSOR_TRACK_MODE       = "TRACK";
      WEAPON_NAME             = "sam";
      ENVELOPE                = "full_kinematic";
      SALVO_SIZE              = 2;
   end_on_initialize

# -------------------------------------------------------------------------------

   // on task assign / cancel, command the acquisition radar to emcon-on and
   // emcon-off, respectively
   script void EvaluateOperatingLevel()
      if ((TasksReceived() > 0) ||
          (OperatingLevelFor("ENGAGE") > 0))
      {
         foreach (WsfPlatform sub in PLATFORM.Subordinates())
         {
            string subType = sub.Type();
            if (subType == "ACQ_RADAR")
            {
               if (OperatingLevelFor("ENGAGE", sub) < 1)
               {
                  ChangeOperatingLevel("ENGAGE", 1, sub);
                  break;
               }
            }
         }
      }
      else
      {
         foreach (WsfPlatform sub in PLATFORM.Subordinates())
         {
            string subType = sub.Type();
            if (subType == "ACQ_RADAR")
            {
               if (OperatingLevelFor("ENGAGE", sub) > 0)
               {
                  ChangeOperatingLevel("ENGAGE", 0, sub);
                  break;
               }
            }
         }
      }
   end_script

   script void on_task_assign(WsfTask aTask, WsfTrack aTrack)
      EvaluateOperatingLevel();
   end_script

   script void on_task_cancel(WsfTask aTask)
      EvaluateOperatingLevel();
   end_script
end_processor

platform_type LARGE_SAM_BATTALION WSF_PLATFORM
   icon TWIN_BOX
   category ENGAGEMENT

   infrared_signature    VEHICLE_INFRARED_SIGNATURE
   optical_signature     VEHICLE_OPTICAL_SIGNATURE
   radar_signature       VEHICLE_RADAR_SIGNATURE

   include processors/commander_track_manager$(TRACKER_TYPE).txt

   comm cmdr_net RED_DATALINK
      network_name <local:slave>
      internal_link data_mgr
      internal_link task_mgr
   end_comm

   comm sub_net RED_DATALINK
      network_name <local:master>
      internal_link data_mgr
      internal_link task_mgr
   end_comm

   zone full_kinematic
      circular
         maximum_altitude 30 km
         maximum_radius   25 nm
   end_zone

   processor data_mgr WSF_TRACK_PROCESSOR
      purge_interval 30 seconds
   end_processor

   processor task_mgr LARGE_SAM_BATTALION_TASK_MGR
   end_processor
/*
   # ---------------------------------------------
   # Draw track to false targets and real targets
   # ---------------------------------------------
   script_variables
      # Create a WsfDraw object.
      WsfDraw draw = WsfDraw();
   end_script_variables

   execute at_interval_of 1.0 s
      foreach (WsfLocalTrack track in MasterTrackList())
      {
         if (track.IsValid() && track.SensorTypeContributor("TTR_RADAR"))
         {
            # use the track ID as a unique draw ID
            # a track icon and a line to the track icon
            # is drawn at each track update
            string trackId = track.TrackId().ToString();
            draw.SetId(trackId);
            draw.SetDuration(1.0); # this is faster than calling erase every time

            if (track.IsFalseTarget())
            {
               draw.SetColor(0,1,0);
            }
            else
            {
               draw.SetColor(1,0,0);

            }
            draw.BeginIcons(track.Heading(), "Wedge");
            draw.Vertex(track.ReportedLocation());
            draw.End();

            # Draw a line to the track
            draw.SetLineStyle("dashed");
            draw.BeginLines();
            draw.Vertex(PLATFORM);
            draw.Vertex(track.ReportedLocation());
            draw.End();
         }
      }
   end_execute
*/
end_platform_type
