// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TxBeamType_h
#define Uci__Type__TxBeamType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__BeamID_Type_h)
# include "uci/type/BeamID_Type.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

#if !defined(Uci__Type__MilliwattPowerRatioType_h)
# include "uci/type/MilliwattPowerRatioType.h"
#endif

#if !defined(Uci__Type__AnglePositiveType_h)
# include "uci/type/AnglePositiveType.h"
#endif

#if !defined(Uci__Type__Point3D_Type_h)
# include "uci/type/Point3D_Type.h"
#endif

#if !defined(Uci__Type__DateTimeRangeBeginType_h)
# include "uci/type/DateTimeRangeBeginType.h"
#endif

#if !defined(Uci__Type__EA_PointingEnum_h)
# include "uci/type/EA_PointingEnum.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__EA_EmissionType_h)
# include "uci/type/EA_EmissionType.h"
#endif

#if !defined(Uci__Type__BeamPointingReferenceType_h)
# include "uci/type/BeamPointingReferenceType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TxBeamType sequence accessor class */
      class TxBeamType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TxBeamType()
         { }

         /** Returns this accessor's type constant, i.e. TxBeamType
           *
           * @return This accessor's type constant, i.e. TxBeamType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::txBeamType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TxBeamType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates a jamming emission of the Beam. [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::type::EA_EmissionType, uci::type::accessorType::eA_EmissionType> Emission;

         /** Returns the accessor that provides access to the complex content that is identified by the BeamID.
           *
           * @return The acecssor that provides access to the complex content that is identified by BeamID.
           */
         virtual const uci::type::BeamID_Type& getBeamID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BeamID.
           *
           * @return The acecssor that provides access to the complex content that is identified by BeamID.
           */
         virtual uci::type::BeamID_Type& getBeamID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the BeamID to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by BeamID
           */
         virtual void setBeamID(const uci::type::BeamID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the DutyCycle.
           *
           * @return The value of the simple primitive data type identified by DutyCycle.
           */
         virtual uci::type::PercentTypeValue getDutyCycle() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the DutyCycle.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setDutyCycle(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by DutyCycle exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by DutyCycle is emabled or not
           */
         virtual bool hasDutyCycle() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by DutyCycle
           *
           * @param type = uci::type::accessorType::percentType This Accessor's accessor type
           */
         virtual void enableDutyCycle(uci::base::accessorType::AccessorType type = uci::type::accessorType::percentType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by DutyCycle */
         virtual void clearDutyCycle()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the EffectiveRadiatedPower.
           *
           * @return The value of the simple primitive data type identified by EffectiveRadiatedPower.
           */
         virtual uci::type::MilliwattPowerRatioTypeValue getEffectiveRadiatedPower() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the EffectiveRadiatedPower.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setEffectiveRadiatedPower(uci::type::MilliwattPowerRatioTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by EffectiveRadiatedPower exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by EffectiveRadiatedPower is emabled or not
           */
         virtual bool hasEffectiveRadiatedPower() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by EffectiveRadiatedPower
           *
           * @param type = uci::type::accessorType::milliwattPowerRatioType This Accessor's accessor type
           */
         virtual void enableEffectiveRadiatedPower(uci::base::accessorType::AccessorType type = uci::type::accessorType::milliwattPowerRatioType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by EffectiveRadiatedPower */
         virtual void clearEffectiveRadiatedPower()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the AzimuthWidth.
           *
           * @return The value of the simple primitive data type identified by AzimuthWidth.
           */
         virtual uci::type::AnglePositiveTypeValue getAzimuthWidth() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the AzimuthWidth.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setAzimuthWidth(uci::type::AnglePositiveTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the ElevationWidth.
           *
           * @return The value of the simple primitive data type identified by ElevationWidth.
           */
         virtual uci::type::AnglePositiveTypeValue getElevationWidth() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the ElevationWidth.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setElevationWidth(uci::type::AnglePositiveTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the StartPosition.
           *
           * @return The acecssor that provides access to the complex content that is identified by StartPosition.
           */
         virtual const uci::type::Point3D_Type& getStartPosition() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the StartPosition.
           *
           * @return The acecssor that provides access to the complex content that is identified by StartPosition.
           */
         virtual uci::type::Point3D_Type& getStartPosition()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the StartPosition to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by StartPosition
           */
         virtual void setStartPosition(const uci::type::Point3D_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by StartPosition exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by StartPosition is emabled or not
           */
         virtual bool hasStartPosition() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by StartPosition
           *
           * @param type = uci::type::accessorType::point3D_Type This Accessor's accessor type
           */
         virtual void enableStartPosition(uci::base::accessorType::AccessorType type = uci::type::accessorType::point3D_Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by StartPosition */
         virtual void clearStartPosition()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Time.
           *
           * @return The acecssor that provides access to the complex content that is identified by Time.
           */
         virtual const uci::type::DateTimeRangeBeginType& getTime() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Time.
           *
           * @return The acecssor that provides access to the complex content that is identified by Time.
           */
         virtual uci::type::DateTimeRangeBeginType& getTime()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Time to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Time
           */
         virtual void setTime(const uci::type::DateTimeRangeBeginType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the TrackingMode.
           *
           * @return The value of the enumeration identified by TrackingMode.
           */
         virtual const uci::type::EA_PointingEnum& getTrackingMode() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the TrackingMode.
           *
           * @return The value of the enumeration identified by TrackingMode.
           */
         virtual uci::type::EA_PointingEnum& getTrackingMode()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the TrackingMode.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setTrackingMode(const uci::type::EA_PointingEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the TrackingMode.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setTrackingMode(uci::type::EA_PointingEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Emission.
           *
           * @return The bounded list identified by Emission.
           */
         virtual const uci::type::TxBeamType::Emission& getEmission() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Emission.
           *
           * @return The bounded list identified by Emission.
           */
         virtual uci::type::TxBeamType::Emission& getEmission()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Emission.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setEmission(const uci::type::TxBeamType::Emission& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BeamPointingReference.
           *
           * @return The acecssor that provides access to the complex content that is identified by BeamPointingReference.
           */
         virtual const uci::type::BeamPointingReferenceType& getBeamPointingReference() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BeamPointingReference.
           *
           * @return The acecssor that provides access to the complex content that is identified by BeamPointingReference.
           */
         virtual uci::type::BeamPointingReferenceType& getBeamPointingReference()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the BeamPointingReference to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by BeamPointingReference
           */
         virtual void setBeamPointingReference(const uci::type::BeamPointingReferenceType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by BeamPointingReference exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by BeamPointingReference is emabled or not
           */
         virtual bool hasBeamPointingReference() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by BeamPointingReference
           *
           * @param type = uci::type::accessorType::beamPointingReferenceType This Accessor's accessor type
           */
         virtual void enableBeamPointingReference(uci::base::accessorType::AccessorType type = uci::type::accessorType::beamPointingReferenceType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by BeamPointingReference */
         virtual void clearBeamPointingReference()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TxBeamType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TxBeamType to copy from
           */
         TxBeamType(const TxBeamType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TxBeamType to the contents of the TxBeamType on the right hand
           * side (rhs) of the assignment operator.TxBeamType [only available to derived classes]
           *
           * @param rhs The TxBeamType on the right hand side (rhs) of the assignment operator whose contents are used to set the
           *      contents of this uci::type::TxBeamType
           * @return A constant reference to this TxBeamType.
           */
         const TxBeamType& operator=(const TxBeamType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TxBeamType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TxBeamType_h

