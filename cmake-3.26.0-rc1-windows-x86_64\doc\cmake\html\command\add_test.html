
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>add_test &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="aux_source_directory" href="aux_source_directory.html" />
    <link rel="prev" title="add_subdirectory" href="add_subdirectory.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="aux_source_directory.html" title="aux_source_directory"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="add_subdirectory.html" title="add_subdirectory"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">add_test</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="add-test">
<span id="command:add_test"></span><h1>add_test<a class="headerlink" href="#add-test" title="Permalink to this heading">¶</a></h1>
<p>Add a test to the project to be run by <span class="target" id="index-0-manual:ctest(1)"></span><a class="reference internal" href="../manual/ctest.1.html#manual:ctest(1)" title="ctest(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ctest(1)</span></code></a>.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_test(</span><span class="no">NAME</span><span class="w"> </span><span class="nv">&lt;name&gt;</span><span class="w"> </span><span class="no">COMMAND</span><span class="w"> </span><span class="nv">&lt;command&gt;</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;arg&gt;...</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">CONFIGURATIONS</span><span class="w"> </span><span class="nv">&lt;config&gt;...</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">WORKING_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="p">]</span><span class="w"></span>
<span class="w">         </span><span class="p">[</span><span class="no">COMMAND_EXPAND_LISTS</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Adds a test called <code class="docutils literal notranslate"><span class="pre">&lt;name&gt;</span></code>.  The test name may contain arbitrary
characters, expressed as a <a class="reference internal" href="../manual/cmake-language.7.html#quoted-argument"><span class="std std-ref">Quoted Argument</span></a> or <a class="reference internal" href="../manual/cmake-language.7.html#bracket-argument"><span class="std std-ref">Bracket Argument</span></a>
if necessary.  See policy <span class="target" id="index-0-policy:CMP0110"></span><a class="reference internal" href="../policy/CMP0110.html#policy:CMP0110" title="CMP0110"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0110</span></code></a>.  The options are:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">COMMAND</span></code></dt><dd><p>Specify the test command-line.  If <code class="docutils literal notranslate"><span class="pre">&lt;command&gt;</span></code> specifies an
executable target (created by <span class="target" id="index-0-command:add_executable"></span><a class="reference internal" href="add_executable.html#command:add_executable" title="add_executable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_executable()</span></code></a>) it will
automatically be replaced by the location of the executable created
at build time.</p>
<p>The command may be specified using
<span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CONFIGURATIONS</span></code></dt><dd><p>Restrict execution of the test only to the named configurations.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WORKING_DIRECTORY</span></code></dt><dd><p>Set the <span class="target" id="index-0-prop_test:WORKING_DIRECTORY"></span><a class="reference internal" href="../prop_test/WORKING_DIRECTORY.html#prop_test:WORKING_DIRECTORY" title="WORKING_DIRECTORY"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">WORKING_DIRECTORY</span></code></a> test property to
specify the working directory in which to execute the test.
If not specified the test will be run with the current working
directory set to the build directory corresponding to the
current source directory.</p>
<p>The working directory may be specified using
<span class="target" id="index-1-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMMAND_EXPAND_LISTS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>Lists in <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> arguments will be expanded, including those
created with
<span class="target" id="index-2-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.</p>
</dd>
</dl>
<p>The given test command is expected to exit with code <code class="docutils literal notranslate"><span class="pre">0</span></code> to pass and
non-zero to fail, or vice-versa if the <span class="target" id="index-0-prop_test:WILL_FAIL"></span><a class="reference internal" href="../prop_test/WILL_FAIL.html#prop_test:WILL_FAIL" title="WILL_FAIL"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">WILL_FAIL</span></code></a> test
property is set.  Any output written to stdout or stderr will be
captured by <span class="target" id="index-1-manual:ctest(1)"></span><a class="reference internal" href="../manual/ctest.1.html#manual:ctest(1)" title="ctest(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ctest(1)</span></code></a> but does not affect the pass/fail status
unless the <span class="target" id="index-0-prop_test:PASS_REGULAR_EXPRESSION"></span><a class="reference internal" href="../prop_test/PASS_REGULAR_EXPRESSION.html#prop_test:PASS_REGULAR_EXPRESSION" title="PASS_REGULAR_EXPRESSION"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">PASS_REGULAR_EXPRESSION</span></code></a>,
<span class="target" id="index-0-prop_test:FAIL_REGULAR_EXPRESSION"></span><a class="reference internal" href="../prop_test/FAIL_REGULAR_EXPRESSION.html#prop_test:FAIL_REGULAR_EXPRESSION" title="FAIL_REGULAR_EXPRESSION"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">FAIL_REGULAR_EXPRESSION</span></code></a> or
<span class="target" id="index-0-prop_test:SKIP_REGULAR_EXPRESSION"></span><a class="reference internal" href="../prop_test/SKIP_REGULAR_EXPRESSION.html#prop_test:SKIP_REGULAR_EXPRESSION" title="SKIP_REGULAR_EXPRESSION"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">SKIP_REGULAR_EXPRESSION</span></code></a> test property is used.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.16: </span>Added <span class="target" id="index-1-prop_test:SKIP_REGULAR_EXPRESSION"></span><a class="reference internal" href="../prop_test/SKIP_REGULAR_EXPRESSION.html#prop_test:SKIP_REGULAR_EXPRESSION" title="SKIP_REGULAR_EXPRESSION"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">SKIP_REGULAR_EXPRESSION</span></code></a> property.</p>
</div>
<p>Tests added with the <code class="docutils literal notranslate"><span class="pre">add_test(NAME)</span></code> signature support using
<span class="target" id="index-3-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>
in test properties set by <span class="target" id="index-0-command:set_property"></span><a class="reference internal" href="set_property.html#command:set_property" title="set_property"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set_property(TEST)</span></code></a> or
<span class="target" id="index-0-command:set_tests_properties"></span><a class="reference internal" href="set_tests_properties.html#command:set_tests_properties" title="set_tests_properties"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set_tests_properties()</span></code></a>.</p>
<p>Example usage:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_test(</span><span class="no">NAME</span><span class="w"> </span><span class="nb">mytest</span><span class="w"></span>
<span class="w">         </span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">testDriver</span><span class="w"> </span><span class="p">--</span><span class="nb">config</span><span class="w"> </span><span class="o">$&lt;</span><span class="no">CONFIG</span><span class="o">&gt;</span><span class="w"></span>
<span class="w">                            </span><span class="p">--</span><span class="nb">exe</span><span class="w"> </span><span class="o">$&lt;</span><span class="no">TARGET_FILE</span><span class="o">:</span><span class="nb">myexe</span><span class="o">&gt;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>This creates a test <code class="docutils literal notranslate"><span class="pre">mytest</span></code> whose command runs a <code class="docutils literal notranslate"><span class="pre">testDriver</span></code> tool
passing the configuration name and the full path to the executable
file produced by target <code class="docutils literal notranslate"><span class="pre">myexe</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>CMake will generate tests only if the <span class="target" id="index-0-command:enable_testing"></span><a class="reference internal" href="enable_testing.html#command:enable_testing" title="enable_testing"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">enable_testing()</span></code></a>
command has been invoked.  The <span class="target" id="index-0-module:CTest"></span><a class="reference internal" href="../module/CTest.html#module:CTest" title="CTest"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CTest</span></code></a> module invokes the
command automatically unless the <code class="docutils literal notranslate"><span class="pre">BUILD_TESTING</span></code> option is turned
<code class="docutils literal notranslate"><span class="pre">OFF</span></code>.</p>
</div>
<hr class="docutils" />
<p>This command also supports a simpler, but less flexible, signature:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_test(</span><span class="nv">&lt;name&gt;</span><span class="w"> </span><span class="nv">&lt;command&gt;</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;arg&gt;...</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Add a test called <code class="docutils literal notranslate"><span class="pre">&lt;name&gt;</span></code> with the given command-line.</p>
<p>Unlike the above <code class="docutils literal notranslate"><span class="pre">NAME</span></code> signature, target names are not supported
in the command-line.  Furthermore, tests added with this signature do not
support <span class="target" id="index-4-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>
in the command-line or test properties.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="add_subdirectory.html"
                          title="previous chapter">add_subdirectory</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="aux_source_directory.html"
                          title="next chapter">aux_source_directory</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/add_test.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="aux_source_directory.html" title="aux_source_directory"
             >next</a> |</li>
        <li class="right" >
          <a href="add_subdirectory.html" title="add_subdirectory"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">add_test</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>