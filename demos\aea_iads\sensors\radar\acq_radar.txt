# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
# * * ************************************** * *
# *   ****** Demonstration input file ******   *
# *   ******      UNCLASSIFIED        ******   *
# * * ************************************** * *

antenna_pattern ACQ_RADAR_ANTENNA
  rectangular_pattern
    peak_gain              15 dB
    minimum_gain           -10.0 db
    azimuth_beamwidth      5 deg
    elevation_beamwidth    5 deg
end_antenna_pattern

# **********************************************

electronic_warfare ACQ_RADAR_EP WSF_ELECTRONIC_PROTECT
   technique pulse_suppression WSF_EP_TECHNIQUE
      mitigated_techniques
         random_pulse_jamming
      end_mitigated_techniques
      effect ps_effect WSF_PULSE_SUPPRESS_EFFECT
         reject pulses 0.05
      end_effect
   end_technique
end_electronic_warfare

# **********************************************

sensor ACQ_RADAR WSF_RADAR_SENSOR

   one_m2_detect_range            50.0 nm

   antenna_height                 5.0 m

   frame_time                     10.0 sec

   scan_mode                      azimuth_and_elevation
   azimuth_scan_limits            -180 deg 180 deg
   elevation_scan_limits          0.0 deg 50.0 deg

   transmitter
      antenna_pattern             ACQ_RADAR_ANTENNA
      power                       1000.0 kw
      frequency                   3000 mhz
      internal_loss               2 db
      pulse_width                 35 usec
      pulse_repetition_interval   350 usec
   end_transmitter

   receiver
      antenna_pattern             ACQ_RADAR_ANTENNA
      bandwidth                   2.0 mhz
      noise_power                 -160 dBw  # will be calibrated for 1 m^2
      internal_loss               7 dB

      electronic_protect ACQ_RADAR_EP end_electronic_protect
   end_receiver

   probability_of_false_alarm     1.0e-6
   required_pd                    0.5
   swerling_case                  1
   number_of_pulses_integrated    10

   hits_to_establish_track        3 5
   hits_to_maintain_track         1 3

   compute_measurement_errors true

   track_quality                  0.6      // 'weapons' cue quality

   reports_range
   reports_bearing
   reports_elevation
   reports_iff
end_sensor
