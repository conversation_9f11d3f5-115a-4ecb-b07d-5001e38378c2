# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
/*
PURPOSE  Vectoring behavior node  
AUTHOR   Snyder
Classification: UNCLASSIFIED//FOUO

Technical Description:

*/
advanced_behavior egress

   script_variables 
      extern string faz_desired;
      extern bool change_desired;
      extern WsfPlatform iacid;
      extern string reason;
      extern bool pitch_up;
      extern double pr_altitude;
      extern double pr_speed;
      extern double pr_heading;
      extern bool first_pass;
      extern double time_ok;
      extern bool alerted;
      extern bool apole_tct;
      extern bool sam_threatnd;
      extern double rng_cls_sam;
      extern double rng_cls_hst;
      extern bool pump_per;
      extern bool threatnd;
      extern bool needil;
#      extern WsfBrawlerProcessor BRAWLER;
      extern Atmosphere atmos;
#      extern WsfBrawlerMover my_mover;
      extern double pr_gees;
      extern WsfGeoPoint home_base;
   end_script_variables

   precondition 
      ////// Evaluate conditions that would prevent behavior alternative from running
      if (!PROCESSOR.IsA_TypeOf("WSF_SA_PROCESSOR"))
      {
         writeln_d("not a brawler processor!");
         return Failure("behavior not attached to a WSF_SA_PROCESSOR");
      }

      if (PLATFORM->rule_type == "ftr" && PLATFORM->faz == "egress")
      { return Success(); }
      else
      { return Failure(reason); }
 
   end_precondition
   
   execute 
      bool BrawlMover;
      WsfBrawlerProcessor BRAWLER;
      if (iacid.Mover().Type() == "WSF_BRAWLER_MOVER")
      {
         for (int i=0; i<iacid.ProcessorCount();i+=1)
         {
            if (iacid.ProcessorEntry(i).Type() == "WSF_BRAWLER_PROCESSOR")
            {
               BrawlMover = true;
               BRAWLER = (WsfBrawlerProcessor)iacid.ProcessorEntry(i);
               break;
            }
         }
      }   
      faz_desired = "egress";
      pr_speed    = iacid->EGRESS_SPD*atmos.SonicVelocity(iacid.Altitude());
      pr_altitude = iacid->EGRESS_ALT;
      if (BrawlMover)
      {
         pr_gees = BRAWLER.MaxSustainedGs();
      }
      else
      {
         pr_gees = 3;
      }
      pr_heading = iacid.TrueBearingTo(home_base);
      pr_vector(iacid,pr_heading,pr_speed,pr_altitude,pr_gees);
      return Success(reason);      
   end_execute

end_advanced_behavior
