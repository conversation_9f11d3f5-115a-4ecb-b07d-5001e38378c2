// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StrikeCapabilityMDT_h
#define Uci__Type__StrikeCapabilityMDT_h 1

#if !defined(Uci__Type__CapabilityBaseType_h)
# include "uci/type/CapabilityBaseType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__StrikeCapabilityType_h)
# include "uci/type/StrikeCapabilityType.h"
#endif

#if !defined(Uci__Type__StoreCarriageCapabilityType_h)
# include "uci/type/StoreCarriageCapabilityType.h"
#endif

#if !defined(Uci__Type__StoreVerificationStatusType_h)
# include "uci/type/StoreVerificationStatusType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the StrikeCapabilityMDT sequence accessor class */
      class StrikeCapabilityMDT : public virtual uci::type::CapabilityBaseType {
      public:

         /** The destructor */
         virtual ~StrikeCapabilityMDT()
         { }

         /** Returns this accessor's type constant, i.e. StrikeCapabilityMDT
           *
           * @return This accessor's type constant, i.e. StrikeCapabilityMDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::strikeCapabilityMDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StrikeCapabilityMDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates a Strike Capability provided by the Subsystem. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::StrikeCapabilityType, uci::type::accessorType::strikeCapabilityType> Capability;

         /** Optional list of carriage related to the StrikeCapability [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::StoreCarriageCapabilityType, uci::type::accessorType::storeCarriageCapabilityType> Carriage;

         /** Returns the bounded list that is identified by the Capability.
           *
           * @return The bounded list identified by Capability.
           */
         virtual const uci::type::StrikeCapabilityMDT::Capability& getCapability() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Capability.
           *
           * @return The bounded list identified by Capability.
           */
         virtual uci::type::StrikeCapabilityMDT::Capability& getCapability()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Capability.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCapability(const uci::type::StrikeCapabilityMDT::Capability& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Carriage.
           *
           * @return The bounded list identified by Carriage.
           */
         virtual const uci::type::StrikeCapabilityMDT::Carriage& getCarriage() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Carriage.
           *
           * @return The bounded list identified by Carriage.
           */
         virtual uci::type::StrikeCapabilityMDT::Carriage& getCarriage()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Carriage.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCarriage(const uci::type::StrikeCapabilityMDT::Carriage& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Verification.
           *
           * @return The acecssor that provides access to the complex content that is identified by Verification.
           */
         virtual const uci::type::StoreVerificationStatusType& getVerification() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Verification.
           *
           * @return The acecssor that provides access to the complex content that is identified by Verification.
           */
         virtual uci::type::StoreVerificationStatusType& getVerification()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Verification to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Verification
           */
         virtual void setVerification(const uci::type::StoreVerificationStatusType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Verification exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Verification is emabled or not
           */
         virtual bool hasVerification() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Verification
           *
           * @param type = uci::type::accessorType::storeVerificationStatusType This Accessor's accessor type
           */
         virtual void enableVerification(uci::base::accessorType::AccessorType type = uci::type::accessorType::storeVerificationStatusType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Verification */
         virtual void clearVerification()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StrikeCapabilityMDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StrikeCapabilityMDT to copy from
           */
         StrikeCapabilityMDT(const StrikeCapabilityMDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StrikeCapabilityMDT to the contents of the StrikeCapabilityMDT on
           * the right hand side (rhs) of the assignment operator.StrikeCapabilityMDT [only available to derived classes]
           *
           * @param rhs The StrikeCapabilityMDT on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::StrikeCapabilityMDT
           * @return A constant reference to this StrikeCapabilityMDT.
           */
         const StrikeCapabilityMDT& operator=(const StrikeCapabilityMDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StrikeCapabilityMDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StrikeCapabilityMDT_h

