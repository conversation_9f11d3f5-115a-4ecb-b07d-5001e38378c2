// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SupportedMissionPlanCommandsType_h
#define Uci__Type__SupportedMissionPlanCommandsType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SupportedMissionPlanCommandsTaskAllocationType_h)
# include "uci/type/SupportedMissionPlanCommandsTaskAllocationType.h"
#endif

#if !defined(Uci__Type__SupportedMissionPlanCommandsRoutePlanningType_h)
# include "uci/type/SupportedMissionPlanCommandsRoutePlanningType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SupportedMissionPlanCommandsType sequence accessor class */
      class SupportedMissionPlanCommandsType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SupportedMissionPlanCommandsType()
         { }

         /** Returns this accessor's type constant, i.e. SupportedMissionPlanCommandsType
           *
           * @return This accessor's type constant, i.e. SupportedMissionPlanCommandsType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::supportedMissionPlanCommandsType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SupportedMissionPlanCommandsType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TaskAllocation.
           *
           * @return The acecssor that provides access to the complex content that is identified by TaskAllocation.
           */
         virtual const uci::type::SupportedMissionPlanCommandsTaskAllocationType& getTaskAllocation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TaskAllocation.
           *
           * @return The acecssor that provides access to the complex content that is identified by TaskAllocation.
           */
         virtual uci::type::SupportedMissionPlanCommandsTaskAllocationType& getTaskAllocation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TaskAllocation to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TaskAllocation
           */
         virtual void setTaskAllocation(const uci::type::SupportedMissionPlanCommandsTaskAllocationType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TaskAllocation exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TaskAllocation is emabled or not
           */
         virtual bool hasTaskAllocation() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TaskAllocation
           *
           * @param type = uci::type::accessorType::supportedMissionPlanCommandsTaskAllocationType This Accessor's accessor type
           */
         virtual void enableTaskAllocation(uci::base::accessorType::AccessorType type = uci::type::accessorType::supportedMissionPlanCommandsTaskAllocationType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TaskAllocation */
         virtual void clearTaskAllocation()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the RoutePlanning.
           *
           * @return The acecssor that provides access to the complex content that is identified by RoutePlanning.
           */
         virtual const uci::type::SupportedMissionPlanCommandsRoutePlanningType& getRoutePlanning() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the RoutePlanning.
           *
           * @return The acecssor that provides access to the complex content that is identified by RoutePlanning.
           */
         virtual uci::type::SupportedMissionPlanCommandsRoutePlanningType& getRoutePlanning()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the RoutePlanning to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by RoutePlanning
           */
         virtual void setRoutePlanning(const uci::type::SupportedMissionPlanCommandsRoutePlanningType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by RoutePlanning exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by RoutePlanning is emabled or not
           */
         virtual bool hasRoutePlanning() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by RoutePlanning
           *
           * @param type = uci::type::accessorType::supportedMissionPlanCommandsRoutePlanningType This Accessor's accessor type
           */
         virtual void enableRoutePlanning(uci::base::accessorType::AccessorType type = uci::type::accessorType::supportedMissionPlanCommandsRoutePlanningType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by RoutePlanning */
         virtual void clearRoutePlanning()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SupportedMissionPlanCommandsType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SupportedMissionPlanCommandsType to copy from
           */
         SupportedMissionPlanCommandsType(const SupportedMissionPlanCommandsType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SupportedMissionPlanCommandsType to the contents of the
           * SupportedMissionPlanCommandsType on the right hand side (rhs) of the assignment
           * operator.SupportedMissionPlanCommandsType [only available to derived classes]
           *
           * @param rhs The SupportedMissionPlanCommandsType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::SupportedMissionPlanCommandsType
           * @return A constant reference to this SupportedMissionPlanCommandsType.
           */
         const SupportedMissionPlanCommandsType& operator=(const SupportedMissionPlanCommandsType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SupportedMissionPlanCommandsType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SupportedMissionPlanCommandsType_h

