# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************

// sensors
include_once processors/sensor_cue_processor.txt
include_once sensors/radar/aesa.txt
include_once sensors/radar/aesa_antenna.txt
include_once sensors/esm_rwr/esm.txt
// platform type
include_once platforms/brawler_test.txt
include_once platforms/lte_fighter_six_dof.txt
// weapons
include_once weapons/aam/medium_range_radar_missile.txt
include_once weapons/aam/short_range_ir_missile.txt

# This brawler enabled platform has the following defined:
# 1. A WSF_BRAWLER_PROCESSOR    (typically named task_mgr or thinker)
# 2. A WSF_BRAWLER_MOVER        (possibly could use WSF_P6DOF_MOVER in future)
# 3. A WSF_PERCEPTION_PROCESSOR (typically named perception)
# 4. A WSF_THREAT_PROCESSOR     (typically named incoming_threats)
# 5. A WSF_BRAWLER_FUEL         (optional: the brawler mover implicitely models brawler fuel provided by the 'aero_file')

radar_signature 10dB_FuzzBall WSF_RADAR_SIGNATURE 
constant 10 dBsm
end_radar_signature

#platform_type  BRAWLER_PLATFORM  WSF_BRAWLER_PLATFORM
platform_type  LTE_FIGHTER  BRAWLER_TEST
   icon f15c
   include prdata/blue.txt

   radar_signature 10dB_FuzzBall
#script_variables 
#      no_tree = true;
#end_script_variables
   mover WSF_BRAWLER_MOVER
      aero_file platforms/fxw/lte_fighter.fxw
      update_time_tolerance 0.01 s
   end_mover

   fuel WSF_BRAWLER_FUEL
      aero_file platforms/fxw/lte_fighter.fxw
      initial_quantity_ratio 1.0
   end_fuel

   weapon fox3 MEDIUM_RANGE_RADAR_MISSILE # fox 3 (MRM)
      quantity 6
   end_weapon

   weapon fox2 SHORT_RANGE_IR_MISSILE # fox 2 (SRM)
      quantity 2
   end_weapon

   weapon fox1 SHORT_RANGE_IR_MISSILE # fox 1 (other)
      quantity 0
   end_weapon
   
   weapon agm SHORT_RANGE_IR_MISSILE # air-to-ground missile, use SRM just to populate something
      quantity 0
   end_weapon

   comm weapon_datalink WSF_COMM_TRANSCEIVER      // uplink to weapons
      network_name weapons_subnet
      internal_link data_mgr
   end_comm

   sensor rdr1 aesa  
      on
      internal_link raw_data_mgr
      internal_link data_mgr
      ignore missile
   end_sensor

   sensor eyes WSF_GEOMETRIC_SENSOR
      on
      azimuth_field_of_view   -180.0 degrees  180.0 degrees
      elevation_field_of_view -90.0  degrees  90.0  degrees
      maximum_range           10 nmi
      frame_time              1 sec
      range_error_sigma       1000 ft
      range_rate_error_sigma  50 m/s
      azimuth_error_sigma     1 deg
      elevation_error_sigma   1 deg
      reports_location 
      reports_velocity 
      reports_range_rate
      reports_range
      reports_bearing 
      reports_elevation 
      reports_iff
      internal_link raw_data_mgr
      ignore_same_side
      ignore missile
   end_sensor
  
    sensor rwr esm                       // 3
      on
      ignore IGNORE
      ignore_same_side
      internal_link raw_data_mgr
      internal_link data_mgr      
   end_sensor

   processor radar_track_cueing SENSOR_CUE_PROCESSOR
      script_variables 
         mSourceSensorNames.Insert("*");
         mCuedSensorName = "rdr1";
         mTrackModeName = "TWS";
      end_script_variables
   end_processor

   processor weapon_datalink_manager WEAPON_DL_MANAGER
      script_variables 
         mUplinkSensorNames.PushBack("rdr1");
      end_script_variables
   end_processor
end_platform_type

platform_type BLUE_FIGHTER LTE_FIGHTER
#   execute at_time 30 s absolute 
#      PLATFORM.DeletePlatform();
#   end_execute
   edit processor assessment
      enemy_side    red
      enemy_type    RED_FIGHTER
      friendly_type BLUE_FIGHTER
      flight_id     1
   end_processor 
end_platform_type

platform_type RED_FIGHTER LTE_FIGHTER
 
   icon su27

   edit processor assessment
      enemy_side    blue
      enemy_type    BLUE_FIGHTER
      friendly_type RED_FIGHTER
      flight_id     202
   end_processor 
end_platform_type

platform_type AIR_MOVER_FIGHTER  BRAWLER_TEST_AIRMOVER
   icon f15c
   include prdata/blue.txt

   radar_signature 10dB_FuzzBall

   mover WSF_AIR_MOVER
#      aero_file platforms/fxw/lte_fighter.fxw
#      update_time_tolerance 0.01 s
   end_mover

   weapon fox3 MEDIUM_RANGE_RADAR_MISSILE # fox 3 (MRM)
      quantity 6
   end_weapon

   weapon fox2 SHORT_RANGE_IR_MISSILE # fox 2 (SRM)
      quantity 2
   end_weapon

   weapon fox1 SHORT_RANGE_IR_MISSILE # fox 1 (other)
      quantity 0
   end_weapon
   
   weapon agm SHORT_RANGE_IR_MISSILE # air-to-ground missile, use SRM just to populate something
      quantity 0
   end_weapon

   comm weapon_datalink WSF_COMM_TRANSCEIVER      // uplink to weapons
      network_name weapons_subnet
      internal_link data_mgr
   end_comm

   sensor rdr1 aesa  
      on
      internal_link raw_data_mgr
      ignore missile
   end_sensor

   sensor eyes WSF_GEOMETRIC_SENSOR
      on
      azimuth_field_of_view   -180.0 degrees  180.0 degrees
      elevation_field_of_view -90.0  degrees  90.0  degrees
      maximum_range           10 nmi
      frame_time              1 sec
      range_error_sigma       1000 ft
      range_rate_error_sigma  50 m/s
      azimuth_error_sigma     1 deg
      elevation_error_sigma   1 deg
      reports_location 
      reports_velocity 
      reports_range_rate
      reports_range
      reports_bearing 
      reports_elevation 
      reports_iff
      internal_link raw_data_mgr
      ignore_same_side
      ignore missile
#      filter WSF_KALMAN_FILTER
#         process_noise_model      constant_acceleration
#         process_noise_sigmas_XYZ 50 10 50          # AFSIM recommended values for high agility aircraft
#      end_filter
      filter WSF_KALMAN_FILTER
         process_noise_model      constant_velocity
         process_noise_sigmas_XYZ 2 2 2          # AFSIM recommended values for high agility aircraft
      end_filter
   end_sensor
  
    sensor rwr esm                       // 3
      on
      ignore IGNORE
      ignore_same_side
      internal_link raw_data_mgr
   end_sensor

   processor radar_track_cueing SENSOR_CUE_PROCESSOR
      script_variables 
         mSourceSensorNames.Insert("*");
         mCuedSensorName = "rdr1";
         mTrackModeName = "TWS";
      end_script_variables
   end_processor

   processor weapon_datalink_manager WEAPON_DL_MANAGER
      script_variables 
         mUplinkSensorNames.PushBack("rdr1");
      end_script_variables
   end_processor
end_platform_type

platform_type BLUE_FIGHTER_AIR AIR_MOVER_FIGHTER 
   edit processor assessment
      enemy_side red
      enemy_type RED_FIGHTER_AIR
      friendly_type BLUE_FIGHTER_AIR
      flight_id 1
   end_processor 
end_platform_type

platform_type RED_FIGHTER_AIR AIR_MOVER_FIGHTER

   icon su27

   edit processor assessment
      enemy_side    blue
      enemy_type    BLUE_FIGHTER_AIR
      friendly_type RED_FIGHTER_AIR
      flight_id     202
   end_processor 
end_platform_type

platform_type SIX_DOF_FIGHTER  BRAWLER_TEST_AIRMOVER
   icon f15c
   include prdata/blue.txt

   radar_signature 10dB_FuzzBall

   mover WSF_POINT_MASS_SIX_DOF_MOVER
       vehicle_type LTE_FIGHTER_PM6
       update_interval 0.05 sec
   end_mover

   weapon fox3 MEDIUM_RANGE_RADAR_MISSILE # fox 3 (MRM)
      quantity 6
   end_weapon

   weapon fox2 SHORT_RANGE_IR_MISSILE # fox 2 (SRM)
      quantity 2
   end_weapon

   weapon fox1 SHORT_RANGE_IR_MISSILE # fox 1 (other)
      quantity 0
   end_weapon
   
   weapon agm SHORT_RANGE_IR_MISSILE # air-to-ground missile, use SRM just to populate something
      quantity 0
   end_weapon

   comm weapon_datalink WSF_COMM_TRANSCEIVER      // uplink to weapons
      network_name weapons_subnet
      internal_link data_mgr
   end_comm

   sensor rdr1 aesa  
      on
      internal_link raw_data_mgr
      ignore missile
   end_sensor

   sensor eyes WSF_GEOMETRIC_SENSOR
      on
      azimuth_field_of_view   -180.0 degrees  180.0 degrees
      elevation_field_of_view -90.0  degrees  90.0  degrees
      maximum_range           10 nmi
      frame_time              1 sec
      range_error_sigma       1000 ft
      range_rate_error_sigma  50 m/s
      azimuth_error_sigma     1 deg
      elevation_error_sigma   1 deg
      reports_location 
      reports_velocity 
      reports_range_rate
      reports_range
      reports_bearing 
      reports_elevation 
      reports_iff
      internal_link raw_data_mgr
      ignore_same_side
      ignore missile
#      filter WSF_KALMAN_FILTER
#         process_noise_model      constant_acceleration
#         process_noise_sigmas_XYZ 50 10 50          # AFSIM recommended values for high agility aircraft
#      end_filter
      filter WSF_KALMAN_FILTER
         process_noise_model      constant_velocity
         process_noise_sigmas_XYZ 2 2 2          # AFSIM recommended values for high agility aircraft
      end_filter
   end_sensor
  
    sensor rwr esm                       // 3
      on
      ignore IGNORE
      ignore_same_side
      internal_link raw_data_mgr
   end_sensor

   processor radar_track_cueing SENSOR_CUE_PROCESSOR
      script_variables 
         mSourceSensorNames.Insert("*");
         mCuedSensorName = "rdr1";
         mTrackModeName = "TWS";
      end_script_variables
   end_processor

   processor weapon_datalink_manager WEAPON_DL_MANAGER
      script_variables 
         mUplinkSensorNames.PushBack("rdr1");
      end_script_variables
   end_processor
end_platform_type

platform_type BLUE_FIGHTER_PM6 SIX_DOF_FIGHTER 
   edit processor assessment
      enemy_side red
      enemy_type RED_FIGHTER_AIR
      friendly_type BLUE_FIGHTER_AIR
      flight_id 1
   end_processor 
end_platform_type

platform_type RED_FIGHTER_PM6 SIX_DOF_FIGHTER

   icon su27

   edit processor assessment
      enemy_side    blue
      enemy_type    BLUE_FIGHTER_AIR
      friendly_type RED_FIGHTER_AIR
      flight_id     202
   end_processor 
end_platform_type
