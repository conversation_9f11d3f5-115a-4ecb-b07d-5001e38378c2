// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SpecificFacilityIdentityConfidenceType_h
#define Uci__Type__SpecificFacilityIdentityConfidenceType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__FacilityIdentificationType_h)
# include "uci/type/FacilityIdentificationType.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Indicates the confidence of the declaration of the specific facility instance's Identity. */
      class SpecificFacilityIdentityConfidenceType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SpecificFacilityIdentityConfidenceType()
         { }

         /** Returns this accessor's type constant, i.e. SpecificFacilityIdentityConfidenceType
           *
           * @return This accessor's type constant, i.e. SpecificFacilityIdentityConfidenceType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::specificFacilityIdentityConfidenceType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SpecificFacilityIdentityConfidenceType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the FacilityIdentification.
           *
           * @return The acecssor that provides access to the complex content that is identified by FacilityIdentification.
           */
         virtual const uci::type::FacilityIdentificationType& getFacilityIdentification() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the FacilityIdentification.
           *
           * @return The acecssor that provides access to the complex content that is identified by FacilityIdentification.
           */
         virtual uci::type::FacilityIdentificationType& getFacilityIdentification()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the FacilityIdentification to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by FacilityIdentification
           */
         virtual void setFacilityIdentification(const uci::type::FacilityIdentificationType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Confidence.
           *
           * @return The value of the simple primitive data type identified by Confidence.
           */
         virtual uci::type::PercentTypeValue getConfidence() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Confidence.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setConfidence(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SpecificFacilityIdentityConfidenceType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SpecificFacilityIdentityConfidenceType to copy from
           */
         SpecificFacilityIdentityConfidenceType(const SpecificFacilityIdentityConfidenceType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SpecificFacilityIdentityConfidenceType to the contents of the
           * SpecificFacilityIdentityConfidenceType on the right hand side (rhs) of the assignment
           * operator.SpecificFacilityIdentityConfidenceType [only available to derived classes]
           *
           * @param rhs The SpecificFacilityIdentityConfidenceType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::SpecificFacilityIdentityConfidenceType
           * @return A constant reference to this SpecificFacilityIdentityConfidenceType.
           */
         const SpecificFacilityIdentityConfidenceType& operator=(const SpecificFacilityIdentityConfidenceType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SpecificFacilityIdentityConfidenceType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SpecificFacilityIdentityConfidenceType_h

