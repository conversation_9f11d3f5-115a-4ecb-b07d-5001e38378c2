// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__VulnerabilityMetricsType_h
#define Uci__Type__VulnerabilityMetricsType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__RouteVulnerabilityType_h)
# include "uci/type/RouteVulnerabilityType.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the VulnerabilityMetricsType sequence accessor class */
      class VulnerabilityMetricsType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~VulnerabilityMetricsType()
         { }

         /** Returns this accessor's type constant, i.e. VulnerabilityMetricsType
           *
           * @return This accessor's type constant, i.e. VulnerabilityMetricsType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::vulnerabilityMetricsType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const VulnerabilityMetricsType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AcquisitionMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by AcquisitionMetrics.
           */
         virtual const uci::type::RouteVulnerabilityType& getAcquisitionMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AcquisitionMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by AcquisitionMetrics.
           */
         virtual uci::type::RouteVulnerabilityType& getAcquisitionMetrics()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the AcquisitionMetrics to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by AcquisitionMetrics
           */
         virtual void setAcquisitionMetrics(const uci::type::RouteVulnerabilityType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by AcquisitionMetrics exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by AcquisitionMetrics is emabled or not
           */
         virtual bool hasAcquisitionMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by AcquisitionMetrics
           *
           * @param type = uci::type::accessorType::routeVulnerabilityType This Accessor's accessor type
           */
         virtual void enableAcquisitionMetrics(uci::base::accessorType::AccessorType type = uci::type::accessorType::routeVulnerabilityType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by AcquisitionMetrics */
         virtual void clearAcquisitionMetrics()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TrackMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by TrackMetrics.
           */
         virtual const uci::type::RouteVulnerabilityType& getTrackMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TrackMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by TrackMetrics.
           */
         virtual uci::type::RouteVulnerabilityType& getTrackMetrics()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TrackMetrics to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TrackMetrics
           */
         virtual void setTrackMetrics(const uci::type::RouteVulnerabilityType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TrackMetrics exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TrackMetrics is emabled or not
           */
         virtual bool hasTrackMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TrackMetrics
           *
           * @param type = uci::type::accessorType::routeVulnerabilityType This Accessor's accessor type
           */
         virtual void enableTrackMetrics(uci::base::accessorType::AccessorType type = uci::type::accessorType::routeVulnerabilityType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TrackMetrics */
         virtual void clearTrackMetrics()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the LaunchMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by LaunchMetrics.
           */
         virtual const uci::type::RouteVulnerabilityType& getLaunchMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the LaunchMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by LaunchMetrics.
           */
         virtual uci::type::RouteVulnerabilityType& getLaunchMetrics()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the LaunchMetrics to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by LaunchMetrics
           */
         virtual void setLaunchMetrics(const uci::type::RouteVulnerabilityType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by LaunchMetrics exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by LaunchMetrics is emabled or not
           */
         virtual bool hasLaunchMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by LaunchMetrics
           *
           * @param type = uci::type::accessorType::routeVulnerabilityType This Accessor's accessor type
           */
         virtual void enableLaunchMetrics(uci::base::accessorType::AccessorType type = uci::type::accessorType::routeVulnerabilityType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by LaunchMetrics */
         virtual void clearLaunchMetrics()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the InterceptMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by InterceptMetrics.
           */
         virtual const uci::type::RouteVulnerabilityType& getInterceptMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the InterceptMetrics.
           *
           * @return The acecssor that provides access to the complex content that is identified by InterceptMetrics.
           */
         virtual uci::type::RouteVulnerabilityType& getInterceptMetrics()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the InterceptMetrics to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by InterceptMetrics
           */
         virtual void setInterceptMetrics(const uci::type::RouteVulnerabilityType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by InterceptMetrics exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by InterceptMetrics is emabled or not
           */
         virtual bool hasInterceptMetrics() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by InterceptMetrics
           *
           * @param type = uci::type::accessorType::routeVulnerabilityType This Accessor's accessor type
           */
         virtual void enableInterceptMetrics(uci::base::accessorType::AccessorType type = uci::type::accessorType::routeVulnerabilityType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by InterceptMetrics */
         virtual void clearInterceptMetrics()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the ProbabilityOfSurvival.
           *
           * @return The value of the simple primitive data type identified by ProbabilityOfSurvival.
           */
         virtual uci::type::PercentTypeValue getProbabilityOfSurvival() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the ProbabilityOfSurvival.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setProbabilityOfSurvival(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by ProbabilityOfSurvival exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by ProbabilityOfSurvival is emabled or not
           */
         virtual bool hasProbabilityOfSurvival() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by ProbabilityOfSurvival
           *
           * @param type = uci::type::accessorType::percentType This Accessor's accessor type
           */
         virtual void enableProbabilityOfSurvival(uci::base::accessorType::AccessorType type = uci::type::accessorType::percentType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by ProbabilityOfSurvival */
         virtual void clearProbabilityOfSurvival()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the ExposureEventProbabilityThreshold.
           *
           * @return The value of the simple primitive data type identified by ExposureEventProbabilityThreshold.
           */
         virtual uci::type::PercentTypeValue getExposureEventProbabilityThreshold() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the ExposureEventProbabilityThreshold.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setExposureEventProbabilityThreshold(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by ExposureEventProbabilityThreshold exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by ExposureEventProbabilityThreshold is emabled or not
           */
         virtual bool hasExposureEventProbabilityThreshold() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by ExposureEventProbabilityThreshold
           *
           * @param type = uci::type::accessorType::percentType This Accessor's accessor type
           */
         virtual void enableExposureEventProbabilityThreshold(uci::base::accessorType::AccessorType type = uci::type::accessorType::percentType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by ExposureEventProbabilityThreshold */
         virtual void clearExposureEventProbabilityThreshold()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         VulnerabilityMetricsType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The VulnerabilityMetricsType to copy from
           */
         VulnerabilityMetricsType(const VulnerabilityMetricsType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this VulnerabilityMetricsType to the contents of the
           * VulnerabilityMetricsType on the right hand side (rhs) of the assignment operator.VulnerabilityMetricsType [only
           * available to derived classes]
           *
           * @param rhs The VulnerabilityMetricsType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::VulnerabilityMetricsType
           * @return A constant reference to this VulnerabilityMetricsType.
           */
         const VulnerabilityMetricsType& operator=(const VulnerabilityMetricsType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // VulnerabilityMetricsType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__VulnerabilityMetricsType_h

