// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SupportedMissionPlanCommandsRoutePlanningFunctionType_h
#define Uci__Type__SupportedMissionPlanCommandsRoutePlanningFunctionType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__RoutePlanningTypeEnum_h)
# include "uci/type/RoutePlanningTypeEnum.h"
#endif

#if !defined(Uci__Type__MissionPlanCommandSystemsEnum_h)
# include "uci/type/MissionPlanCommandSystemsEnum.h"
#endif

#if !defined(Uci__Type__PathTypeEnum_h)
# include "uci/type/PathTypeEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SupportedMissionPlanCommandsRoutePlanningFunctionType sequence accessor class */
      class SupportedMissionPlanCommandsRoutePlanningFunctionType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SupportedMissionPlanCommandsRoutePlanningFunctionType()
         { }

         /** Returns this accessor's type constant, i.e. SupportedMissionPlanCommandsRoutePlanningFunctionType
           *
           * @return This accessor's type constant, i.e. SupportedMissionPlanCommandsRoutePlanningFunctionType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::supportedMissionPlanCommandsRoutePlanningFunctionType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SupportedMissionPlanCommandsRoutePlanningFunctionType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the FunctionType.
           *
           * @return The value of the enumeration identified by FunctionType.
           */
         virtual const uci::type::RoutePlanningTypeEnum& getFunctionType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the FunctionType.
           *
           * @return The value of the enumeration identified by FunctionType.
           */
         virtual uci::type::RoutePlanningTypeEnum& getFunctionType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the FunctionType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setFunctionType(const uci::type::RoutePlanningTypeEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the FunctionType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setFunctionType(uci::type::RoutePlanningTypeEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SystemType.
           *
           * @return The value of the enumeration identified by SystemType.
           */
         virtual const uci::type::MissionPlanCommandSystemsEnum& getSystemType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SystemType.
           *
           * @return The value of the enumeration identified by SystemType.
           */
         virtual uci::type::MissionPlanCommandSystemsEnum& getSystemType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SystemType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSystemType(const uci::type::MissionPlanCommandSystemsEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SystemType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSystemType(uci::type::MissionPlanCommandSystemsEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the RoutePathType.
           *
           * @return The value of the enumeration identified by RoutePathType.
           */
         virtual const uci::type::PathTypeEnum& getRoutePathType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the RoutePathType.
           *
           * @return The value of the enumeration identified by RoutePathType.
           */
         virtual uci::type::PathTypeEnum& getRoutePathType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the RoutePathType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setRoutePathType(const uci::type::PathTypeEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the RoutePathType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setRoutePathType(uci::type::PathTypeEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield RoutePathTypeis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasRoutePathType() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getRoutePathType will return the optional field and not throw an exception when
           * invoked.
           *
           * @param type = uci::type::accessorType::pathTypeEnum This Accessor's accessor type
           */
         virtual void enableRoutePathType(uci::base::accessorType::AccessorType type = uci::type::accessorType::pathTypeEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearRoutePathType()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SupportedMissionPlanCommandsRoutePlanningFunctionType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SupportedMissionPlanCommandsRoutePlanningFunctionType to copy from
           */
         SupportedMissionPlanCommandsRoutePlanningFunctionType(const SupportedMissionPlanCommandsRoutePlanningFunctionType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SupportedMissionPlanCommandsRoutePlanningFunctionType to the
           * contents of the SupportedMissionPlanCommandsRoutePlanningFunctionType on the right hand side (rhs) of the assignment
           * operator.SupportedMissionPlanCommandsRoutePlanningFunctionType [only available to derived classes]
           *
           * @param rhs The SupportedMissionPlanCommandsRoutePlanningFunctionType on the right hand side (rhs) of the assignment
           *      operator whose contents are used to set the contents of this
           *      uci::type::SupportedMissionPlanCommandsRoutePlanningFunctionType
           * @return A constant reference to this SupportedMissionPlanCommandsRoutePlanningFunctionType.
           */
         const SupportedMissionPlanCommandsRoutePlanningFunctionType& operator=(const SupportedMissionPlanCommandsRoutePlanningFunctionType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SupportedMissionPlanCommandsRoutePlanningFunctionType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SupportedMissionPlanCommandsRoutePlanningFunctionType_h

