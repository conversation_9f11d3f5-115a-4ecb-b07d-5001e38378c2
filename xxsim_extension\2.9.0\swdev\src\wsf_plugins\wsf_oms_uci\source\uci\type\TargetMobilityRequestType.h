// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TargetMobilityRequestType_h
#define Uci__Type__TargetMobilityRequestType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__EntityID_Type_h)
# include "uci/type/EntityID_Type.h"
#endif

#if !defined(Uci__Type__WeatherEffectsType_h)
# include "uci/type/WeatherEffectsType.h"
#endif

#if !defined(Uci__Type__Point4D_Type_h)
# include "uci/type/Point4D_Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** The inputs used in generating and/or requesting a Target Mobility Assessment. */
      class TargetMobilityRequestType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TargetMobilityRequestType()
         { }

         /** Returns this accessor's type constant, i.e. TargetMobilityRequestType
           *
           * @return This accessor's type constant, i.e. TargetMobilityRequestType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::targetMobilityRequestType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TargetMobilityRequestType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityID.
           */
         virtual const uci::type::EntityID_Type& getEntityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityID.
           */
         virtual uci::type::EntityID_Type& getEntityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the EntityID to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by EntityID
           */
         virtual void setEntityID(const uci::type::EntityID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherEffects.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherEffects.
           */
         virtual const uci::type::WeatherEffectsType& getWeatherEffects() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherEffects.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherEffects.
           */
         virtual uci::type::WeatherEffectsType& getWeatherEffects()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WeatherEffects to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WeatherEffects
           */
         virtual void setWeatherEffects(const uci::type::WeatherEffectsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by WeatherEffects exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by WeatherEffects is emabled or not
           */
         virtual bool hasWeatherEffects() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by WeatherEffects
           *
           * @param type = uci::type::accessorType::weatherEffectsType This Accessor's accessor type
           */
         virtual void enableWeatherEffects(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherEffectsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by WeatherEffects */
         virtual void clearWeatherEffects()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityAssessmentLocation.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityAssessmentLocation.
           */
         virtual const uci::type::Point4D_Type& getEntityAssessmentLocation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityAssessmentLocation.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityAssessmentLocation.
           */
         virtual uci::type::Point4D_Type& getEntityAssessmentLocation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the EntityAssessmentLocation to the contents of the complex content
           * that is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by EntityAssessmentLocation
           */
         virtual void setEntityAssessmentLocation(const uci::type::Point4D_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by EntityAssessmentLocation exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by EntityAssessmentLocation is emabled or not
           */
         virtual bool hasEntityAssessmentLocation() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by EntityAssessmentLocation
           *
           * @param type = uci::type::accessorType::point4D_Type This Accessor's accessor type
           */
         virtual void enableEntityAssessmentLocation(uci::base::accessorType::AccessorType type = uci::type::accessorType::point4D_Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by EntityAssessmentLocation */
         virtual void clearEntityAssessmentLocation()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TargetMobilityRequestType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TargetMobilityRequestType to copy from
           */
         TargetMobilityRequestType(const TargetMobilityRequestType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TargetMobilityRequestType to the contents of the
           * TargetMobilityRequestType on the right hand side (rhs) of the assignment operator.TargetMobilityRequestType [only
           * available to derived classes]
           *
           * @param rhs The TargetMobilityRequestType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::TargetMobilityRequestType
           * @return A constant reference to this TargetMobilityRequestType.
           */
         const TargetMobilityRequestType& operator=(const TargetMobilityRequestType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TargetMobilityRequestType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TargetMobilityRequestType_h

