{"files.associations": {"deque": "cpp", "list": "cpp", "map": "cpp", "unordered_map": "cpp", "vector": "cpp", "xhash": "cpp", "xstring": "cpp", "xtree": "cpp", "utility": "cpp", "chrono": "cpp", "memory": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "charconv": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "format": "cpp", "forward_list": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "locale": "cpp", "mutex": "cpp", "new": "cpp", "numeric": "cpp", "optional": "cpp", "ostream": "cpp", "queue": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "set": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "unordered_set": "cpp", "valarray": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xtr1common": "cpp", "xutility": "cpp", "viewereventhandlers": "cpp", "light": "cpp", "future": "cpp", "stylesheet": "cpp", "iotypes": "cpp", "layershader": "cpp", "status": "cpp", "particleprocessor": "cpp"}}