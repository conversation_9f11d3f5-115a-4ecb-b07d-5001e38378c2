
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>cmake_parse_arguments &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="cmake_path" href="cmake_path.html" />
    <link rel="prev" title="cmake_minimum_required" href="cmake_minimum_required.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="cmake_path.html" title="cmake_path"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake_minimum_required.html" title="cmake_minimum_required"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_parse_arguments</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cmake-parse-arguments">
<span id="command:cmake_parse_arguments"></span><h1>cmake_parse_arguments<a class="headerlink" href="#cmake-parse-arguments" title="Permalink to this heading">¶</a></h1>
<p>Parse function or macro arguments.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_parse_arguments(</span><span class="nv">&lt;prefix&gt;</span><span class="w"> </span><span class="nv">&lt;options&gt;</span><span class="w"> </span><span class="nv">&lt;one_value_keywords&gt;</span><span class="w"></span>
<span class="w">                      </span><span class="nv">&lt;multi_value_keywords&gt;</span><span class="w"> </span><span class="nv">&lt;args&gt;...</span><span class="nf">)</span><span class="w"></span>

<span class="nf">cmake_parse_arguments(</span><span class="no">PARSE_ARGV</span><span class="w"> </span><span class="nv">&lt;N&gt;</span><span class="w"> </span><span class="nv">&lt;prefix&gt;</span><span class="w"> </span><span class="nv">&lt;options&gt;</span><span class="w"></span>
<span class="w">                      </span><span class="nv">&lt;one_value_keywords&gt;</span><span class="w"> </span><span class="nv">&lt;multi_value_keywords&gt;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>This command is implemented natively.  Previously, it has been defined in the
module <span class="target" id="index-0-module:CMakeParseArguments"></span><a class="reference internal" href="../module/CMakeParseArguments.html#module:CMakeParseArguments" title="CMakeParseArguments"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CMakeParseArguments</span></code></a>.</p>
</div>
<p>This command is for use in macros or functions.
It processes the arguments given to that macro or function,
and defines a set of variables which hold the values of the
respective options.</p>
<p>The first signature reads processes arguments passed in the <code class="docutils literal notranslate"><span class="pre">&lt;args&gt;...</span></code>.
This may be used in either a <span class="target" id="index-0-command:macro"></span><a class="reference internal" href="macro.html#command:macro" title="macro"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">macro()</span></code></a> or a <span class="target" id="index-0-command:function"></span><a class="reference internal" href="function.html#command:function" title="function"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">function()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>The <code class="docutils literal notranslate"><span class="pre">PARSE_ARGV</span></code> signature is only for use in a <span class="target" id="index-1-command:function"></span><a class="reference internal" href="function.html#command:function" title="function"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">function()</span></code></a>
body.  In this case the arguments that are parsed come from the
<code class="docutils literal notranslate"><span class="pre">ARGV#</span></code> variables of the calling function.  The parsing starts with
the <code class="docutils literal notranslate"><span class="pre">&lt;N&gt;</span></code>-th argument, where <code class="docutils literal notranslate"><span class="pre">&lt;N&gt;</span></code> is an unsigned integer.
This allows for the values to have special characters like <code class="docutils literal notranslate"><span class="pre">;</span></code> in them.</p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;options&gt;</span></code> argument contains all options for the respective macro,
i.e.  keywords which can be used when calling the macro without any value
following, like e.g.  the <code class="docutils literal notranslate"><span class="pre">OPTIONAL</span></code> keyword of the <span class="target" id="index-0-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a>
command.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;one_value_keywords&gt;</span></code> argument contains all keywords for this macro
which are followed by one value, like e.g. <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> keyword of the
<span class="target" id="index-1-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a> command.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;multi_value_keywords&gt;</span></code> argument contains all keywords for this
macro which can be followed by more than one value, like e.g. the
<code class="docutils literal notranslate"><span class="pre">TARGETS</span></code> or <code class="docutils literal notranslate"><span class="pre">FILES</span></code> keywords of the <span class="target" id="index-2-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a> command.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>All keywords shall be unique. I.e. every keyword shall only be specified
once in either <code class="docutils literal notranslate"><span class="pre">&lt;options&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;one_value_keywords&gt;</span></code> or
<code class="docutils literal notranslate"><span class="pre">&lt;multi_value_keywords&gt;</span></code>. A warning will be emitted if uniqueness is
violated.</p>
</div>
<p>When done, <code class="docutils literal notranslate"><span class="pre">cmake_parse_arguments</span></code> will consider for each of the
keywords listed in <code class="docutils literal notranslate"><span class="pre">&lt;options&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;one_value_keywords&gt;</span></code> and
<code class="docutils literal notranslate"><span class="pre">&lt;multi_value_keywords&gt;</span></code> a variable composed of the given <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;</span></code>
followed by <code class="docutils literal notranslate"><span class="pre">&quot;_&quot;</span></code> and the name of the respective keyword.  These
variables will then hold the respective value from the argument list
or be undefined if the associated option could not be found.
For the <code class="docutils literal notranslate"><span class="pre">&lt;options&gt;</span></code> keywords, these will always be defined,
to <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> or <code class="docutils literal notranslate"><span class="pre">FALSE</span></code>, whether the option is in the argument list or not.</p>
<p>All remaining arguments are collected in a variable
<code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;_UNPARSED_ARGUMENTS</span></code> that will be undefined if all arguments
were recognized. This can be checked afterwards to see
whether your macro was called with unrecognized parameters.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.15: </span><code class="docutils literal notranslate"><span class="pre">&lt;one_value_keywords&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">&lt;multi_value_keywords&gt;</span></code> that were given no
values at all are collected in a variable
<code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;_KEYWORDS_MISSING_VALUES</span></code> that will be undefined if all keywords
received values. This can be checked to see if there were keywords without
any values given.</p>
</div>
<p>Consider the following example macro, <code class="docutils literal notranslate"><span class="pre">my_install()</span></code>, which takes similar
arguments to the real <span class="target" id="index-3-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a> command:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">macro(</span><span class="nb">my_install</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">set(</span><span class="nb">options</span><span class="w"> </span><span class="no">OPTIONAL</span><span class="w"> </span><span class="no">FAST</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">set(</span><span class="nb">oneValueArgs</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="no">RENAME</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">set(</span><span class="nb">multiValueArgs</span><span class="w"> </span><span class="no">TARGETS</span><span class="w"> </span><span class="no">CONFIGURATIONS</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">cmake_parse_arguments(</span><span class="no">MY_INSTALL</span><span class="w"> </span><span class="s">&quot;${options}&quot;</span><span class="w"> </span><span class="s">&quot;${oneValueArgs}&quot;</span><span class="w"></span>
<span class="w">                          </span><span class="s">&quot;${multiValueArgs}&quot;</span><span class="w"> </span><span class="o">${</span><span class="nt">ARGN</span><span class="o">}</span><span class="w"> </span><span class="nf">)</span><span class="w"></span>

<span class="w">    </span><span class="c"># ...</span>
</pre></div>
</div>
<p>Assume <code class="docutils literal notranslate"><span class="pre">my_install()</span></code> has been called like this:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">my_install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">foo</span><span class="w"> </span><span class="nb">bar</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nb">bin</span><span class="w"> </span><span class="no">OPTIONAL</span><span class="w"> </span><span class="nb">blub</span><span class="w"> </span><span class="no">CONFIGURATIONS</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>After the <code class="docutils literal notranslate"><span class="pre">cmake_parse_arguments</span></code> call the macro will have set or undefined
the following variables:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>MY_INSTALL_OPTIONAL = TRUE
MY_INSTALL_FAST = FALSE # was not used in call to my_install
MY_INSTALL_DESTINATION = &quot;bin&quot;
MY_INSTALL_RENAME &lt;UNDEFINED&gt; # was not used
MY_INSTALL_TARGETS = &quot;foo;bar&quot;
MY_INSTALL_CONFIGURATIONS &lt;UNDEFINED&gt; # was not used
MY_INSTALL_UNPARSED_ARGUMENTS = &quot;blub&quot; # nothing expected after &quot;OPTIONAL&quot;
MY_INSTALL_KEYWORDS_MISSING_VALUES = &quot;CONFIGURATIONS&quot;
         # No value for &quot;CONFIGURATIONS&quot; given
</pre></div>
</div>
<p>You can then continue and process these variables.</p>
<p>Keywords terminate lists of values, e.g. if directly after a
<code class="docutils literal notranslate"><span class="pre">one_value_keyword</span></code> another recognized keyword follows, this is
interpreted as the beginning of the new option.  E.g.
<code class="docutils literal notranslate"><span class="pre">my_install(TARGETS</span> <span class="pre">foo</span> <span class="pre">DESTINATION</span> <span class="pre">OPTIONAL)</span></code> would result in
<code class="docutils literal notranslate"><span class="pre">MY_INSTALL_DESTINATION</span></code> set to <code class="docutils literal notranslate"><span class="pre">&quot;OPTIONAL&quot;</span></code>, but as <code class="docutils literal notranslate"><span class="pre">OPTIONAL</span></code>
is a keyword itself <code class="docutils literal notranslate"><span class="pre">MY_INSTALL_DESTINATION</span></code> will be empty (but added
to <code class="docutils literal notranslate"><span class="pre">MY_INSTALL_KEYWORDS_MISSING_VALUES</span></code>) and <code class="docutils literal notranslate"><span class="pre">MY_INSTALL_OPTIONAL</span></code> will
therefore be set to <code class="docutils literal notranslate"><span class="pre">TRUE</span></code>.</p>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-2-command:function"></span><a class="reference internal" href="function.html#command:function" title="function"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">function()</span></code></a></p></li>
<li><p><span class="target" id="index-1-command:macro"></span><a class="reference internal" href="macro.html#command:macro" title="macro"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">macro()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake_parse_arguments</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake_minimum_required.html"
                          title="previous chapter">cmake_minimum_required</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmake_path.html"
                          title="next chapter">cmake_path</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/cmake_parse_arguments.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="cmake_path.html" title="cmake_path"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake_minimum_required.html" title="cmake_minimum_required"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_parse_arguments</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>