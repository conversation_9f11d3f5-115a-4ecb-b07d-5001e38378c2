// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SystemMetadataQFT_h
#define Uci__Type__SystemMetadataQFT_h 1

#if !defined(Uci__Type__QueryFilterPET_h)
# include "uci/type/QueryFilterPET.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__MetadataID_Type_h)
# include "uci/type/MetadataID_Type.h"
#endif

#if !defined(Uci__Type__SystemID_Type_h)
# include "uci/type/SystemID_Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SystemMetadataQFT sequence accessor class */
      class SystemMetadataQFT : public virtual uci::type::QueryFilterPET {
      public:

         /** The destructor */
         virtual ~SystemMetadataQFT()
         { }

         /** Returns this accessor's type constant, i.e. SystemMetadataQFT
           *
           * @return This accessor's type constant, i.e. SystemMetadataQFT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::systemMetadataQFT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SystemMetadataQFT& accessor)
            throw(uci::base::UCIException) = 0;


         /** [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::type::MetadataID_Type, uci::type::accessorType::metadataID_Type> MetadataID;

         /** [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::type::SystemID_Type, uci::type::accessorType::systemID_Type> SystemID;

         /** Returns the bounded list that is identified by the MetadataID.
           *
           * @return The bounded list identified by MetadataID.
           */
         virtual const uci::type::SystemMetadataQFT::MetadataID& getMetadataID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the MetadataID.
           *
           * @return The bounded list identified by MetadataID.
           */
         virtual uci::type::SystemMetadataQFT::MetadataID& getMetadataID()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the MetadataID.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setMetadataID(const uci::type::SystemMetadataQFT::MetadataID& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SystemID.
           *
           * @return The bounded list identified by SystemID.
           */
         virtual const uci::type::SystemMetadataQFT::SystemID& getSystemID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SystemID.
           *
           * @return The bounded list identified by SystemID.
           */
         virtual uci::type::SystemMetadataQFT::SystemID& getSystemID()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SystemID.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSystemID(const uci::type::SystemMetadataQFT::SystemID& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SystemMetadataQFT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SystemMetadataQFT to copy from
           */
         SystemMetadataQFT(const SystemMetadataQFT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SystemMetadataQFT to the contents of the SystemMetadataQFT on the
           * right hand side (rhs) of the assignment operator.SystemMetadataQFT [only available to derived classes]
           *
           * @param rhs The SystemMetadataQFT on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::SystemMetadataQFT
           * @return A constant reference to this SystemMetadataQFT.
           */
         const SystemMetadataQFT& operator=(const SystemMetadataQFT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SystemMetadataQFT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SystemMetadataQFT_h

