// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TaskRiskAdjustmentType_h
#define Uci__Type__TaskRiskAdjustmentType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__TaskTypeRiskAdjustmentType_h)
# include "uci/type/TaskTypeRiskAdjustmentType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SystemID_Type_h)
# include "uci/type/SystemID_Type.h"
#endif

#if !defined(Uci__Type__RiskSettingType_h)
# include "uci/type/RiskSettingType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TaskRiskAdjustmentType sequence accessor class */
      class TaskRiskAdjustmentType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TaskRiskAdjustmentType()
         { }

         /** Returns this accessor's type constant, i.e. TaskRiskAdjustmentType
           *
           * @return This accessor's type constant, i.e. TaskRiskAdjustmentType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::taskRiskAdjustmentType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TaskRiskAdjustmentType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Identifies which vehicles or types of vehicles this adjustment applies to. [Minimum occurrences: 0] [Maximum
           * occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SystemID_Type, uci::type::accessorType::systemID_Type> AppliesToSystemID;

         /** Identifies the Risk Level adjustment to be made. [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::type::RiskSettingType, uci::type::accessorType::riskSettingType> RiskLevel;

         /** Returns the accessor that provides access to the complex content that is identified by the Task.
           *
           * @return The acecssor that provides access to the complex content that is identified by Task.
           */
         virtual const uci::type::TaskTypeRiskAdjustmentType& getTask() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Task.
           *
           * @return The acecssor that provides access to the complex content that is identified by Task.
           */
         virtual uci::type::TaskTypeRiskAdjustmentType& getTask()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Task to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Task
           */
         virtual void setTask(const uci::type::TaskTypeRiskAdjustmentType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the AppliesToSystemID.
           *
           * @return The bounded list identified by AppliesToSystemID.
           */
         virtual const uci::type::TaskRiskAdjustmentType::AppliesToSystemID& getAppliesToSystemID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the AppliesToSystemID.
           *
           * @return The bounded list identified by AppliesToSystemID.
           */
         virtual uci::type::TaskRiskAdjustmentType::AppliesToSystemID& getAppliesToSystemID()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the AppliesToSystemID.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setAppliesToSystemID(const uci::type::TaskRiskAdjustmentType::AppliesToSystemID& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the RiskLevel.
           *
           * @return The bounded list identified by RiskLevel.
           */
         virtual const uci::type::TaskRiskAdjustmentType::RiskLevel& getRiskLevel() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the RiskLevel.
           *
           * @return The bounded list identified by RiskLevel.
           */
         virtual uci::type::TaskRiskAdjustmentType::RiskLevel& getRiskLevel()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the RiskLevel.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setRiskLevel(const uci::type::TaskRiskAdjustmentType::RiskLevel& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TaskRiskAdjustmentType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TaskRiskAdjustmentType to copy from
           */
         TaskRiskAdjustmentType(const TaskRiskAdjustmentType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TaskRiskAdjustmentType to the contents of the
           * TaskRiskAdjustmentType on the right hand side (rhs) of the assignment operator.TaskRiskAdjustmentType [only available
           * to derived classes]
           *
           * @param rhs The TaskRiskAdjustmentType on the right hand side (rhs) of the assignment operator whose contents are used
           *      to set the contents of this uci::type::TaskRiskAdjustmentType
           * @return A constant reference to this TaskRiskAdjustmentType.
           */
         const TaskRiskAdjustmentType& operator=(const TaskRiskAdjustmentType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TaskRiskAdjustmentType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TaskRiskAdjustmentType_h

