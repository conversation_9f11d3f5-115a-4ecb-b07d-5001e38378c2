// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StrengthRangeType_h
#define Uci__Type__StrengthRangeType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__UnsignedIntAccessor_h)
# include "uci/base/UnsignedIntAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This type defines the strength range for a track. */
      class StrengthRangeType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~StrengthRangeType()
         { }

         /** Returns this accessor's type constant, i.e. StrengthRangeType
           *
           * @return This accessor's type constant, i.e. StrengthRangeType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::strengthRangeType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StrengthRangeType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Minimum.
           *
           * @return The value of the simple primitive data type identified by Minimum.
           */
         virtual xs::UnsignedInt getMinimum() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Minimum.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setMinimum(xs::UnsignedInt value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Minimum exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Minimum is emabled or not
           */
         virtual bool hasMinimum() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Minimum
           *
           * @param type = uci::base::accessorType::unsignedIntAccessor This Accessor's accessor type
           */
         virtual void enableMinimum(uci::base::accessorType::AccessorType type = uci::base::accessorType::unsignedIntAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Minimum */
         virtual void clearMinimum()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Maximum.
           *
           * @return The value of the simple primitive data type identified by Maximum.
           */
         virtual xs::UnsignedInt getMaximum() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Maximum.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setMaximum(xs::UnsignedInt value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Maximum exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Maximum is emabled or not
           */
         virtual bool hasMaximum() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Maximum
           *
           * @param type = uci::base::accessorType::unsignedIntAccessor This Accessor's accessor type
           */
         virtual void enableMaximum(uci::base::accessorType::AccessorType type = uci::base::accessorType::unsignedIntAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Maximum */
         virtual void clearMaximum()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StrengthRangeType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StrengthRangeType to copy from
           */
         StrengthRangeType(const StrengthRangeType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StrengthRangeType to the contents of the StrengthRangeType on the
           * right hand side (rhs) of the assignment operator.StrengthRangeType [only available to derived classes]
           *
           * @param rhs The StrengthRangeType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::StrengthRangeType
           * @return A constant reference to this StrengthRangeType.
           */
         const StrengthRangeType& operator=(const StrengthRangeType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StrengthRangeType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StrengthRangeType_h

