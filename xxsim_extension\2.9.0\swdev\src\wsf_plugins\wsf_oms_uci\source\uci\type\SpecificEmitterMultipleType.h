// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SpecificEmitterMultipleType_h
#define Uci__Type__SpecificEmitterMultipleType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SpecificEmitterIdentityConfidenceType_h)
# include "uci/type/SpecificEmitterIdentityConfidenceType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SpecificEmitterMultipleType sequence accessor class */
      class SpecificEmitterMultipleType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SpecificEmitterMultipleType()
         { }

         /** Returns this accessor's type constant, i.e. SpecificEmitterMultipleType
           *
           * @return This accessor's type constant, i.e. SpecificEmitterMultipleType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::specificEmitterMultipleType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SpecificEmitterMultipleType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates a possible specific emitter type instance that has been identified in association with the Emitter. The
           * value of the child Confidence element of all instances of this element should sum to 100%. Specific Emitter
           * Identification (SEI) technology passively measures emitters, and characterizes unique emitter signatures that can be
           * used for friendly or enemy emitter identification and fingerprinting. A SEI and this SpecificEmitter element refer to
           * a specific physical instance of a type of emitter as given. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SpecificEmitterIdentityConfidenceType, uci::type::accessorType::specificEmitterIdentityConfidenceType> SpecificEmitterInstance;

         /** Returns the bounded list that is identified by the SpecificEmitterInstance.
           *
           * @return The bounded list identified by SpecificEmitterInstance.
           */
         virtual const uci::type::SpecificEmitterMultipleType::SpecificEmitterInstance& getSpecificEmitterInstance() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SpecificEmitterInstance.
           *
           * @return The bounded list identified by SpecificEmitterInstance.
           */
         virtual uci::type::SpecificEmitterMultipleType::SpecificEmitterInstance& getSpecificEmitterInstance()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SpecificEmitterInstance.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSpecificEmitterInstance(const uci::type::SpecificEmitterMultipleType::SpecificEmitterInstance& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SpecificEmitterMultipleType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SpecificEmitterMultipleType to copy from
           */
         SpecificEmitterMultipleType(const SpecificEmitterMultipleType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SpecificEmitterMultipleType to the contents of the
           * SpecificEmitterMultipleType on the right hand side (rhs) of the assignment operator.SpecificEmitterMultipleType [only
           * available to derived classes]
           *
           * @param rhs The SpecificEmitterMultipleType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SpecificEmitterMultipleType
           * @return A constant reference to this SpecificEmitterMultipleType.
           */
         const SpecificEmitterMultipleType& operator=(const SpecificEmitterMultipleType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SpecificEmitterMultipleType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SpecificEmitterMultipleType_h

