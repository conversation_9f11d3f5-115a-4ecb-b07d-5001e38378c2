
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>message &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="option" href="option.html" />
    <link rel="prev" title="math" href="math.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="option.html" title="option"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="math.html" title="math"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">message</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="message">
<span id="command:message"></span><h1>message<a class="headerlink" href="#message" title="Permalink to this heading">¶</a></h1>
<p>Log a message.</p>
<section id="synopsis">
<h2>Synopsis<a class="headerlink" href="#synopsis" title="Permalink to this heading">¶</a></h2>
<pre class="literal-block"><a class="reference internal" href="#general-messages">General messages</a>
  message([&lt;mode&gt;] &quot;message text&quot; ...)

<a class="reference internal" href="#reporting-checks">Reporting checks</a>
  message(&lt;checkState&gt; &quot;message text&quot; ...)

<a class="reference internal" href="#configure-log">Configure Log</a>
  message(CONFIGURE_LOG &lt;text&gt;...)</pre>
</section>
<section id="general-messages">
<h2>General messages<a class="headerlink" href="#general-messages" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">message(</span><span class="p">[</span><span class="nv">&lt;mode&gt;</span><span class="p">]</span><span class="w"> </span><span class="s">&quot;message text&quot;</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Record the specified message text in the log.  If more than one message
string is given, they are concatenated into a single message with no
separator between the strings.</p>
<p>The optional <code class="docutils literal notranslate"><span class="pre">&lt;mode&gt;</span></code> keyword determines the type of message, which
influences the way the message is handled:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">FATAL_ERROR</span></code></dt><dd><p>CMake Error, stop processing and generation.</p>
<p>The <span class="target" id="index-0-manual:cmake(1)"></span><a class="reference internal" href="../manual/cmake.1.html#manual:cmake(1)" title="cmake(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake(1)</span></code></a> executable will return a non-zero
<a class="reference internal" href="../manual/cmake.1.html#cmake-exit-code"><span class="std std-ref">exit code</span></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SEND_ERROR</span></code></dt><dd><p>CMake Error, continue processing, but skip generation.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WARNING</span></code></dt><dd><p>CMake Warning, continue processing.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">AUTHOR_WARNING</span></code></dt><dd><p>CMake Warning (dev), continue processing.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DEPRECATION</span></code></dt><dd><p>CMake Deprecation Error or Warning if variable
<span class="target" id="index-0-variable:CMAKE_ERROR_DEPRECATED"></span><a class="reference internal" href="../variable/CMAKE_ERROR_DEPRECATED.html#variable:CMAKE_ERROR_DEPRECATED" title="CMAKE_ERROR_DEPRECATED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_ERROR_DEPRECATED</span></code></a> or <span class="target" id="index-0-variable:CMAKE_WARN_DEPRECATED"></span><a class="reference internal" href="../variable/CMAKE_WARN_DEPRECATED.html#variable:CMAKE_WARN_DEPRECATED" title="CMAKE_WARN_DEPRECATED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_WARN_DEPRECATED</span></code></a>
is enabled, respectively, else no message.</p>
</dd>
<dt>(none) or <code class="docutils literal notranslate"><span class="pre">NOTICE</span></code></dt><dd><p>Important message printed to stderr to attract user's attention.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">STATUS</span></code></dt><dd><p>The main interesting messages that project users might be interested in.
Ideally these should be concise, no more than a single line, but still
informative.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VERBOSE</span></code></dt><dd><p>Detailed informational messages intended for project users.  These messages
should provide additional details that won't be of interest in most cases,
but which may be useful to those building the project when they want deeper
insight into what's happening.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DEBUG</span></code></dt><dd><p>Detailed informational messages intended for developers working on the
project itself as opposed to users who just want to build it.  These messages
will not typically be of interest to other users building the project and
will often be closely related to internal implementation details.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TRACE</span></code></dt><dd><p>Fine-grained messages with very low-level implementation details.  Messages
using this log level would normally only be temporary and would expect to be
removed before releasing the project, packaging up the files, etc.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.15: </span>Added the <code class="docutils literal notranslate"><span class="pre">NOTICE</span></code>, <code class="docutils literal notranslate"><span class="pre">VERBOSE</span></code>, <code class="docutils literal notranslate"><span class="pre">DEBUG</span></code>, and <code class="docutils literal notranslate"><span class="pre">TRACE</span></code> levels.</p>
</div>
<p>The CMake command-line tool displays <code class="docutils literal notranslate"><span class="pre">STATUS</span></code> to <code class="docutils literal notranslate"><span class="pre">TRACE</span></code> messages on stdout
with the message preceded by two hyphens and a space.  All other message types
are sent to stderr and are not prefixed with hyphens.  The
<span class="target" id="index-0-manual:cmake-gui(1)"></span><a class="reference internal" href="../manual/cmake-gui.1.html#manual:cmake-gui(1)" title="cmake-gui(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">CMake</span> <span class="pre">GUI</span></code></a> displays all messages in its log area.
The <span class="target" id="index-0-manual:ccmake(1)"></span><a class="reference internal" href="../manual/ccmake.1.html#manual:ccmake(1)" title="ccmake(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">curses</span> <span class="pre">interface</span></code></a> shows <code class="docutils literal notranslate"><span class="pre">STATUS</span></code> to <code class="docutils literal notranslate"><span class="pre">TRACE</span></code>
messages one at a time on a status line and other messages in an
interactive pop-up box.  The <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-log-level"><code class="xref std std-option docutils literal notranslate"><span class="pre">--log-level</span></code></a>
command-line option to each of these tools can be used to control which
messages will be shown.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.17: </span>To make a log level persist between CMake runs, the
<span class="target" id="index-0-variable:CMAKE_MESSAGE_LOG_LEVEL"></span><a class="reference internal" href="../variable/CMAKE_MESSAGE_LOG_LEVEL.html#variable:CMAKE_MESSAGE_LOG_LEVEL" title="CMAKE_MESSAGE_LOG_LEVEL"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MESSAGE_LOG_LEVEL</span></code></a> variable can be set instead.
Note that the command line option takes precedence over the cache variable.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.16: </span>Messages of log levels <code class="docutils literal notranslate"><span class="pre">NOTICE</span></code> and below will have each line preceded
by the content of the <span class="target" id="index-0-variable:CMAKE_MESSAGE_INDENT"></span><a class="reference internal" href="../variable/CMAKE_MESSAGE_INDENT.html#variable:CMAKE_MESSAGE_INDENT" title="CMAKE_MESSAGE_INDENT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MESSAGE_INDENT</span></code></a> variable (converted to
a single string by concatenating its list items).  For <code class="docutils literal notranslate"><span class="pre">STATUS</span></code> to <code class="docutils literal notranslate"><span class="pre">TRACE</span></code>
messages, this indenting content will be inserted after the hyphens.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.17: </span>Messages of log levels <code class="docutils literal notranslate"><span class="pre">NOTICE</span></code> and below can also have each line preceded
with context of the form <code class="docutils literal notranslate"><span class="pre">[some.context.example]</span></code>.  The content between the
square brackets is obtained by converting the <span class="target" id="index-0-variable:CMAKE_MESSAGE_CONTEXT"></span><a class="reference internal" href="../variable/CMAKE_MESSAGE_CONTEXT.html#variable:CMAKE_MESSAGE_CONTEXT" title="CMAKE_MESSAGE_CONTEXT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MESSAGE_CONTEXT</span></code></a>
list variable to a dot-separated string.  The message context will always
appear before any indenting content but after any automatically added leading
hyphens. By default, message context is not shown, it has to be explicitly
enabled by giving the <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-log-context"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">--log-context</span></code></a>
command-line option or by setting the <span class="target" id="index-0-variable:CMAKE_MESSAGE_CONTEXT_SHOW"></span><a class="reference internal" href="../variable/CMAKE_MESSAGE_CONTEXT_SHOW.html#variable:CMAKE_MESSAGE_CONTEXT_SHOW" title="CMAKE_MESSAGE_CONTEXT_SHOW"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MESSAGE_CONTEXT_SHOW</span></code></a>
variable to true.  See the <span class="target" id="index-1-variable:CMAKE_MESSAGE_CONTEXT"></span><a class="reference internal" href="../variable/CMAKE_MESSAGE_CONTEXT.html#variable:CMAKE_MESSAGE_CONTEXT" title="CMAKE_MESSAGE_CONTEXT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MESSAGE_CONTEXT</span></code></a> documentation for
usage examples.</p>
</div>
<p>CMake Warning and Error message text displays using a simple markup
language.  Non-indented text is formatted in line-wrapped paragraphs
delimited by newlines.  Indented text is considered pre-formatted.</p>
</section>
<section id="reporting-checks">
<h2>Reporting checks<a class="headerlink" href="#reporting-checks" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>A common pattern in CMake output is a message indicating the start of some
sort of check, followed by another message reporting the result of that check.
For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">message(</span><span class="no">STATUS</span><span class="w"> </span><span class="s">&quot;Looking for someheader.h&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="c">#... do the checks, set checkSuccess with the result</span>
<span class="nf">if(</span><span class="nb">checkSuccess</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">message(</span><span class="no">STATUS</span><span class="w"> </span><span class="s">&quot;Looking for someheader.h - found&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">else()</span><span class="w"></span>
<span class="w">  </span><span class="nf">message(</span><span class="no">STATUS</span><span class="w"> </span><span class="s">&quot;Looking for someheader.h - not found&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">endif()</span><span class="w"></span>
</pre></div>
</div>
<p>This can be more robustly and conveniently expressed using the <code class="docutils literal notranslate"><span class="pre">CHECK_...</span></code>
keyword form of the <code class="docutils literal notranslate"><span class="pre">message()</span></code> command:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">message(</span><span class="nv">&lt;checkState&gt;</span><span class="w"> </span><span class="s">&quot;message&quot;</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">&lt;checkState&gt;</span></code> must be one of the following:</p>
<blockquote>
<div><dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">CHECK_START</span></code></dt><dd><p>Record a concise message about the check about to be performed.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CHECK_PASS</span></code></dt><dd><p>Record a successful result for a check.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CHECK_FAIL</span></code></dt><dd><p>Record an unsuccessful result for a check.</p>
</dd>
</dl>
</div></blockquote>
<p>When recording a check result, the command repeats the message from the most
recently started check for which no result has yet been reported, then some
separator characters and then the message text provided after the
<code class="docutils literal notranslate"><span class="pre">CHECK_PASS</span></code> or <code class="docutils literal notranslate"><span class="pre">CHECK_FAIL</span></code> keyword.  Check messages are always reported
at <code class="docutils literal notranslate"><span class="pre">STATUS</span></code> log level.</p>
<p>Checks may be nested and every <code class="docutils literal notranslate"><span class="pre">CHECK_START</span></code> should have exactly one
matching <code class="docutils literal notranslate"><span class="pre">CHECK_PASS</span></code> or <code class="docutils literal notranslate"><span class="pre">CHECK_FAIL</span></code>.
The <span class="target" id="index-1-variable:CMAKE_MESSAGE_INDENT"></span><a class="reference internal" href="../variable/CMAKE_MESSAGE_INDENT.html#variable:CMAKE_MESSAGE_INDENT" title="CMAKE_MESSAGE_INDENT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MESSAGE_INDENT</span></code></a> variable can also be used to add
indenting to nested checks if desired.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">message(</span><span class="no">CHECK_START</span><span class="w"> </span><span class="s">&quot;Finding my things&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">list(</span><span class="no">APPEND</span><span class="w"> </span><span class="no">CMAKE_MESSAGE_INDENT</span><span class="w"> </span><span class="s">&quot;  &quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">unset(</span><span class="nb">missingComponents</span><span class="nf">)</span><span class="w"></span>

<span class="nf">message(</span><span class="no">CHECK_START</span><span class="w"> </span><span class="s">&quot;Finding partA&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="c"># ... do check, assume we find A</span>
<span class="nf">message(</span><span class="no">CHECK_PASS</span><span class="w"> </span><span class="s">&quot;found&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="nf">message(</span><span class="no">CHECK_START</span><span class="w"> </span><span class="s">&quot;Finding partB&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="c"># ... do check, assume we don&#39;t find B</span>
<span class="nf">list(</span><span class="no">APPEND</span><span class="w"> </span><span class="nb">missingComponents</span><span class="w"> </span><span class="no">B</span><span class="nf">)</span><span class="w"></span>
<span class="nf">message(</span><span class="no">CHECK_FAIL</span><span class="w"> </span><span class="s">&quot;not found&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="nf">list(</span><span class="no">POP_BACK</span><span class="w"> </span><span class="no">CMAKE_MESSAGE_INDENT</span><span class="nf">)</span><span class="w"></span>
<span class="nf">if(</span><span class="nb">missingComponents</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">message(</span><span class="no">CHECK_FAIL</span><span class="w"> </span><span class="s">&quot;missing components: ${missingComponents}&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">else()</span><span class="w"></span>
<span class="w">  </span><span class="nf">message(</span><span class="no">CHECK_PASS</span><span class="w"> </span><span class="s">&quot;all components found&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">endif()</span><span class="w"></span>
</pre></div>
</div>
<p>Output from the above would appear something like the following:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>-- Finding my things
--   Finding partA
--   Finding partA - found
--   Finding partB
--   Finding partB - not found
-- Finding my things - missing components: B
</pre></div>
</div>
</section>
<section id="configure-log">
<h2>Configure Log<a class="headerlink" href="#configure-log" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">message(</span><span class="no">CONFIGURE_LOG</span><span class="w"> </span><span class="nv">&lt;text&gt;...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Record a <a class="reference internal" href="../manual/cmake-configure-log.7.html#message-configure-log-event"><span class="std std-ref">configure-log message event</span></a>
with the specified <code class="docutils literal notranslate"><span class="pre">&lt;text&gt;</span></code>.  By convention, if the text contains more
than one line, the first line should be a summary of the event.</p>
<p>This mode is intended to record the details of a system inspection check
or other one-time operation guarded by a cache entry, but that is not
performed using <span class="target" id="index-0-command:try_compile"></span><a class="reference internal" href="try_compile.html#command:try_compile" title="try_compile"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">try_compile()</span></code></a> or <span class="target" id="index-0-command:try_run"></span><a class="reference internal" href="try_run.html#command:try_run" title="try_run"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">try_run()</span></code></a>, which
automatically log their details.  Projects should avoid calling it every
time CMake runs.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">if</span> <span class="nf">(</span><span class="no">NOT</span><span class="w"> </span><span class="no">DEFINED</span><span class="w"> </span><span class="no">MY_CHECK_RESULT</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="c"># Print check summary in configure output.</span>
<span class="w">  </span><span class="nf">message(</span><span class="no">CHECK_START</span><span class="w"> </span><span class="s">&quot;My Check&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="w">  </span><span class="c"># ... perform system inspection, e.g., with execute_process ...</span>

<span class="w">  </span><span class="c"># Cache the result so we do not run the check again.</span>
<span class="w">  </span><span class="nf">set(</span><span class="no">MY_CHECK_RESULT</span><span class="w"> </span><span class="s">&quot;${MY_CHECK_RESULT}&quot;</span><span class="w"> </span><span class="no">CACHE</span><span class="w"> </span><span class="no">INTERNAL</span><span class="w"> </span><span class="s">&quot;My Check&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="w">  </span><span class="c"># Record the check details in the cmake-configure-log.</span>
<span class="w">  </span><span class="nf">message(</span><span class="no">CONFIGURE_LOG</span><span class="w"></span>
<span class="w">    </span><span class="s">&quot;My Check Result: ${MY_CHECK_RESULT}\n&quot;</span><span class="w"></span>
<span class="w">    </span><span class="s">&quot;${details}&quot;</span><span class="w"></span>
<span class="w">  </span><span class="nf">)</span><span class="w"></span>

<span class="w">  </span><span class="c"># Print check result in configure output.</span>
<span class="w">  </span><span class="nf">if(</span><span class="no">MY_CHECK_RESULT</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">message(</span><span class="no">CHECK_PASS</span><span class="w"> </span><span class="s">&quot;passed&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">else()</span><span class="w"></span>
<span class="w">    </span><span class="nf">message(</span><span class="no">CHECK_FAIL</span><span class="w"> </span><span class="s">&quot;failed&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">endif()</span><span class="w"></span>
<span class="nf">endif()</span><span class="w"></span>
</pre></div>
</div>
<p>If no project is currently being configured, such as in
<a class="reference internal" href="../manual/cmake.1.html#script-processing-mode"><span class="std std-ref">cmake -P</span></a> script mode,
this command does nothing.</p>
</section>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-0-command:cmake_language"></span><a class="reference internal" href="cmake_language.html#command:cmake_language" title="cmake_language"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_language(GET_MESSAGE_LOG_LEVEL)</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">message</a><ul>
<li><a class="reference internal" href="#synopsis">Synopsis</a></li>
<li><a class="reference internal" href="#general-messages">General messages</a></li>
<li><a class="reference internal" href="#reporting-checks">Reporting checks</a></li>
<li><a class="reference internal" href="#configure-log">Configure Log</a></li>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="math.html"
                          title="previous chapter">math</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="option.html"
                          title="next chapter">option</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/message.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="option.html" title="option"
             >next</a> |</li>
        <li class="right" >
          <a href="math.html" title="math"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">message</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>