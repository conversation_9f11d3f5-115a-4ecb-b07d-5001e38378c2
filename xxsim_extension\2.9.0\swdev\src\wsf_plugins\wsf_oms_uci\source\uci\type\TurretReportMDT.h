// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TurretReportMDT_h
#define Uci__Type__TurretReportMDT_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SupportCapabilityID_Type_h)
# include "uci/type/SupportCapabilityID_Type.h"
#endif

#if !defined(Uci__Type__TurretReportPointingType_h)
# include "uci/type/TurretReportPointingType.h"
#endif

#if !defined(Uci__Type__PointingType_h)
# include "uci/type/PointingType.h"
#endif

#if !defined(Uci__Type__IMU_AlignmentEnum_h)
# include "uci/type/IMU_AlignmentEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TurretReportMDT sequence accessor class */
      class TurretReportMDT : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TurretReportMDT()
         { }

         /** Returns this accessor's type constant, i.e. TurretReportMDT
           *
           * @return This accessor's type constant, i.e. TurretReportMDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::turretReportMDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TurretReportMDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SupportCapabilityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SupportCapabilityID.
           */
         virtual const uci::type::SupportCapabilityID_Type& getSupportCapabilityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SupportCapabilityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SupportCapabilityID.
           */
         virtual uci::type::SupportCapabilityID_Type& getSupportCapabilityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SupportCapabilityID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SupportCapabilityID
           */
         virtual void setSupportCapabilityID(const uci::type::SupportCapabilityID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Pointing.
           *
           * @return The acecssor that provides access to the complex content that is identified by Pointing.
           */
         virtual const uci::type::TurretReportPointingType& getPointing() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Pointing.
           *
           * @return The acecssor that provides access to the complex content that is identified by Pointing.
           */
         virtual uci::type::TurretReportPointingType& getPointing()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Pointing to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Pointing
           */
         virtual void setPointing(const uci::type::TurretReportPointingType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TurretPointingTarget.
           *
           * @return The acecssor that provides access to the complex content that is identified by TurretPointingTarget.
           */
         virtual const uci::type::PointingType& getTurretPointingTarget() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TurretPointingTarget.
           *
           * @return The acecssor that provides access to the complex content that is identified by TurretPointingTarget.
           */
         virtual uci::type::PointingType& getTurretPointingTarget()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TurretPointingTarget to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TurretPointingTarget
           */
         virtual void setTurretPointingTarget(const uci::type::PointingType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the IMU_State.
           *
           * @return The value of the enumeration identified by IMU_State.
           */
         virtual const uci::type::IMU_AlignmentEnum& getIMU_State() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the IMU_State.
           *
           * @return The value of the enumeration identified by IMU_State.
           */
         virtual uci::type::IMU_AlignmentEnum& getIMU_State()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the IMU_State.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setIMU_State(const uci::type::IMU_AlignmentEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the IMU_State.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setIMU_State(uci::type::IMU_AlignmentEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield IMU_Stateis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasIMU_State() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getIMU_State will return the optional field and not throw an exception when
           * invoked.
           *
           * @param type = uci::type::accessorType::iMU_AlignmentEnum This Accessor's accessor type
           */
         virtual void enableIMU_State(uci::base::accessorType::AccessorType type = uci::type::accessorType::iMU_AlignmentEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearIMU_State()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TurretReportMDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TurretReportMDT to copy from
           */
         TurretReportMDT(const TurretReportMDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TurretReportMDT to the contents of the TurretReportMDT on the
           * right hand side (rhs) of the assignment operator.TurretReportMDT [only available to derived classes]
           *
           * @param rhs The TurretReportMDT on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::TurretReportMDT
           * @return A constant reference to this TurretReportMDT.
           */
         const TurretReportMDT& operator=(const TurretReportMDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TurretReportMDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TurretReportMDT_h

