# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************

include_once rules/brawler_script_util.txt

include  processors/uplink.txt

# File generated by Wizard 2.7.0 on Sep 22, 2020.
// Brawler RULES Instantiation
include_once rules/rultype_change.txt
include_once rules/ftr_rules.txt
include_once rules/route_rules.txt
include_once rules/orbit_rules.txt
include_once rules/intercept_rules.txt
# weapon decision logic - targeting selection and shooting
#include_once rules/tgt_bias.txt
include_once rules/select_wpn.txt
include_once rules/shoot_it.txt
include_once rules/sam_shoot_it.txt


// ftr_rules behavior nodes (phases)
include_once rules/ingress.txt
include_once rules/direct.txt
include_once rules/vectoring.txt
include_once rules/sam_vctr.txt
include_once rules/reaction.txt
include_once rules/pump.txt
include_once rules/pump_in.txt
include_once rules/crank.txt
include_once rules/merge.txt
include_once rules/egress.txt
// sensors
include_once processors/sensor_cue_processor.txt

# This brawler enabled platform has the following defined:
# 1. A WSF_BRAWLER_PROCESSOR    (typically named task_mgr or thinker)
# 2. A WSF_BRAWLER_MOVER        (possibly could use WSF_P6DOF_MOVER in future)
# 3. A WSF_PERCEPTION_PROCESSOR (typically named perception)
# 4. A WSF_THREAT_PROCESSOR     (typically named incoming_threats)
# 5. A WSF_BRAWLER_FUEL         (optional: the brawler mover implicitely models brawler fuel provided by the 'aero_file')

filter KAL_FILTER WSF_KALMAN_FILTER
#   range_measurement_sigma 100 ft     #overwritten by sensor
#   bearing_measurement_sigma 0.1 deg   #overwritten by sensor
#   elevation_measurement_sigma 0.1 deg #overwritten by sensor
   #
   process_noise_model constant_velocity
   process_noise_sigmas_XYZ 2 2 2
end_filter

platform_type  BRAWLER_TEST  WSF_PLATFORM
   icon f15c
   category AIRCRAFT
   category aircraft
   category brawler
   category EZJA

   
   aux_data 
      bool captured = false // set to true if captured which is brawler lingo meaning landed at a base
   end_aux_data

/*define global script variables attached to this platform type
  these are used in the EZJA behavior tree
*/
   include rules/ezja_script_variables.txt

   processor thinker WSF_BRAWLER_PROCESSOR
      mind_file platforms/mind
      consciousness_event_update_time 10 s
   end_processor 
   
   comm datalink WSF_COMM_TRANSCEIVER
      propagation_speed 0 m/s   #instantaneous propogation (faster than speed of light)
      network_name brawler_net
      internal_link data_mgr
      internal_link tsk_mgr
      internal_link thinker
      internal_link perception
      internal_link assessment
   end_comm

   comm wpn_datalink WSF_COMM_XMTR 
      propagation_speed 0 m/s   #instantaneous propogation (faster than speed of light)
      network_name weapons_subnet
      internal_link data_mgr
      internal_link raw_data_mgr
   end_comm

   processor raw_data_mgr WSF_TRACK_PROCESSOR
      non_master_track_processor 
      purge_interval  60 sec
      update_interval 2.0 sec
      report_interval 2.0 sec 
      drop_after_inactive 30 sec
      
#      unchanged_track_reporting false   #TEST
#      report_raw_tracks 
#      report_fused_tracks
#      circular_report_rejection enable
#      internal_link data_mgr
      // fuze my own ship tracks, link to master track processor
      track_manager 
         fusion_method weighted_average       
         end_fusion_method
      end_track_manager
   end_processor

   processor data_mgr WSF_TRACK_PROCESSOR 
      master_track_processor 
      purge_interval  60 sec
      report_interval 10.0 sec 
      unchanged_track_reporting false   #TEST
      circular_report_rejection enable
#      report_raw_tracks 
      internal_link tsk_mgr
      report_fused_tracks 
#      report_to command_chain IFLITE commander via datalink
#      report_to command_chain IFLITE peers via datalink
#      report_to command_chain IFLITE subordinates via datalink
      report_to commander via datalink
      report_to subordinates via datalink
      report_to peers via datalink
      report_to subordinates via wpn_datalink to uplink
   end_processor

   track_manager 
      fusion_method weighted_average
      end_fusion_method
   end_track_manager
   
   processor tsk_mgr WSF_TASK_PROCESSOR 
      on
      internal_link data_mgr
   end_processor

   processor perception WSF_PERCEPTION_PROCESSOR
      update_interval  0.5 s             // time interval sim will call processor
      reports_self on                  // Specifies whether or not to report out asset status messages about this platform
      report_interval 6.0 sec          // specifies the interval at which asset state messages are sent to friends
#      asset_perception truth subordinates:commander:peers  // asset perception will utilize received WSF_ASSET_MESSAGE messages
      
      threat_update_interval 10 s       // interval at which the processor updates its threat perception
      max_threat_load 0               // max percievable threat entities
      max_asset_load 20
#      report_to commander via datalink
#      report_to subordinates via datalink
#      report_to peers via datalink
#      report_to command_chain IFLITE commander via datalink
#      report_to command_chain IFLITE peers via datalink
#      report_to command_chain IFLITE subordinates via datalink   
   end_processor
   
   // processor is not used within the behavior tree but must exist on the platform in order to use Brawler Processor
   processor incoming_threats WSF_THREAT_PROCESSOR
      threat_time_to_intercept 120 sec   #default: 60 sec
   end_processor
     
   processor assessment WSF_SA_PROCESSOR 
      on
#     update_interval 0.1 sec
      update_interval 1.0 sec

      //This includes the following blocks/scripts: 
      // script_variables, on_initialize, on_update, BogieBanditConsiderationScoring, CalculateRiskPosedByEntity, CalculateDefensivenessInducedByEntity, 
      // CalculateUrgencyInducedByEntity, CalcRisk, CalcSelfRisk, CalcFlightRisk, CalcPackageRisk, CalcMissionRisk, CalcDefensiveness, 
      // CalcUrgency, CalcWeaponSupport, CalculateThreatLevel, CalculateTargetValue,   
      include processors/assessment_scripts.txt
      
      # Flight Information
      flight_id  1
      id_flag    1
      
      # Joker/Bingo Fuel States
      joker_fuel 4000 lbs
      bingo_fuel 3000 lbs
      
#       filter_assets_from_bogies false
#       filter_assets_from_tracks false         
      
      # Data collection rates
      report_interval                              1.00 sec
      engagement_data_update_interval              1.00 sec
      flight_data_update_interval                  1.00 sec
      fuel_data_update_interval                    1.00 sec
      nav_data_update_interval                     1.00 sec
      flight_controls_data_update_interval         1.00 sec
      weapons_data_update_interval                 1.00 sec
      track_data_update_interval                   1.00 sec
      asset_data_update_interval                   1.00 sec
      perceived_item_data_update_interval          1.00 sec
      prioritized_item_data_update_interval        1.00 sec      
      perceived_item_calculation_update_interval   1.00 sec
      prioritized_item_calculation_update_interval 1.00 sec      
      
      enemy_side     red
      enemy_type     RED_STRIKER
      friendly_type  BLUE_STRIKER

      max_range_for_perceived_assets              150 nm
      max_range_for_perceived_bogies_and_bandits  150 nm
      max_range_for_engagement_data               200 nm
      assumed_range_for_angle_only_targets        150 nm      
      
      #filter_requires_same_side
      #filter_requires_not_same_side          true
      filter_requires_air_domain              true
      #filter_requires_not_air_domain         true
      #filter_requires_land_or_surface_domain true
      #filter_requires_not_subsurface_domain  true
      #filter_requires_not_space_domain       true
      filter_requires_sa_processor            true

      report_to commander    via datalink
      report_to peers        via datalink
      report_to subordinates via datalink

      # Assets/friends settings
      asset_perception           truth commander:peers:subordinates
      perceive_self              false
      reporting_self             true
      reporting_others           true
      max_asset_load             100

      # Bogie and bandit/threat settings
      max_threat_load            100
      
      # Expendable countermeasures
      use_simple_countermeasures true
      num_chaff                  60
      num_flares                 40
      num_decoys                 20  

      advanced_behavior_tree
         btt on
         #EZJA tactic set implementaion plan, emulates our RULES structure and call tree
         sequence 
            behavior_node rultype //select which RULES type I want

            # *******  PHASE CHANGING LOGIC ********

            selector # execute chosen RULES type
               behavior_node ftr_rules  // brawler rules
               behavior_node route_rules // follow a wsf_route
               behavior_node orbit_rules // cap
               behavior_node intercept_rules // intercept a task track
            end_selector

            selector
//             if rule_type == ftr, these nodes are used
               behavior_node ingress
               behavior_node direct
               behavior_node vectoring
               behavior_node sam_vctr
               behavior_node crank
               behavior_node reaction
               behavior_node pump
               behavior_node pump_in
               behavior_node merge
               behavior_node egress
            end_selector

            # *******  WEAPON EMPLOYMENT DECISION ********

            sequence  # weapon logic
               behavior_node select_wpn     // pair a weapon w/ target track
               behavior_node shoot_it       // loop through shoot list, fire weapon if proper conditions are met
               behavior_node sam_shoot_it   // fire weapon against SAMs if proper conditions are met
            end_sequence
         end_sequence 
      end_advanced_behavior_tree   

   end_processor

end_platform_type

platform_type  BRAWLER_TEST_AIRMOVER  WSF_PLATFORM
   icon f15c
   category AIRCRAFT
   category EZJA

   aux_data 
      bool captured = false // set to true if captured which is brawler lingo meaning landed at a base
   end_aux_data

   # define global script variables attached to this platform type - these are used in the EZJA behavior tree
   include rules/ezja_script_variables.txt

#   processor thinker WSF_BRAWLER_PROCESSOR
#      mind_file platforms/mind
#      consciousness_event_update_time 0.5 s
#   end_processor
   
   processor assessment WSF_SA_PROCESSOR 
      on
#     update_interval 0.1 sec
      update_interval 1.0 sec

      //This includes the following blocks/scripts: 
      // script_variables, on_initialize, on_update, BogieBanditConsiderationScoring, CalculateRiskPosedByEntity, CalculateDefensivenessInducedByEntity, 
      // CalculateUrgencyInducedByEntity, CalcRisk, CalcSelfRisk, CalcFlightRisk, CalcPackageRisk, CalcMissionRisk, CalcDefensiveness, 
      // CalcUrgency, CalcWeaponSupport, CalculateThreatLevel, CalculateTargetValue,   
      include processors/assessment_scripts.txt
      
      # Flight Information
      flight_id  1
      id_flag    1
      
      # Joker/Bingo Fuel States
      joker_fuel 4000 lbs
      bingo_fuel 3000 lbs
      
#       filter_assets_from_bogies false
#       filter_assets_from_tracks false         
      
      # Data collection rates
      report_interval                              1.00 sec
      engagement_data_update_interval              1.00 sec
      flight_data_update_interval                  1.00 sec
      fuel_data_update_interval                    1.00 sec
      nav_data_update_interval                     1.00 sec
      flight_controls_data_update_interval         1.00 sec
      weapons_data_update_interval                 1.00 sec
      track_data_update_interval                   1.00 sec
      asset_data_update_interval                   1.00 sec
      perceived_item_data_update_interval          1.00 sec
      prioritized_item_data_update_interval        1.00 sec      
      perceived_item_calculation_update_interval   1.00 sec
      prioritized_item_calculation_update_interval 1.00 sec       
      
      enemy_side     red
      enemy_type     RED_STRIKER
      friendly_type  BLUE_STRIKER

      max_range_for_perceived_assets              150 nm
      max_range_for_perceived_bogies_and_bandits  150 nm
      max_range_for_engagement_data               200 nm
      assumed_range_for_angle_only_targets        150 nm      
      
      #filter_requires_same_side
      #filter_requires_not_same_side          true
      filter_requires_air_domain              true
      #filter_requires_not_air_domain         true
      #filter_requires_land_or_surface_domain true
      #filter_requires_not_subsurface_domain  true
      #filter_requires_not_space_domain       true
      filter_requires_sa_processor            true

      report_to commander    via datalink
      report_to peers        via datalink
      report_to subordinates via datalink

      # Assets/friends settings
      asset_perception           truth commander:peers:subordinates
      perceive_self              false
      reporting_self             true
      reporting_others           true
      max_asset_load             100

      # Bogie and bandit/threat settings
      max_threat_load            100
      
      # Expendable countermeasures
      use_simple_countermeasures true
      num_chaff                  60
      num_flares                 40
      num_decoys                 20  

      advanced_behavior_tree
         btt on
         #EZJA tactic set implementaion plan, emulates our RULES structure and call tree
         sequence 
            behavior_node rultype //select which RULES type I want
/*
   *******  PHASE CHANGING LOGIC ********
*/
            selector # execute chosen RULES type
               behavior_node ftr_rules  // brawler rules
               behavior_node route_rules // follow a wsf_route
               behavior_node orbit_rules // cap
               behavior_node intercept_rules // intercept a task track
            end_selector

            selector
//             if rule_type == ftr, these nodes are used
               behavior_node ingress
               behavior_node direct
               behavior_node vectoring
               behavior_node sam_vctr
               behavior_node crank
               behavior_node reaction
               behavior_node pump
               behavior_node pump_in
               behavior_node merge
               behavior_node egress
            end_selector
/*
   *******  WEAPON EMPLOYMENT DECISION ********
*/
            sequence  # weapon logic
               behavior_node select_wpn     // pair a weapon w/ target track
               behavior_node shoot_it       // loop through shoot list, fire weapon if proper conditions are met
               behavior_node sam_shoot_it   // fire weapon against SAMs if proper conditions are met
            end_sequence
         end_sequence 
      end_advanced_behavior_tree   

   end_processor
   
   comm datalink WSF_COMM_TRANSCEIVER
      propagation_speed 0 m/s   #instantaneous propogation (faster than speed of light)
      network_name brawler_net
      internal_link data_mgr
      internal_link tsk_mgr
      internal_link thinker
      internal_link perception
      internal_link assessment
   end_comm

   comm wpn_datalink WSF_COMM_XMTR 
      propagation_speed 0 m/s   #instantaneous propogation (faster than speed of light)
      network_name weapons_subnet
      internal_link data_mgr
      internal_link raw_data_mgr
   end_comm

   processor raw_data_mgr WSF_TRACK_PROCESSOR
      non_master_track_processor 
      purge_interval  60 sec
      update_interval 2.0 sec
      report_interval 2.0 sec 
      drop_after_inactive 30 sec
      
#      unchanged_track_reporting false   #TEST
#      report_raw_tracks 
      report_fused_tracks
#      circular_report_rejection enable
      internal_link data_mgr
      // fuze my own ship tracks, link to master track processor
      track_manager 
         fusion_method weighted_average       
         end_fusion_method
      end_track_manager
   end_processor

   processor data_mgr WSF_TRACK_PROCESSOR 
      master_track_processor 
      purge_interval  60 sec
      report_interval 10.0 sec 
      unchanged_track_reporting false   #TEST
      circular_report_rejection enable
#      report_raw_tracks 
      internal_link tsk_mgr
      report_fused_tracks 
#      report_to command_chain IFLITE commander via datalink
#      report_to command_chain IFLITE peers via datalink
#      report_to command_chain IFLITE subordinates via datalink
      report_to commander via datalink
      report_to subordinates via datalink
      report_to peers via datalink
      report_to subordinates via wpn_datalink to uplink
   end_processor

   track_manager 
      fusion_method weighted_average
      end_fusion_method
   end_track_manager
   
   processor tsk_mgr WSF_TASK_PROCESSOR 
      on
      internal_link data_mgr
   end_processor

   processor perception WSF_PERCEPTION_PROCESSOR
      update_interval  0.5 s             // time interval sim will call processor
      reports_self on                  // Specifies whether or not to report out asset status messages about this platform
      report_interval 2.0 sec          // specifies the interval at which asset state messages are sent to friends
      asset_perception truth subordinates:commander:peers  // asset perception will utlize recieved WSF_ASSET_MESSAGE messages
      
      threat_update_interval 1 s       // interval at which the processor updates its threat perception
      max_threat_load 0               // max percievable threat entities
      max_asset_load 20
#      report_to commander via datalink
#      report_to subordinates via datalink
#      report_to peers via datalink
#      report_to command_chain IFLITE commander via datalink
#      report_to command_chain IFLITE peers via datalink
#      report_to command_chain IFLITE subordinates via datalink   
   end_processor
   
   // processor is not used within the behavior tree but must exist on the platform in order to use Brawler Processor
   processor incoming_threats WSF_THREAT_PROCESSOR
      threat_time_to_intercept 120 sec   #default: 60 sec
   end_processor
end_platform_type
#
