
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>install_files &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="install_programs" href="install_programs.html" />
    <link rel="prev" title="export_library_dependencies" href="export_library_dependencies.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="install_programs.html" title="install_programs"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="export_library_dependencies.html" title="export_library_dependencies"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">install_files</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="install-files">
<span id="command:install_files"></span><h1>install_files<a class="headerlink" href="#install-files" title="Permalink to this heading">¶</a></h1>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.0: </span>Use the <span class="target" id="index-0-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(FILES)</span></code></a> command instead.</p>
</div>
<p>This command has been superseded by the <span class="target" id="index-1-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a> command.  It is
provided for compatibility with older CMake code.  The <code class="docutils literal notranslate"><span class="pre">FILES</span></code> form is
directly replaced by the <code class="docutils literal notranslate"><span class="pre">FILES</span></code> form of the <span class="target" id="index-2-command:install"></span><a class="reference internal" href="install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a>
command.  The regexp form can be expressed more clearly using the <code class="docutils literal notranslate"><span class="pre">GLOB</span></code>
form of the <span class="target" id="index-0-command:file"></span><a class="reference internal" href="file.html#command:file" title="file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file()</span></code></a> command.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install_files(</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="nb">extension</span><span class="w"> </span><span class="nb">file</span><span class="w"> </span><span class="nb">file</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Create rules to install the listed files with the given extension into
the given directory.  Only files existing in the current source tree
or its corresponding location in the binary tree may be listed.  If a
file specified already has an extension, that extension will be
removed first.  This is useful for providing lists of source files
such as foo.cxx when you want the corresponding foo.h to be installed.
A typical extension is <code class="docutils literal notranslate"><span class="pre">.h</span></code>.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install_files(</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="nb">regexp</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Any files in the current source directory that match the regular
expression will be installed.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">install_files(</span><span class="nv">&lt;dir&gt;</span><span class="w"> </span><span class="no">FILES</span><span class="w"> </span><span class="nb">file</span><span class="w"> </span><span class="nb">file</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Any files listed after the <code class="docutils literal notranslate"><span class="pre">FILES</span></code> keyword will be installed explicitly
from the names given.  Full paths are allowed in this form.</p>
<p>The directory <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> is relative to the installation prefix, which is
stored in the variable <span class="target" id="index-0-variable:CMAKE_INSTALL_PREFIX"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_PREFIX.html#variable:CMAKE_INSTALL_PREFIX" title="CMAKE_INSTALL_PREFIX"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_PREFIX</span></code></a>.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="export_library_dependencies.html"
                          title="previous chapter">export_library_dependencies</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="install_programs.html"
                          title="next chapter">install_programs</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/install_files.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="install_programs.html" title="install_programs"
             >next</a> |</li>
        <li class="right" >
          <a href="export_library_dependencies.html" title="export_library_dependencies"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">install_files</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>