// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__VulnerabilityLevelsCombinedType_h
#define Uci__Type__VulnerabilityLevelsCombinedType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__VulnerabilityLevelsType_h)
# include "uci/type/VulnerabilityLevelsType.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the VulnerabilityLevelsCombinedType sequence accessor class */
      class VulnerabilityLevelsCombinedType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~VulnerabilityLevelsCombinedType()
         { }

         /** Returns this accessor's type constant, i.e. VulnerabilityLevelsCombinedType
           *
           * @return This accessor's type constant, i.e. VulnerabilityLevelsCombinedType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::vulnerabilityLevelsCombinedType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const VulnerabilityLevelsCombinedType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AcquisitionCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by AcquisitionCostLevels.
           */
         virtual const uci::type::VulnerabilityLevelsType& getAcquisitionCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AcquisitionCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by AcquisitionCostLevels.
           */
         virtual uci::type::VulnerabilityLevelsType& getAcquisitionCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the AcquisitionCostLevels to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by AcquisitionCostLevels
           */
         virtual void setAcquisitionCostLevels(const uci::type::VulnerabilityLevelsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by AcquisitionCostLevels exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by AcquisitionCostLevels is emabled or not
           */
         virtual bool hasAcquisitionCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by AcquisitionCostLevels
           *
           * @param type = uci::type::accessorType::vulnerabilityLevelsType This Accessor's accessor type
           */
         virtual void enableAcquisitionCostLevels(uci::base::accessorType::AccessorType type = uci::type::accessorType::vulnerabilityLevelsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by AcquisitionCostLevels */
         virtual void clearAcquisitionCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TrackCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by TrackCostLevels.
           */
         virtual const uci::type::VulnerabilityLevelsType& getTrackCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TrackCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by TrackCostLevels.
           */
         virtual uci::type::VulnerabilityLevelsType& getTrackCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TrackCostLevels to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TrackCostLevels
           */
         virtual void setTrackCostLevels(const uci::type::VulnerabilityLevelsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TrackCostLevels exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TrackCostLevels is emabled or not
           */
         virtual bool hasTrackCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TrackCostLevels
           *
           * @param type = uci::type::accessorType::vulnerabilityLevelsType This Accessor's accessor type
           */
         virtual void enableTrackCostLevels(uci::base::accessorType::AccessorType type = uci::type::accessorType::vulnerabilityLevelsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TrackCostLevels */
         virtual void clearTrackCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the InterceptCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by InterceptCostLevels.
           */
         virtual const uci::type::VulnerabilityLevelsType& getInterceptCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the InterceptCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by InterceptCostLevels.
           */
         virtual uci::type::VulnerabilityLevelsType& getInterceptCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the InterceptCostLevels to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by InterceptCostLevels
           */
         virtual void setInterceptCostLevels(const uci::type::VulnerabilityLevelsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by InterceptCostLevels exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by InterceptCostLevels is emabled or not
           */
         virtual bool hasInterceptCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by InterceptCostLevels
           *
           * @param type = uci::type::accessorType::vulnerabilityLevelsType This Accessor's accessor type
           */
         virtual void enableInterceptCostLevels(uci::base::accessorType::AccessorType type = uci::type::accessorType::vulnerabilityLevelsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by InterceptCostLevels */
         virtual void clearInterceptCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the LaunchCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by LaunchCostLevels.
           */
         virtual const uci::type::VulnerabilityLevelsType& getLaunchCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the LaunchCostLevels.
           *
           * @return The acecssor that provides access to the complex content that is identified by LaunchCostLevels.
           */
         virtual uci::type::VulnerabilityLevelsType& getLaunchCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the LaunchCostLevels to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by LaunchCostLevels
           */
         virtual void setLaunchCostLevels(const uci::type::VulnerabilityLevelsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by LaunchCostLevels exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by LaunchCostLevels is emabled or not
           */
         virtual bool hasLaunchCostLevels() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by LaunchCostLevels
           *
           * @param type = uci::type::accessorType::vulnerabilityLevelsType This Accessor's accessor type
           */
         virtual void enableLaunchCostLevels(uci::base::accessorType::AccessorType type = uci::type::accessorType::vulnerabilityLevelsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by LaunchCostLevels */
         virtual void clearLaunchCostLevels()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the ProbabilityOfSurvival.
           *
           * @return The value of the simple primitive data type identified by ProbabilityOfSurvival.
           */
         virtual uci::type::PercentTypeValue getProbabilityOfSurvival() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the ProbabilityOfSurvival.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setProbabilityOfSurvival(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by ProbabilityOfSurvival exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by ProbabilityOfSurvival is emabled or not
           */
         virtual bool hasProbabilityOfSurvival() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by ProbabilityOfSurvival
           *
           * @param type = uci::type::accessorType::percentType This Accessor's accessor type
           */
         virtual void enableProbabilityOfSurvival(uci::base::accessorType::AccessorType type = uci::type::accessorType::percentType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by ProbabilityOfSurvival */
         virtual void clearProbabilityOfSurvival()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         VulnerabilityLevelsCombinedType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The VulnerabilityLevelsCombinedType to copy from
           */
         VulnerabilityLevelsCombinedType(const VulnerabilityLevelsCombinedType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this VulnerabilityLevelsCombinedType to the contents of the
           * VulnerabilityLevelsCombinedType on the right hand side (rhs) of the assignment
           * operator.VulnerabilityLevelsCombinedType [only available to derived classes]
           *
           * @param rhs The VulnerabilityLevelsCombinedType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::VulnerabilityLevelsCombinedType
           * @return A constant reference to this VulnerabilityLevelsCombinedType.
           */
         const VulnerabilityLevelsCombinedType& operator=(const VulnerabilityLevelsCombinedType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // VulnerabilityLevelsCombinedType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__VulnerabilityLevelsCombinedType_h

