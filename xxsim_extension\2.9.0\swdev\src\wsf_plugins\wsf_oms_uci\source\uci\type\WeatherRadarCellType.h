// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WeatherRadarCellType_h
#define Uci__Type__WeatherRadarCellType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__WeatherRadarCellLocationType_h)
# include "uci/type/WeatherRadarCellLocationType.h"
#endif

#if !defined(Uci__Base__DoubleAccessor_h)
# include "uci/base/DoubleAccessor.h"
#endif

#if !defined(Uci__Type__WeatherAreaDataType_h)
# include "uci/type/WeatherAreaDataType.h"
#endif

#if !defined(Uci__Type__WeatherWarningEnum_h)
# include "uci/type/WeatherWarningEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WeatherRadarCellType sequence accessor class */
      class WeatherRadarCellType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WeatherRadarCellType()
         { }

         /** Returns this accessor's type constant, i.e. WeatherRadarCellType
           *
           * @return This accessor's type constant, i.e. WeatherRadarCellType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::weatherRadarCellType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WeatherRadarCellType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CellLocation.
           *
           * @return The acecssor that provides access to the complex content that is identified by CellLocation.
           */
         virtual const uci::type::WeatherRadarCellLocationType& getCellLocation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the CellLocation.
           *
           * @return The acecssor that provides access to the complex content that is identified by CellLocation.
           */
         virtual uci::type::WeatherRadarCellLocationType& getCellLocation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the CellLocation to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by CellLocation
           */
         virtual void setCellLocation(const uci::type::WeatherRadarCellLocationType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Reflectivity.
           *
           * @return The value of the simple primitive data type identified by Reflectivity.
           */
         virtual xs::Double getReflectivity() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Reflectivity.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setReflectivity(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherData.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherData.
           */
         virtual const uci::type::WeatherAreaDataType& getWeatherData() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherData.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherData.
           */
         virtual uci::type::WeatherAreaDataType& getWeatherData()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WeatherData to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WeatherData
           */
         virtual void setWeatherData(const uci::type::WeatherAreaDataType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by WeatherData exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by WeatherData is emabled or not
           */
         virtual bool hasWeatherData() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by WeatherData
           *
           * @param type = uci::type::accessorType::weatherAreaDataType This Accessor's accessor type
           */
         virtual void enableWeatherData(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherAreaDataType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by WeatherData */
         virtual void clearWeatherData()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the WeatherWarning.
           *
           * @return The value of the enumeration identified by WeatherWarning.
           */
         virtual const uci::type::WeatherWarningEnum& getWeatherWarning() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the WeatherWarning.
           *
           * @return The value of the enumeration identified by WeatherWarning.
           */
         virtual uci::type::WeatherWarningEnum& getWeatherWarning()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the WeatherWarning.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWeatherWarning(const uci::type::WeatherWarningEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the WeatherWarning.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWeatherWarning(uci::type::WeatherWarningEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield WeatherWarningis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasWeatherWarning() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getWeatherWarning will return the optional field and not throw an exception when
           * invoked.
           *
           * @param type = uci::type::accessorType::weatherWarningEnum This Accessor's accessor type
           */
         virtual void enableWeatherWarning(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherWarningEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearWeatherWarning()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WeatherRadarCellType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WeatherRadarCellType to copy from
           */
         WeatherRadarCellType(const WeatherRadarCellType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WeatherRadarCellType to the contents of the WeatherRadarCellType
           * on the right hand side (rhs) of the assignment operator.WeatherRadarCellType [only available to derived classes]
           *
           * @param rhs The WeatherRadarCellType on the right hand side (rhs) of the assignment operator whose contents are used
           *      to set the contents of this uci::type::WeatherRadarCellType
           * @return A constant reference to this WeatherRadarCellType.
           */
         const WeatherRadarCellType& operator=(const WeatherRadarCellType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WeatherRadarCellType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WeatherRadarCellType_h

