# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************

/*
PURPOSE  Sam Vector behavior node  
AUTHOR   Armstrong
Classification: UNCLASSIFIED//FOUO

Technical Description: Describes tactics for when engaging SAMs

*/

advanced_behavior sam_vctr
   script_variables 
      extern string faz_desired;
      extern bool change_desired;
      extern WsfPlatform iacid;
      extern string reason;
      extern WsfCommandChain iflite;
      extern bool pitch_up;
      extern double pr_altitude;
      extern double pr_speed;
      extern double pr_heading;
      extern double pr_gees;
      extern bool first_pass;
      extern double time_ok;
      extern bool alerted;
      extern bool apole_tct;
      extern bool sam_threatnd;
      extern double rng_cls_sam;
      extern double rng_cls_hst;
      extern bool pump_per;
      extern bool threatnd;
      extern bool needil;
#      extern WsfBrawlerProcessor BRAWLER;
      extern Atmosphere atmos;
      extern WsfSA_EntityPerception ppmjid;
      extern double t_phase;
      extern Array<double> VECTOR_FORMATION;
      extern Array<int> PLAYBOOK;      
   end_script_variables


   precondition 
      ////// Evaluate conditions that would prevent behavior alternative from running
      if (!PROCESSOR.IsA_TypeOf("WSF_SA_PROCESSOR"))
      {
         writeln_d("not a brawler processor!");
         return Failure("behavior not attached to a WSF_SA_PROCESSOR");
      } 

      if (PLATFORM->rule_type == "ftr" && PLATFORM->faz == "sam_vctr")
      { return Success(); }
      else
      { return Failure(reason); }
 
   end_precondition

   execute
      bool BrawlMover;
      WsfBrawlerProcessor BRAWLER;
      if (iacid.Mover().Type() == "WSF_BRAWLER_MOVER")
      {
         for (int i=0; i<iacid.ProcessorCount();i+=1)
         {
            if (iacid.ProcessorEntry(i).Type() == "WSF_BRAWLER_PROCESSOR")
            {
               BrawlMover = true;
               BRAWLER = (WsfBrawlerProcessor)iacid.ProcessorEntry(i);
               break;
            }
         }
      }   
      faz_desired = "sam_vctr"; 
      int plan_1 = PLAYBOOK[0]; 
      int plan_2 = PLAYBOOK[1]; // create local version of these to manipulate in this routine
      int plan_3 = PLAYBOOK[2];
      
      // set the speed based on commit speed and if I want to sprint above the mach on the first pass
      if(first_pass)
      {
         pr_speed = (iacid->COMMIT_SPD + iacid->SPRINT_SPD)*atmos.SonicVelocity(iacid.Altitude());
      }
      else
      {
         pr_speed = iacid->COMMIT_SPD * atmos.SonicVelocity(iacid.Altitude());
      }
      pr_heading = pr_sam_vctr_hdg(iacid); // calculate desired heading to fly on
      pr_altitude = iacid->COMMIT_ALT; // set desired altitude

      // fly on the desired vector
      if (BrawlMover)
      {
         pr_gees=BRAWLER.MaxSustainedGs();
      }
      else
      {
         pr_gees=3;
      }
      pr_vector(iacid,pr_heading,pr_speed,pr_altitude,pr_gees);
      if ( alerted || rng_cls_hst <= iacid->DOR )
      {  
          if ( plan_3 == 1 && pump_per && time_ok)
          {  
            faz_desired = "pump";
             reason = "ALWAYS PUMP AT MAR";
             t_phase=TIME_NOW + iacid->TDRAG;        
          }
          else if ( plan_3 == 2 && pump_per && time_ok && threatnd)
          {  
            faz_desired = "pump";
             reason = "ALWAYS PUMP WHEN THREATENED";
             t_phase=TIME_NOW + iacid->TDRAG;
          }
         else if (needil && apole_tct) // crank tactics
         {  
            faz_desired = "crank";
            reason = "SUPPORTING MISSILES";
            t_phase = TIME_NOW + 10;
         }
      }   
      else if (needil && apole_tct) // crank tactics
      {  
         faz_desired = "crank";
         reason = "SUPPORTING MISSILES";
         t_phase=TIME_NOW + 10;
      }
      else if (rng_cls_hst > iacid->COMMIT_RNG*1.5 && rng_cls_sam > iacid->COMMIT_RNG*1.5)
      {  
         faz_desired = "ingress";
         reason = "WEAPONS AND FUEL LEFT";
      }
      else if (ppmjid.Track().IsValid() && ppmjid.Track().AirDomain())
      {
         faz_desired = "vectoring";
         reason = "ENGAGING AIRCRAFT";
         t_phase=TIME_NOW + 10;
      }
      else if ((rng_cls_sam - iacid->SAM_PMP_RNG) > (rng_cls_hst - iacid->DOR))
      {
//    Hostile aircraft is closer to threatend range than SAM is to SAM pump range => switch to vectoring
         faz_desired = "vectoring";
         reason = "ENGAGE AIRCRAFT";
         t_phase=TIME_NOW + 10;
      }   
      return Success(reason);            
   end_execute

end_advanced_behavior
