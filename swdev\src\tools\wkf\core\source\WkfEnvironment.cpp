// ****************************************************************************
// CUI
//
// The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
//
// Copyright 2016 Infoscitex, a DCS Company. All rights reserved.
//
// The use, dissemination or disclosure of data in this file is subject to
// limitation or restriction. See accompanying README and LICENSE for details.
// ****************************************************************************

#if defined(_WIN32)
#include <windows.h>
#endif

#include "WkfEnvironment.hpp"

#include <QApplication>
#include <QMenu>
#include <QMessageBox>
#include <QStyle>
#include <QStyleFactory>
#include <QTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QNetworkReply>
#include <QUrlQuery>

#include "UtLog.hpp"
#include "UtLogPublisher.hpp"
#include "UtMemory.hpp"
#include "UtPath.hpp"
#include "UtPathParser.hpp"
#include "UtRunEnvManager.hpp"
#include "UtoShaders.hpp"
#include "VaModelDatabase.hpp"
#include "VaResourceManager.hpp"
#include "VaTimeManager.hpp"
#include "VaUtils.hpp"
#include "WkfConfigurationObject.hpp"
#include "WkfFactory.hpp"
#include "WkfGeneralPrefObject.hpp"
#include "WkfMainWindow.hpp"
#include "WkfObserver.hpp"
#include "WkfPermissionDialog.hpp"
#include "WkfPermissionManager.hpp"
#include "WkfPlatform.hpp"
#include "WkfPluginManager.hpp"
#include "WkfProxyStyle.hpp"
#include "WkfScenario.hpp"
#include "WkfUpdater.hpp"
#include "WkfVtkEnvironment.hpp"

#ifdef WKFCODETIMERS_AVAILABLE
#include "WkfCodeTimer.hpp"
#endif


// This is here to support Scene dumps, which is available only in debug
#include <cassert>

#include "VaOSG_ScenegraphBrowserQt.hpp"

namespace
{
QTimer* timerPtr = nullptr;
}

namespace DcsInfo
{
    char mDcsReceBuffer[1024 * 1024 * 10];
}


wkf::Environment* wkf::Environment::mInstancePtr = nullptr;

wkf::Environment::Environment(const QString& aApplicationName,
                              const QString& aApplicationPrefix,
                              const QString& aSettingsFile,
                              bool           aImportSettings,
                              const QString& aPermissionFile)
   : mStarted(false)
   , mUserSettings(nullptr)
   , mInitialUserSettings(nullptr)
   , mFrameUpdatedEnabled(false)
   , mMainWindowPtr(nullptr)
   , mPluginManagerPtr(new PluginManager(aApplicationName))
   , mApplication(aApplicationName)
   , mApplicationPrefix(aApplicationPrefix)
   , mResourcesDir("")
   , mSourceRoot("")
   , mFormationInfo("")
   , mCapturePath("")
   , m_DcsSimuationTime(0)
   , m_FormationCallbackNum(1)
   , mPermissions(ut::make_unique<wkf::permission::Manager>())
   , mLockFileLoad(false)
{
   // Maps hardware exceptions to ut::HardwareException.
   // Does nothing if PROMOTE_HARDWARE_EXCEPTIONS CMake flag is not set.
   ut::SetupThreadErrorHandling();

   // Check if an instance already exists
   if (mInstancePtr != nullptr)
   {
      ut::log::fatal() << "Only one instance of WkfEnvironment may exist at any time.";
      exit(1);
   }

   // By setting the instance pointer in the constructor we allow
   // users to derive from WkfEnvironment, which is a singleton.
   mInstancePtr = this;

   // Utilize the Fusion application style and display Light theme icons by default
   qApp->setStyle(new ProxyStyle(QStyleFactory::create("Fusion")));
   QIcon::setThemeName("Light");

   mSharedDataDir   = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "//..//WKF_Shared";
   mAppDataDir      = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
   mAppDataSettings = new QSettings(mAppDataDir + "//AppData.ini", QSettings::IniFormat, this);

   // Look up resources directory to determine if this is a development build running in Visual Studio or an installed build
   UtPath path = UtRunEnvManager::GetRunPath();
   path.Up();
   std::string pathStr      = path.GetSystemPath() + "/resources/";
   UtPath      resourcePath = pathStr;
   // This path should exist in all installed loads
   if (resourcePath.Exists())
   {
      mResourcesDir = QString::fromStdString(pathStr);
   }
   else
   {
      { // RAII block
         auto out = ut::log::info() << "Directory not found, assuming development build paths.";
         out.AddNote() << "Directory: " << pathStr;
      }

      // Use the developer path to the resources directory
      const char* sourceRoot   = ::getenv("SOURCE_ROOT");
      const char* resourcePath = ::getenv("RESOURCE_PATH");
      if (sourceRoot && resourcePath)
      {
         mSourceRoot   = sourceRoot;
         mResourcesDir = resourcePath;
      }
      else
      {
         ut::log::warning() << "Resource not found.";
      }
   }

   mSharedUserSettings    = new QSettings(mSharedDataDir + "//Settings.ini", QSettings::IniFormat, this);
   mUserSettings          = new QSettings(mAppDataDir + "//Settings.ini", QSettings::IniFormat, this);
   mInitialSharedSettings = mSharedUserSettings;
   mInitialUserSettings   = mUserSettings;
   if (!aSettingsFile.isEmpty())
   {
      if (QFile(aSettingsFile).exists())
      {
         mInitialUserSettings   = new QSettings(aSettingsFile, QSettings::IniFormat, this);
         mInitialSharedSettings = mInitialUserSettings;
         if (!aImportSettings)
         {
            mSharedUserSettings = mInitialSharedSettings;
            mUserSettings       = mInitialUserSettings;
         }
      }
      else
      {
         auto out = ut::log::error() << "Could not load configuration file.";
         out.AddNote() << "File: " << aSettingsFile.toStdString();
      }
   }

   if (aPermissionFile.isEmpty())
   {
      mPermissionSettings = new QSettings(mAppDataDir + "//Permissions.ini", QSettings::IniFormat, this);
   }
   else
   {
      if (QFile(aPermissionFile).exists())
      {
         mPermissionSettings = new QSettings(aPermissionFile, QSettings::IniFormat, this);
      }
      else
      {
         auto out = ut::log::error() << "Could not load permission file.";
         out.AddNote() << "File: " << aPermissionFile.toStdString();
      }
   }

   mCallbacks.Add(Observer::EntityRemoved.Connect(&Environment::EntityRemovedCB, this));
   connect(this, &Environment::PlatformVisibilityChanged, this, &Environment::PlatformVisibilityChangedHandler);
   connect(this, &Environment::SignalSendCommandTask, this, &Environment::SendCommandTask);
   connect(this, &Environment::SignalGetValue, this, [&](QString type) {
       if (mDcsThread)
       {
           mDcsThread->GetSimInfo(type);
       }
    });
   // This is here to support Scene dumps, which is available only in debug, via right click on map window
   vespa::VaOSG_ScenegraphBrowserQt::InitializeSubscriptions();

   mMainWindowPtr = new MainWindow;
   mMainWindowPtr->InitializeDialogs();

   mResourceManager = ut::make_unique<ResourceManager>();

   QString modelDirFile = mResourcesDir + "/models/milStdIconMappings.csv";
   if (!mSourceRoot.empty())
   {
      modelDirFile = QString::fromStdString(mSourceRoot) + "/tools/vespatk/models/milStdIconMappings.csv";
   }
   QString siteDirFile = mResourcesDir + "/site/milStdIconMappings.csv";
   mIconLoader         = ut::make_unique<WkfMilStd2525d>(QStringList{modelDirFile, siteDirFile});

   // Manually set up the Theme Object so it can be set the palette to be used immediately after WkfEnvironment
   // creation. This allows dialog to be display prior to Wk Environment initializing, such as a startup dialog.
   GetPreferenceObject<GeneralPrefObject>()->LoadSettings(*mInitialUserSettings, *mInitialSharedSettings);

   ut::log::Publisher::SetConsoleEnabled(false);

}

wkf::Environment::~Environment()
{
   ut::log::Publisher::SetConsoleEnabled(true);
   // Visibility Filters registered from plugin will not be cleaned properly if the plugin
   //  is deleted prior to the std::function within the Visibility Filter is cleaned up.
   //  Therefore clear the filters prior to deleting the Plugin Manager (which deletes the plugins)
   mPlatformVisibilityFilters.clear();

   mSidePerspectiveCallback.clear();

   mDamageStatisticsCallback.clear();

   mFormationCallback.clear();

   mTaskBrowerCallback.clear();

   mPrintScreenCallback.clear();
   // Since permissions may include types defined in plugin DLLs, they need to be deleted before
   //  the DLL that defines the destructor is closed.
   mPermissions.reset();

   // Deleting the MainWindow will delete the children of the MainWindow also.  These children may
   //  refer to Wkf::Environment and try to get the MainWindow pointer, which is bad.  Therefore,
   //  set the MainWindow pointer to nullptr, then delete the memory, which allows the check to be
   //  performed against nullptr prior to using the MainWindow pointer return by the Environment.
   auto tempPtr   = mMainWindowPtr;
   mMainWindowPtr = nullptr;
   delete tempPtr;

   delete mPluginManagerPtr;

   mInstancePtr = nullptr;
}

void wkf::Environment::TimerHandler()
{
   vaEnv.GetTime()->UpdateFrame();
   if (mFrameUpdatedEnabled)
   {
      emit UpdateFrame();
   }
}

wkf::Environment& wkf::Environment::GetInstance()
{
   assert(mInstancePtr != nullptr);
   return *mInstancePtr;
}

void wkf::Environment::Create(const QString& aApplicationName,
                              const QString& aApplicationPrefix,
                              const QString& aSettings,
                              bool           aImportSettings,
                              const QString& aPermissionFile)
{
   assert(mInstancePtr == nullptr);
   if (mInstancePtr == nullptr)
   {
      new Environment(aApplicationName, aApplicationPrefix, aSettings, aImportSettings, aPermissionFile);
   }
}

bool wkf::Environment::Exists()
{
   return (mInstancePtr != nullptr);
}

void wkf::Environment::StartUp()
{
   if (!mStarted)
   {
      mStarted = true;

      mApplicationTips = new TipContainer(this);

      // Initialize Main Window after data classes because child dialogs may depend on data classes.
      if (SetupVespaEnvironment())
      {
         for (auto&& it : mPrefObjectMap)
         {
            mMainWindowPtr->GetConfigurationObject()->RegisterPrefObject(it.second);
         }

#ifdef WKFCODETIMERS_AVAILABLE
         auto ct = new CodeTimerObject(mMainWindowPtr); // qobject, mainwindow will clean it up
#endif

         // Settings for plugin manager will determine which plugins get auto-started in call to Initialize()
         mPluginManagerPtr->LoadSettings(*mInitialUserSettings);
         // Load plugins after any dependencies they may have like the Main Window
         mPluginManagerPtr->Initialize();

         emit Initialize();

         mMainWindowPtr->GetConfigurationObject()->LoadSettings(*mInitialUserSettings, *mInitialSharedSettings);
         Observer::LoadSettings(*mInitialUserSettings);

         GetPermissions().ReadFile(GetPermissionFile());
         mMainWindowPtr->GetPermissionDialog()->SetActionVisible(!GetPermissions().IsEmpty());

         mMainWindowPtr->show();

         // Create 30 Hz timer for frame updates
         timerPtr = new QTimer(this);
         connect(timerPtr, &QTimer::timeout, this, &Environment::TimerHandler);
         timerPtr->start(33);

         

          mObsTimerPtr = new QTimer(this);
          connect(mObsTimerPtr, &QTimer::timeout, this, [&]() {
              if (mDcsThread)
              {
                  mDcsThread->GetSimInfo("obs");
              }
              });
          

          InitDcsThread();
      }
   }
}

void wkf::Environment::LoadSettings(QSettings& aSettings)
{
   Observer::LoadSettings(aSettings);
   emit UpdatePlatformOptions();
}

void wkf::Environment::LoadModelDatabase()
{
   std::string resourcePath = UtRunEnvManager::GetCmeRelBase() + "/models/";
   if (!mSourceRoot.empty())
   {
      std::string path = mSourceRoot + "/tools/vespatk/models/models.txt";
      if (UtPathParser::FileExists(path))
      {
         VtkEnvironment::Instance().GetModelDatabase()->LoadModelSet(mSourceRoot + "/tools/vespatk/models/models.txt",
                                                                     true,
                                                                     resourcePath);
      }
   }
   else
   {
      std::string path = resourcePath + "models.txt";
      if (UtPathParser::FileExists(path))
      {
         VtkEnvironment::Instance().GetModelDatabase()->LoadModelSet(UtRunEnvManager::GetCmeRelBase() +
                                                                        "/models/models.txt",
                                                                     true);
      }
   }
   std::string siteList = UtRunEnvManager::GetCmeRelBase() + "/site/models.txt";
   if (UtPathParser::FileExists(siteList))
   {
      VtkEnvironment::Instance().GetModelDatabase()->LoadModelSet(siteList, false);
   }
   vaEnv.GetModelDatabase()->ReloadModels();
}

QStringList wkf::Environment::GetApplicationTips() const
{
   if (mApplicationTips)
   {
      return mApplicationTips->GetTips();
   }
   return QStringList();
}

void wkf::Environment::Shutdown()
{
   if (mInstancePtr)
   {
      if (timerPtr)
      {
         timerPtr->stop();
      }
      CleanupVespaEnvironment();
      delete mInstancePtr;
   }
}

bool wkf::Environment::SetupVespaEnvironment()
{
   if (LoadVespaResources())
   {
      // Initialize the VTK environment.
      if (!vaEnv.Initialize())
      {
         ut::log::error() << "VespaTk environment has failed to initialize, shutting down...";
         return false;
      }

      return true;
   }
   return false;
}

void wkf::Environment::CleanupVespaEnvironment()
{
   VtkEnvironment::Shutdown();
}

bool wkf::Environment::LoadVespaResources()
{
   bool loadedResources = true;

   UtRunEnvManager::Setup(nullptr, mResourcesDir.toStdString().c_str());

   std::string resourcePath = UtRunEnvManager::GetCmeRelBase() + "/models/";
   if (!mSourceRoot.empty())
   {
      std::string path = mSourceRoot + "/tools/vespatk/models/models.txt";
      if (UtPathParser::FileExists(path))
      {
         VtkEnvironment::Instance().GetModelDatabase()->LoadModelSet(mSourceRoot + "/tools/vespatk/models/models.txt",
                                                                     true,
                                                                     resourcePath);
      }
   }
   else
   {
      std::string path = resourcePath + "models.txt";
      if (UtPathParser::FileExists(path))
      {
         VtkEnvironment::Instance().GetModelDatabase()->LoadModelSet(UtRunEnvManager::GetCmeRelBase() +
                                                                        "/models/models.txt",
                                                                     true);
      }
   }
   std::string siteList = UtRunEnvManager::GetCmeRelBase() + "/site/models.txt";
   if (UtPathParser::FileExists(siteList))
   {
      VtkEnvironment::Instance().GetModelDatabase()->LoadModelSet(siteList, false);
   }

   if (!mSourceRoot.empty())
   {
      UtoShaders::AddShaderDirectory(mSourceRoot + "/tools/vespatk/shaders");
   }

   std::string shadersDir = UtRunEnvManager::GetCmeFolder("shaders");
   UtoShaders::AddShaderDirectory(shadersDir);

   std::string resources = UtRunEnvManager::GetCmeFolder("data") + "/resources.txt";

   if (!vespa::VaResourceManager::Instance().LoadResources(resources))
   {
      std::string path = mSourceRoot;
      path += "/tools/vespatk/data";
      vespa::VaUtils::AddApplicationDataPath(path);
      resources = path + "/resources.txt";
      if (!vespa::VaResourceManager::Instance().LoadResources(resources))
      {
         auto out = ut::log::error() << "Could not load the VESPA resources.";
         out.AddNote() << "Resources: " << resources;
         out.AddNote() << "Base: " << UtRunEnvManager::GetCmeRelBase();
         out.AddNote() << "Suffix: " << UtRunEnvManager::GetCmeRelSuffix();
         loadedResources = false;
      }
   }
   return loadedResources;
}

void wkf::Environment::EntityRemovedCB(vespa::VaEntity* aEntityPtr)
{
   auto* platform = dynamic_cast<Platform*>(aEntityPtr);
   if (platform)
   {
      if (IsPlatformSelected(platform))
      {
         SetPlatformSelected(platform, false);
      }
   }
}

QString wkf::Environment::GetDocumentationDir() const
{
   return QString::fromStdString(UtRunEnvManager::GetRunPath() + "/../documentation");
}

void wkf::Environment::BuildEntityContextMenu(QMenu* aMenu, wkf::Entity* aEntity)
{
   auto plugins = mPluginManagerPtr->GetLoadedPlugins();
   for (auto& p : plugins)
   {
      p.second->BuildEntityContextMenu(aMenu, aEntity);
   }
}

void wkf::Environment::BuildViewerContextMenu(QMenu* aMenu, vespa::VaViewer* aViewerPtr)
{
   auto plugins = mPluginManagerPtr->GetLoadedPlugins();
   for (auto& p : plugins)
   {
      p.second->BuildViewerContextMenu(aMenu, aViewerPtr);
   }
}

void wkf::Environment::ClearPlatformGroupings(int aIndex)
{
   auto it = mPlatformToGroupingMap.find(aIndex);
   if (it != mPlatformToGroupingMap.end())
   {
      // clear the groupings for the particular platform
      for (auto& cit : it->second)
      {
         auto jt = mGroupingToPlatformMap[cit].find(aIndex);
         if (jt != mGroupingToPlatformMap[cit].end())
         {
            mGroupingToPlatformMap[cit].erase(jt);
         }
      }
      // remove any groupings that do not contain any platforms
      for (auto iter = mGroupingToPlatformMap.begin(); iter != mGroupingToPlatformMap.end();)
      {
         if (iter->second.empty())
         {
            // remove the grouping
            iter = mGroupingToPlatformMap.erase(iter);
         }
         else
         {
            ++iter;
         }
      }

      mPlatformToGroupingMap.erase(it);
   }
}

void wkf::Environment::SetPlatformGrouping(int aIndex, const PlatformGrouping aGrouping)
{
   bool newGroup = false;
   mPlatformToGroupingMap[aIndex].insert(aGrouping);
   // check to see whether or not it is a new grouping
   if (mGroupingToPlatformMap.find(aGrouping) == mGroupingToPlatformMap.end())
   {
      newGroup = true;
   }
   mGroupingToPlatformMap[aGrouping].insert(aIndex);
   // add the new grouping
   if (newGroup)
   {
      emit NewGrouping(aGrouping.Type(), QString::fromStdString(aGrouping.Name()), aIndex);
   }
   else
   {
      emit GroupingChanged(aGrouping.Type(), QString::fromStdString(aGrouping.Name()), aIndex);
   }
}

void wkf::Environment::RegisterPrefObject(PrefObject* aObjectPtr)
{
   mPrefObjectMap[aObjectPtr->GetName()] = aObjectPtr;
   if (mMainWindowPtr && mMainWindowPtr->GetConfigurationObject())
   {
      mMainWindowPtr->GetConfigurationObject()->RegisterPrefObject(aObjectPtr);
   }
}

void wkf::Environment::UnregisterPrefObject(PrefObject* aObjectPtr)
{
   mPrefObjectMap.erase(aObjectPtr->GetName());
}

size_t wkf::Environment::RegisterPlatformVisibilityFilter(const VisibilityFilter& aFilter)
{
   size_t id = mNextRegistrationId++;
   mPlatformVisibilityFilters.emplace(id, aFilter);
   emit PlatformVisibilityChanged();
   return id;
}

void wkf::Environment::UnregisterPlatformVisibilityFilter(size_t aId)
{
   if (mPlatformVisibilityFilters.erase(aId))
   {
      emit PlatformVisibilityChanged();
   }
}

bool wkf::Environment::IsPlatformVisible(const wkf::Platform* aPlatform)
{
   if (aPlatform)
   {
      bool visible = true;
      // Check all of the visibility filters to see if the Platform is visible
      for (auto& pair : mPlatformVisibilityFilters)
      {
         visible = pair.second(*aPlatform);
         // If a single filter return false, we can quit processing and return false
         if (!visible)
         {
            return false;
         }
      }
      // If all filters return true, then return true
      return true;
   }
   return false;
}

QStringList wkf::Environment::GetUpdaterList() const
{
   return mUpdaterFactory.keys();
}

std::unique_ptr<wkf::Updater> wkf::Environment::GetUpdater(const QString& aUpdaterId, const std::string& aPlatformName) const
{
   auto createFn = mUpdaterFactory.find(aUpdaterId);
   if (createFn != std::end(mUpdaterFactory))
   {
      return (*createFn)(aPlatformName);
   }
   return nullptr;
}

void wkf::Environment::SetFrameUpdateEnabled(bool aEnabled)
{
   // If transitioning to no longer emit UpdateFrame,
   //  first emit one last UpdateFrame, so that anyone connected objects can process remaining data
   if (mFrameUpdatedEnabled && !aEnabled)
   {
      emit UpdateFrame();
   }
   mFrameUpdatedEnabled = aEnabled;
}

wkf::Platform* wkf::Environment::GetPlatformOfInterest() const
{
   if (!mSelectedPlatforms.empty())
   {
      return mSelectedPlatforms.back();
   }
   else
   {
      return nullptr;
   }
}

void wkf::Environment::SetPlatformSelected(Platform* aPlatform, bool aFocus)
{
   if (aPlatform)
   {
      if (aFocus != IsPlatformSelected(aPlatform)) // If selection of platform is changing
      {
         // If selecting a new platform or un-selecting the platform of interest, notify subscribers with the updated
         // platform of interest
         bool platformOfInterestNeedsUpdate = aFocus || (aPlatform == GetPlatformOfInterest());

         if (aFocus)
         {
            mSelectedPlatforms.push_back(aPlatform);
            emit PlatformSelectionChanged({aPlatform}, {});
         }
         else
         {
            mSelectedPlatforms.removeOne(aPlatform);
            emit PlatformSelectionChanged({}, {aPlatform});
         }

         if (platformOfInterestNeedsUpdate)
         {
            emit PlatformOfInterestChanged(GetPlatformOfInterest());
         }
      }
   }
}

// Platforms that become invisible should not longer be selected
void wkf::Environment::PlatformVisibilityChangedHandler()
{
   wkf::Scenario* scenario = vaEnv.GetStandardScenario();
   if (scenario != nullptr)
   {
      wkf::PlatformList   platformList;
      vespa::VaEntityList list;
      scenario->GetEntityList(list);
      for (auto& i : list)
      {
         wkf::Platform* platPtr = dynamic_cast<wkf::Platform*>(i);
         if (platPtr != nullptr)
         {
            if (IsPlatformSelected(platPtr))
            {
               if (IsPlatformVisible(platPtr))
               {
                  platformList.push_back(platPtr);
               }
               else
               {
                  platPtr->ClearSelection();
               }
            }
         }
      }
      SetPlatformsSelected(platformList);
   }
}

// Select all platforms in aPlatforms.
// Anything that is currently selected that is not contained in aPlatforms will become unselected
void wkf::Environment::SetPlatformsSelected(const PlatformList& aPlatforms)
{
   PlatformList selectedPlatforms;
   PlatformList unselectedPlatforms;
   Platform*    previousPlatformOfInterest = GetPlatformOfInterest();

   // un-select platforms that are currently selected, but not in aPlatforms (desired selection)
   auto it = mSelectedPlatforms.begin();
   while (it != mSelectedPlatforms.end())
   {
      if (std::find(aPlatforms.begin(), aPlatforms.end(), *it) == aPlatforms.end())
      {
         unselectedPlatforms.push_back(*it);
         it = mSelectedPlatforms.erase(it);
      }
      else
      {
         ++it;
      }
   }

   // select platforms that are not currently selected, but are in aPlatforms (desired selection)
   for (const auto& p : aPlatforms)
   {
      if (!IsPlatformSelected(p))
      {
         mSelectedPlatforms.push_back(p);
         selectedPlatforms.push_back(p);
      }
   }

   if (!selectedPlatforms.empty() || !unselectedPlatforms.empty())
   {
      emit PlatformSelectionChanged(selectedPlatforms, unselectedPlatforms);

      Platform* currentPlatformOfInterest = GetPlatformOfInterest();
      if (previousPlatformOfInterest != currentPlatformOfInterest)
      {
         emit PlatformOfInterestChanged(currentPlatformOfInterest);
      }
   }
}

bool wkf::Environment::IsPlatformSelected(const Platform* aPlatform) const
{
   return std::find(mSelectedPlatforms.begin(), mSelectedPlatforms.end(), aPlatform) != mSelectedPlatforms.end();
}

bool wkf::Environment::IsPlatformSelected(const std::string& aPlatformName) const
{
   auto nameCompare = [&aPlatformName](Platform* a) { return (a->GetName() == aPlatformName); };
   return std::find_if(mSelectedPlatforms.begin(), mSelectedPlatforms.end(), nameCompare) != mSelectedPlatforms.end();
}

void wkf::Environment::RegisterSidePerspectiveCallback(DcsRegisterCallback callback)
{
    if (callback)
    {
        mSidePerspectiveCallback.emplace_back(callback);
    }
}

void wkf::Environment::RunSidePerspectiveCallback(const std::string& obs_data)
{
    for (const DcsRegisterCallback& callback : mSidePerspectiveCallback)
    {
        if (callback)
        {
            callback(obs_data);
        }
    }
    
}

void wkf::Environment::RegisterTaskBrowerCallback(DcsRegisterCallback callback)
{
    if (callback)
    {
        mTaskBrowerCallback.emplace_back(callback);
    }
}

void wkf::Environment::RunTaskBrowerCallback(const std::string& obs_data)
{
    for (const DcsRegisterCallback& callback : mTaskBrowerCallback)
    {
        if (callback)
        {
            callback(obs_data);
        }
    }

}

void wkf::Environment::SendGetAllTaskInfo(const std::string& info)
{
    emit GetAllTaskInfoString(info);
}

void wkf::Environment::RegisterPrintScreenCallback(DcsRegisterCallback callback)
{
    if (callback)
    {
        mPrintScreenCallback.emplace_back(callback);
    }
}

void wkf::Environment::RunPrintScreenCallback(const std::string& obs_data)
{
    for (const DcsRegisterCallback& callback : mPrintScreenCallback)
    {
        if (callback)
        {
            callback(obs_data);
        }
    }

}

void wkf::Environment::RegisterDamageStatisticsCallback(DcsRegisterCallback callback)
{
    if (callback)
    {
        mDamageStatisticsCallback.emplace_back(callback);
    }
}
void wkf::Environment::RunDamageStatisticsCallback(const std::string& data)
{
    for (const DcsRegisterCallback& callback : mDamageStatisticsCallback)
    {
        if (callback)
        {
            callback(data);
        }
    }
}

size_t wkf::Environment::RegisterFormationCallback(DcsRegisterCallback callback)
{
    if (callback)
    {
        size_t id = m_FormationCallbackNum++;
        mFormationCallback.emplace(id, callback);
        return id;
    }
    return 0;
}

void wkf::Environment::UnregisterFormationCallback(size_t aId)
{
    if (!aId)
        return;
    mFormationCallback.erase(aId);
}

void wkf::Environment::RunFormationCallback(const std::string& data)
{
    for (const auto& callback : mFormationCallback)
    {
        if (callback.second)
        {
            callback.second(data);
        }
    }
}

void wkf::Environment::SetObsInfo(const std::string& obs)
{
    RunSidePerspectiveCallback(obs);
}

void wkf::Environment::SetDamageStatistics(const std::string& data)
{
    RunDamageStatisticsCallback(data);
}

double wkf::Environment::GetDcsSimulation()
{
    std::lock_guard<std::mutex> lock(m_ObsMtx);
    return m_DcsSimuationTime;
}

void wkf::Environment::SetDcsSimulation(double a_DcsSimuationTime)
{
    std::lock_guard<std::mutex> lock(m_ObsMtx);
    m_DcsSimuationTime = a_DcsSimuationTime;
}


std::vector<wkf::WeaponQuantityRemaining> wkf::Environment::GetWeaponQuantityRemaining(std::string weapon_name)
{
    std::lock_guard<std::mutex> lock(m_WeaponQuantityMtx);
    auto it = m_PlatformWeaponMap.find(weapon_name);
    if (it != m_PlatformWeaponMap.end())
    {
        return it->second;
    }
    return std::vector<wkf::WeaponQuantityRemaining>();
}

void wkf::Environment::UpdateWeaponQuantityRemaining(const std::string& obs_data)
{
    std::map<std::string, std::vector<WeaponQuantityRemaining>> a_PlatformWeaponMap;
    try
    {
        auto AnalysisJson = [this, &a_PlatformWeaponMap](QJsonObject side_json)
        {
            QJsonArray unit_array = side_json["units"].toArray();
            for (const QJsonValue& value : unit_array)
            {
                if (!value.isObject())
                    continue;
                QJsonObject unit_obj = value.toObject();
                if (unit_obj.contains("weapons"))
                {
                    std::vector<WeaponQuantityRemaining> _weaponVector;
                    QJsonArray weapon_array = unit_obj["weapons"].toArray();
                    for (const QJsonValue& weapon_value : weapon_array)
                    {
                        if (!weapon_value.isObject())
                            continue;
                        QJsonObject weapon_obj = weapon_value.toObject();
                        if (weapon_obj["Type"] != "WSF_RF_JAMMER")
                        {
                            WeaponQuantityRemaining _weapon;
                            _weapon.weapon_name = weapon_obj["Name"].toString().toStdString();
                            _weapon.quantity = weapon_obj["QuatR"].toInt();
                            _weaponVector.push_back(_weapon);
                        }
                    }
                    a_PlatformWeaponMap[unit_obj["name"].toString().toStdString()] = _weaponVector;
                }
            }
        };

        QJsonDocument jsonDoc = QJsonDocument::fromJson(obs_data.c_str());
        if (jsonDoc.isNull() || !jsonDoc.isObject())
        {
            return;
        }
        QJsonObject obs_json = jsonDoc.object();

        if (obs_json.contains("red"))
            AnalysisJson(obs_json["red"].toObject());
        if (obs_json.contains("blue"))
            AnalysisJson(obs_json["blue"].toObject());

        std::lock_guard<std::mutex> lock(m_WeaponQuantityMtx);
        m_PlatformWeaponMap = a_PlatformWeaponMap;
    }
    catch (const std::exception&)
    {

    }
}

std::string wkf::Environment::GetCapturePath() const
{
    return mCapturePath;
}

void wkf::Environment::SendCommandTask(const std::string& aMessage, std::string type)
{
    if (mDcsThread)
        mDcsThread->SendCommandTask(aMessage,type);
}

void wkf::Environment::InitDcsThread()
{
    QFile file("warlock_config.txt");
    DcsInfo::DcsNetworkInfo dcsInfo;
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        dcsInfo.mDcsAddress = "error";
        dcsInfo.mDcsSendPort = 0;
    }
    else
    {
        QTextStream in(&file);
        QString fileContent = in.readAll();
        file.close();
        QJsonDocument doc = QJsonDocument::fromJson(fileContent.toUtf8());
        if (!doc.isNull() && doc.isObject())
        {
            QJsonObject obj = doc.object();

            if (!obj.contains("esim_command_address") || !obj.contains("esim_command_port"))
            {
                dcsInfo.mDcsAddress = "error";
                dcsInfo.mDcsSendPort = 0;
            }
            else
            {
                dcsInfo.mDcsAddress = obj["esim_command_address"].toString().toStdString();
                dcsInfo.mDcsSendPort = obj["esim_command_port"].toInt();
            }



            if (obj.contains("capture_path"))
            {
                std::string _path = obj["capture_path"].toString().toStdString();
                mCapturePath = _path;
            }
        }  
    }
    mIsDcsSimualtion = true;
    mDcsThread = new DcsInfo::DcsManagerThread(this, dcsInfo);
    mObsTimerPtr->start(2000);

    // lifengyu=======
    if (mMainWindowPtr)
    {
        mMainWindowPtr->UpdateNetworkInfo(
            QString::fromStdString(dcsInfo.mDcsAddress), 
            dcsInfo.mDcsSendPort
        );
    }
}

void wkf::Environment::Capture(const std::string& name)
{
    RunPrintScreenCallback(mCapturePath);
}

// this will look a provider for the requested resource associated with the platform
bool wkf::Environment::RequestPlatformResourceInstance(const QString& aResourceName, const QString& aPlatformName)
{
   auto plugins = mPluginManagerPtr->GetLoadedPlugins();
   for (auto& p : plugins)
   {
      if (p.second->RequestPlatformResourceInstance(aResourceName, aPlatformName))
      {
         return true;
      }
   }
   return false;
}

QString wkf::getOpenFileName(QWidget*             aParent /*= Q_NULLPTR*/,
                             const QString&       aCaption /*= QString()*/,
                             const QString&       aDir /*= QString()*/,
                             const QString&       aFilter /*= QString()*/,
                             QString*             aSelectedFilter /*= Q_NULLPTR*/,
                             QFileDialog::Options aOptions /*= QFileDialog::Options()*/)
{
   if (wkfEnv.IsFileBrowsingLocked())
   {
      QMessageBox::information(nullptr,
                               "File Browsing Disabled",
                               "File Browsing has been disabled.  To enable this please contact an administrator.");
      return QString();
   }
   else
   {
      return QFileDialog::getOpenFileName(aParent, aCaption, aDir, aFilter, aSelectedFilter, aOptions);
   }
}

QStringList wkf::getOpenFileNames(QWidget*             aParent /*= Q_NULLPTR*/,
                                  const QString&       aCaption /*= QString()*/,
                                  const QString&       aDir /*= QString()*/,
                                  const QString&       aFilter /*= QString()*/,
                                  QString*             aSelectedFilter /*= Q_NULLPTR*/,
                                  QFileDialog::Options aOptions /*= QFileDialog::Options()*/)
{
   if (wkfEnv.IsFileBrowsingLocked())
   {
      QMessageBox::information(nullptr,
                               "File Browsing Disabled",
                               "File Browsing has been disabled.  To enable this please contact an administrator.");
      return QStringList();
   }
   else
   {
      return QFileDialog::getOpenFileNames(aParent, aCaption, aDir, aFilter, aSelectedFilter, aOptions);
   }
}

QString wkf::getSaveFileName(QWidget*             aParent /*= Q_NULLPTR*/,
                             const QString&       aCaption /*= QString()*/,
                             const QString&       aDir /*= QString()*/,
                             const QString&       aFilter /*= QString()*/,
                             QString*             aSelectedFilter /*= Q_NULLPTR*/,
                             QFileDialog::Options aOptions /*= QFileDialog::Options()*/)
{
   if (wkfEnv.IsFileBrowsingLocked())
   {
      QMessageBox::information(nullptr,
                               "File Browsing Disabled",
                               "File Browsing has been disabled.  To enable this please contact an administrator.");
      return QString();
   }
   else
   {
      return QFileDialog::getSaveFileName(aParent, aCaption, aDir, aFilter, aSelectedFilter, aOptions);
   }
}

WKF_EXPORT QString wkf::getExistingDirectory(QWidget*             aParent /*= Q_NULLPTR*/,
                                             const QString&       aCaption /*= QString()*/,
                                             const QString&       aDir /*= QString()*/,
                                             QFileDialog::Options aOptions /*= QFileDialog::ShowDirsOnly*/)
{
   if (wkfEnv.IsFileBrowsingLocked())
   {
      QMessageBox::information(nullptr,
                               "File Browsing Disabled",
                               "File Browsing has been disabled.  To enable this please contact an administrator.");
      return QString();
   }
   else
   {
      return QFileDialog::getExistingDirectory(aParent, aCaption, aDir, aOptions);
   }
}

DcsInfo::DcsManagerThread::DcsManagerThread(wkf::Environment* sim, DcsNetworkInfo info)
    :mSimulation(sim)
{
    m_DcsHttpClient = new QNetworkAccessManager();
    mServerAddress = info.mDcsAddress;
    mSendPort = info.mDcsSendPort;
}

void DcsInfo::DcsManagerThread::SendCommandTask(const std::string& aMessage, std::string type)
{
    if (mServerAddress == "error")
    {
        return;
    }

    QJsonObject outerJson;
    if (type == "set_action")
    {
        outerJson["type"] = "request";
        QJsonObject data_object;
        data_object["key"] = "set_action";
        data_object["value"] = QString::fromStdString(aMessage);
        QString data_objcet = QJsonDocument(data_object).toJson(QJsonDocument::Compact);
        outerJson["data"] = data_objcet;

    }
    else if (type == "meta_task")
    {
        outerJson["type"] = "meta_task";
        outerJson["data"] = QString::fromStdString(aMessage);
    }
    QJsonObject ans_object;
    QString ans_value = QJsonDocument(outerJson).toJson(QJsonDocument::Compact);
    ans_object["content"] = ans_value;
    QByteArray jsonData = QJsonDocument(ans_object).toJson();
    QUrl url;
    url.setScheme("http");
    url.setHost(QString::fromStdString(mServerAddress));
    url.setPort(mSendPort);
    url.setPath(QString("/api/UITaskExec"));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    QNetworkReply* reply = m_DcsHttpClient->post(request, jsonData);
    
}

void DcsInfo::DcsManagerThread::GetSimInfo(QString type)
{
    if (mServerAddress == "error")
    {
        return;
    }
        
    QUrl url;
    url.setScheme("http");  
    url.setHost(QString::fromStdString(mServerAddress));
    url.setPort(mSendPort);
    url.setPath(QString("/api/%1").arg(type));  
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    QNetworkReply* reply = m_DcsHttpClient->get(request);

    connect(reply, &QNetworkReply::finished, this, [this, reply, type]() {
        handleResponse(reply, type);
    });
}

void DcsInfo::DcsManagerThread::handleResponse(QNetworkReply* reply, QString handleType)
{
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray responseData = reply->readAll();
        if (handleType == "obs")
        {
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isNull() || !jsonDoc.isObject())
            {
                return;
            }
            QJsonObject obs_json = jsonDoc.object();
            if (obs_json.contains("message"))
            {
                QString data = obs_json["message"].toString();
                mSimulation->UpdateWeaponQuantityRemaining(data.toStdString());
                QJsonDocument _doc = QJsonDocument::fromJson(data.toUtf8());
                if (!jsonDoc.isNull() || jsonDoc.isObject())
                {
                    mSimulation->SetDcsSimulation(_doc["sim_time"].toDouble());
                }
                mSimulation->SetObsInfo(data.toStdString());
            }
        }
        else if (handleType == "statistics")
        {
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isNull() || !jsonDoc.isObject())
            {
                return;
            }
            QJsonObject obs_json = jsonDoc.object();
            if (obs_json.contains("message"))
            {
                QString data = obs_json["message"].toString();
                mSimulation->SetDamageStatistics(data.toStdString());
            }
        }
        else if (handleType == "GetFormationInfo")
        {
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isNull() || !jsonDoc.isObject())
            {
                return;
            }
            QJsonObject obs_json = jsonDoc.object();
            if (obs_json.contains("message"))
            {
                QString data = obs_json["message"].toString();
                mSimulation->RunFormationCallback(data.toStdString());
            }
        }
        else if (handleType == "print_screen")
        {
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isNull() || !jsonDoc.isObject())
            {
                return;
            }
            QJsonObject obs_json = jsonDoc.object();
            if (obs_json.contains("message"))
            {
                if (obs_json["message"].toBool())
                {
                    mSimulation->Capture("");
                }
            }
        }
        else if (handleType == "GetMetaTaskInfo")
        {
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isNull() || !jsonDoc.isObject())
            {
                return;
            }
            QJsonObject obs_json = jsonDoc.object();
            if (obs_json.contains("message"))
            {
                if (obs_json["message"].isObject())
                {
                    QJsonObject data = obs_json["message"].toObject();
                    QJsonDocument ans_doc(data);
                    mSimulation->RunTaskBrowerCallback(ans_doc.toJson(QJsonDocument::Compact).toStdString());
                }
                else if (obs_json["message"].isString())
                {
                    QString data = obs_json["message"].toString();
                    mSimulation->RunTaskBrowerCallback(data.toStdString());
                }
                
            }
        }
        else if (handleType == "GetAllTaskInfo")
        {
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isNull() || !jsonDoc.isObject())
            {
                return;
            }
            QJsonObject obs_json = jsonDoc.object();
            if (obs_json.contains("message"))
            {
                if (obs_json["message"].isArray())
                {
                    QJsonArray data = obs_json["message"].toArray();
                    QJsonDocument ans_doc(data);
                    mSimulation->SendGetAllTaskInfo(ans_doc.toJson(QJsonDocument::Compact).toStdString());
                }
            }
        }
    }
    reply->deleteLater();
}
