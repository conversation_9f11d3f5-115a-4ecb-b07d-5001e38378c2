// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TaskAllocationTriggerApprovalCaseType_h
#define Uci__Type__TaskAllocationTriggerApprovalCaseType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__MissionPlanningTriggerType_h)
# include "uci/type/MissionPlanningTriggerType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__TaskAllocationApprovalPolicyType_h)
# include "uci/type/TaskAllocationApprovalPolicyType.h"
#endif

#if !defined(Uci__Type__TaskAllocationResultCaseType_h)
# include "uci/type/TaskAllocationResultCaseType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TaskAllocationTriggerApprovalCaseType sequence accessor class */
      class TaskAllocationTriggerApprovalCaseType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TaskAllocationTriggerApprovalCaseType()
         { }

         /** Returns this accessor's type constant, i.e. TaskAllocationTriggerApprovalCaseType
           *
           * @return This accessor's type constant, i.e. TaskAllocationTriggerApprovalCaseType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::taskAllocationTriggerApprovalCaseType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TaskAllocationTriggerApprovalCaseType& accessor)
            throw(uci::base::UCIException) = 0;


         /** This element specifies the default approval policy for the sibling Trigger. The default approval policy can be
           * selectively overridden based on the result of the triggered task allocation using the sibling
           * TriggerApprovalPolicyByResultCase element. [Minimum occurrences: 0] [Maximum occurrences: 3]
           */
         typedef uci::base::BoundedList<uci::type::TaskAllocationApprovalPolicyType, uci::type::accessorType::taskAllocationApprovalPolicyType> DefaultPolicy;

         /** This element allows the sibling Default of the sibling Trigger to be overridden based on the result of a triggered
           * task allocation. [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::TaskAllocationResultCaseType, uci::type::accessorType::taskAllocationResultCaseType> ByResultCase;

         /** Returns the accessor that provides access to the complex content that is identified by the Trigger.
           *
           * @return The acecssor that provides access to the complex content that is identified by Trigger.
           */
         virtual const uci::type::MissionPlanningTriggerType& getTrigger() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Trigger.
           *
           * @return The acecssor that provides access to the complex content that is identified by Trigger.
           */
         virtual uci::type::MissionPlanningTriggerType& getTrigger()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Trigger to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Trigger
           */
         virtual void setTrigger(const uci::type::MissionPlanningTriggerType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the DefaultPolicy.
           *
           * @return The bounded list identified by DefaultPolicy.
           */
         virtual const uci::type::TaskAllocationTriggerApprovalCaseType::DefaultPolicy& getDefaultPolicy() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the DefaultPolicy.
           *
           * @return The bounded list identified by DefaultPolicy.
           */
         virtual uci::type::TaskAllocationTriggerApprovalCaseType::DefaultPolicy& getDefaultPolicy()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the DefaultPolicy.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setDefaultPolicy(const uci::type::TaskAllocationTriggerApprovalCaseType::DefaultPolicy& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ByResultCase.
           *
           * @return The bounded list identified by ByResultCase.
           */
         virtual const uci::type::TaskAllocationTriggerApprovalCaseType::ByResultCase& getByResultCase() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ByResultCase.
           *
           * @return The bounded list identified by ByResultCase.
           */
         virtual uci::type::TaskAllocationTriggerApprovalCaseType::ByResultCase& getByResultCase()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the ByResultCase.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setByResultCase(const uci::type::TaskAllocationTriggerApprovalCaseType::ByResultCase& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TaskAllocationTriggerApprovalCaseType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TaskAllocationTriggerApprovalCaseType to copy from
           */
         TaskAllocationTriggerApprovalCaseType(const TaskAllocationTriggerApprovalCaseType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TaskAllocationTriggerApprovalCaseType to the contents of the
           * TaskAllocationTriggerApprovalCaseType on the right hand side (rhs) of the assignment
           * operator.TaskAllocationTriggerApprovalCaseType [only available to derived classes]
           *
           * @param rhs The TaskAllocationTriggerApprovalCaseType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::TaskAllocationTriggerApprovalCaseType
           * @return A constant reference to this TaskAllocationTriggerApprovalCaseType.
           */
         const TaskAllocationTriggerApprovalCaseType& operator=(const TaskAllocationTriggerApprovalCaseType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TaskAllocationTriggerApprovalCaseType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TaskAllocationTriggerApprovalCaseType_h

