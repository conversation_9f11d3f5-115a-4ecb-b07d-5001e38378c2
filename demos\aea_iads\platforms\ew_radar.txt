# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
# * * ************************************** * *
# *   ****** Demonstration input file ******   *
# *   ******      UNCLASSIFIED        ******   *
# * * ************************************** * *

include_once platforms/red_radar.txt
include_once sensors/radar/ew_radar.txt
include_once processors/ep_operations.txt

platform_type EW_RADAR RED_RADAR
  icon Ground_Radar
  category 2D_RADAR

  sensor ew_radar EW_RADAR
     on
     internal_link data_mgr
     ignore_same_side
  end_sensor

  processor ep_operations EP_OPERATIONS
     script_variables
        mSensorNameList.PushBack("ew_radar");
     end_script_variables
  end_processor

  aux_data
     double max_range = 185200 # meters = 100 nm
  end_aux_data
end_platform_type
