.. ****************************************************************************
.. CUI
..
.. The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
..
.. The use, dissemination or disclosure of data in this file is subject to
.. limitation or restriction. See accompanying README and LICENSE for details.
.. ****************************************************************************

.. demo:: aea_iads

.. |classification| replace:: Unclassified
.. |date|           replace:: 2018-03-22
.. |group|          replace:: Demos
.. |image|          replace:: images/aea_iads_demo.png
.. |tags|           replace:: n/a
.. |title|          replace:: aea_iads demo
.. |startup|        replace:: strike.txt
.. |summary|        replace:: This directory contains the necessary files to test the EW effects in a scenario similar to the iads_demo baseline scenario.

.. |i| image:: images/aea_iads_demo.png
   :height: 150 px
   :width:  150 px

.. include:: demo_template.txt

| This directory contains the necessary files to test the EW effects
| in a scenario similar to the iads_demo baseline scenario.
|
| The SOJ and EW_Radar have EA and EP techniques, respectively, with them
| to aid in the testing and verification of these techniques.
|
| The strike.txt file is the normal run input file to run the simulation
| as a single application.
|
| To execute, open "strike.txt" in Wizard and execute using the mission
| application. The replay file will written to the output directory for 
| opening by Mystic.
|
| Also included are two realtime run files strike-rt.txt and iads-rt.txt
| that can be run separately to test the jamming and EW effects over DIS.
| To run both concurrently, open two Wizard applications one with 
| "iads-rt.txt" and one with "strike-rt.txt" and execute using the mission
| application. Each WSF application communicate over DIS. The applications
| can be run on separate hosts or on the same host. The file dis_data.txt
| provides the DIS Enumeration mapping for the entity types, emitter types,
| and EW technique types.
|
| To visually monitor the progress of the realtime sims, Warlock can be opened
| and then setup to monitor multicast address ***********, port 3225 via input
 file containing a dis_interface block.
