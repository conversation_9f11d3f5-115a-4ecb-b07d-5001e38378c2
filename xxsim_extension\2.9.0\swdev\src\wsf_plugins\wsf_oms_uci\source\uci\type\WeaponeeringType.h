// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WeaponeeringType_h
#define Uci__Type__WeaponeeringType_h 1

#if !defined(Uci__Type__StoreType_h)
# include "uci/type/StoreType.h"
#endif

#if !defined(Uci__Base__UnsignedIntAccessor_h)
# include "uci/base/UnsignedIntAccessor.h"
#endif

#if !defined(Uci__Type__ImpactConditionsType_h)
# include "uci/type/ImpactConditionsType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WeaponeeringType sequence accessor class */
      class WeaponeeringType : public virtual uci::type::StoreType {
      public:

         /** The destructor */
         virtual ~WeaponeeringType()
         { }

         /** Returns this accessor's type constant, i.e. WeaponeeringType
           *
           * @return This accessor's type constant, i.e. WeaponeeringType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::weaponeeringType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WeaponeeringType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the StoreQuantity.
           *
           * @return The value of the simple primitive data type identified by StoreQuantity.
           */
         virtual xs::UnsignedInt getStoreQuantity() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the StoreQuantity.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setStoreQuantity(xs::UnsignedInt value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the ImpactConditions.
           *
           * @return The acecssor that provides access to the complex content that is identified by ImpactConditions.
           */
         virtual const uci::type::ImpactConditionsType& getImpactConditions() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the ImpactConditions.
           *
           * @return The acecssor that provides access to the complex content that is identified by ImpactConditions.
           */
         virtual uci::type::ImpactConditionsType& getImpactConditions()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the ImpactConditions to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by ImpactConditions
           */
         virtual void setImpactConditions(const uci::type::ImpactConditionsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by ImpactConditions exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by ImpactConditions is emabled or not
           */
         virtual bool hasImpactConditions() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by ImpactConditions
           *
           * @param type = uci::type::accessorType::impactConditionsType This Accessor's accessor type
           */
         virtual void enableImpactConditions(uci::base::accessorType::AccessorType type = uci::type::accessorType::impactConditionsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by ImpactConditions */
         virtual void clearImpactConditions()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WeaponeeringType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WeaponeeringType to copy from
           */
         WeaponeeringType(const WeaponeeringType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WeaponeeringType to the contents of the WeaponeeringType on the
           * right hand side (rhs) of the assignment operator.WeaponeeringType [only available to derived classes]
           *
           * @param rhs The WeaponeeringType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::WeaponeeringType
           * @return A constant reference to this WeaponeeringType.
           */
         const WeaponeeringType& operator=(const WeaponeeringType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WeaponeeringType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WeaponeeringType_h

