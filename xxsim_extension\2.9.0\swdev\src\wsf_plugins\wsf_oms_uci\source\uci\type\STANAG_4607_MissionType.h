// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__STANAG_4607_MissionType_h
#define Uci__Type__STANAG_4607_MissionType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__STANAG_4607_NationalityType_h)
# include "uci/type/STANAG_4607_NationalityType.h"
#endif

#if !defined(Uci__Type__STANAG_4607_PlatformIdentifierType_h)
# include "uci/type/STANAG_4607_PlatformIdentifierType.h"
#endif

#if !defined(Uci__Base__IntAccessor_h)
# include "uci/base/IntAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This element represents a filter criteria using STANAG 4607 Mission characteristics. */
      class STANAG_4607_MissionType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~STANAG_4607_MissionType()
         { }

         /** Returns this accessor's type constant, i.e. STANAG_4607_MissionType
           *
           * @return This accessor's type constant, i.e. STANAG_4607_MissionType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::sTANAG_4607_MissionType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const STANAG_4607_MissionType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Nationality.
           *
           * @return The value of the string data type identified by Nationality.
           */
         virtual const uci::type::STANAG_4607_NationalityType& getNationality() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Nationality.
           *
           * @return The value of the string data type identified by Nationality.
           */
         virtual uci::type::STANAG_4607_NationalityType& getNationality()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Nationality to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setNationality(const uci::type::STANAG_4607_NationalityType& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Nationality to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setNationality(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Nationality to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setNationality(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the PlatformID.
           *
           * @return The value of the string data type identified by PlatformID.
           */
         virtual const uci::type::STANAG_4607_PlatformIdentifierType& getPlatformID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the PlatformID.
           *
           * @return The value of the string data type identified by PlatformID.
           */
         virtual uci::type::STANAG_4607_PlatformIdentifierType& getPlatformID()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the PlatformID to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setPlatformID(const uci::type::STANAG_4607_PlatformIdentifierType& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the PlatformID to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setPlatformID(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the PlatformID to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setPlatformID(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the MissionID.
           *
           * @return The value of the simple primitive data type identified by MissionID.
           */
         virtual xs::Int getMissionID() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the MissionID.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setMissionID(xs::Int value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         STANAG_4607_MissionType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The STANAG_4607_MissionType to copy from
           */
         STANAG_4607_MissionType(const STANAG_4607_MissionType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this STANAG_4607_MissionType to the contents of the
           * STANAG_4607_MissionType on the right hand side (rhs) of the assignment operator.STANAG_4607_MissionType [only
           * available to derived classes]
           *
           * @param rhs The STANAG_4607_MissionType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::STANAG_4607_MissionType
           * @return A constant reference to this STANAG_4607_MissionType.
           */
         const STANAG_4607_MissionType& operator=(const STANAG_4607_MissionType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // STANAG_4607_MissionType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__STANAG_4607_MissionType_h

