
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>cmake_policy &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="configure_file" href="configure_file.html" />
    <link rel="prev" title="cmake_path" href="cmake_path.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="configure_file.html" title="configure_file"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake_path.html" title="cmake_path"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_policy</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cmake-policy">
<span id="command:cmake_policy"></span><h1>cmake_policy<a class="headerlink" href="#cmake-policy" title="Permalink to this heading">¶</a></h1>
<p>Manage CMake Policy settings.  See the <span class="target" id="index-0-manual:cmake-policies(7)"></span><a class="reference internal" href="../manual/cmake-policies.7.html#manual:cmake-policies(7)" title="cmake-policies(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-policies(7)</span></code></a>
manual for defined policies.</p>
<p>As CMake evolves it is sometimes necessary to change existing behavior
in order to fix bugs or improve implementations of existing features.
The CMake Policy mechanism is designed to help keep existing projects
building as new versions of CMake introduce changes in behavior.  Each
new policy (behavioral change) is given an identifier of the form
<code class="docutils literal notranslate"><span class="pre">CMP&lt;NNNN&gt;</span></code> where <code class="docutils literal notranslate"><span class="pre">&lt;NNNN&gt;</span></code> is an integer index.  Documentation
associated with each policy describes the <code class="docutils literal notranslate"><span class="pre">OLD</span></code> and <code class="docutils literal notranslate"><span class="pre">NEW</span></code> behavior
and the reason the policy was introduced.  Projects may set each policy
to select the desired behavior.  When CMake needs to know which behavior
to use it checks for a setting specified by the project.  If no
setting is available the <code class="docutils literal notranslate"><span class="pre">OLD</span></code> behavior is assumed and a warning is
produced requesting that the policy be set.</p>
<section id="setting-policies-by-cmake-version">
<h2>Setting Policies by CMake Version<a class="headerlink" href="#setting-policies-by-cmake-version" title="Permalink to this heading">¶</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">cmake_policy</span></code> command is used to set policies to <code class="docutils literal notranslate"><span class="pre">OLD</span></code> or <code class="docutils literal notranslate"><span class="pre">NEW</span></code>
behavior.  While setting policies individually is supported, we
encourage projects to set policies based on CMake versions:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_policy(</span><span class="no">VERSION</span><span class="w"> </span><span class="nv">&lt;min&gt;</span><span class="p">[...</span><span class="nv">&lt;max&gt;</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12: </span>The optional <code class="docutils literal notranslate"><span class="pre">&lt;max&gt;</span></code> version.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">&lt;min&gt;</span></code> and the optional <code class="docutils literal notranslate"><span class="pre">&lt;max&gt;</span></code> are each CMake versions of the form
<code class="docutils literal notranslate"><span class="pre">major.minor[.patch[.tweak]]</span></code>, and the <code class="docutils literal notranslate"><span class="pre">...</span></code> is literal.  The <code class="docutils literal notranslate"><span class="pre">&lt;min&gt;</span></code>
version must be at least <code class="docutils literal notranslate"><span class="pre">2.4</span></code> and at most the running version of CMake.
The <code class="docutils literal notranslate"><span class="pre">&lt;max&gt;</span></code> version, if specified, must be at least the <code class="docutils literal notranslate"><span class="pre">&lt;min&gt;</span></code> version
but may exceed the running version of CMake.  If the running version of
CMake is older than 3.12, the extra <code class="docutils literal notranslate"><span class="pre">...</span></code> dots will be seen as version
component separators, resulting in the <code class="docutils literal notranslate"><span class="pre">...&lt;max&gt;</span></code> part being ignored and
preserving the pre-3.12 behavior of basing policies on <code class="docutils literal notranslate"><span class="pre">&lt;min&gt;</span></code>.</p>
<p>This specifies that the current CMake code is written for the given
range of CMake versions.  All policies known to the running version of CMake
and introduced in the <code class="docutils literal notranslate"><span class="pre">&lt;min&gt;</span></code> (or <code class="docutils literal notranslate"><span class="pre">&lt;max&gt;</span></code>, if specified) version
or earlier will be set to use <code class="docutils literal notranslate"><span class="pre">NEW</span></code> behavior.  All policies
introduced in later versions will be unset (unless the
<span class="target" id="index-0-variable:CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;"></span><a class="reference internal" href="../variable/CMAKE_POLICY_DEFAULT_CMPNNNN.html#variable:CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;" title="CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;</span></code></a> variable sets a default).
This effectively requests behavior preferred as of a given CMake
version and tells newer CMake versions to warn about their new policies.</p>
<p>Note that the <span class="target" id="index-0-command:cmake_minimum_required"></span><a class="reference internal" href="cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required(VERSION)</span></code></a>
command implicitly calls <code class="docutils literal notranslate"><span class="pre">cmake_policy(VERSION)</span></code> too.</p>
</section>
<section id="setting-policies-explicitly">
<h2>Setting Policies Explicitly<a class="headerlink" href="#setting-policies-explicitly" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_policy(</span><span class="no">SET</span><span class="w"> </span><span class="no">CMP</span><span class="nv">&lt;NNNN&gt;</span><span class="w"> </span><span class="no">NEW</span><span class="nf">)</span><span class="w"></span>
<span class="nf">cmake_policy(</span><span class="no">SET</span><span class="w"> </span><span class="no">CMP</span><span class="nv">&lt;NNNN&gt;</span><span class="w"> </span><span class="no">OLD</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Tell CMake to use the <code class="docutils literal notranslate"><span class="pre">OLD</span></code> or <code class="docutils literal notranslate"><span class="pre">NEW</span></code> behavior for a given policy.
Projects depending on the old behavior of a given policy may silence a
policy warning by setting the policy state to <code class="docutils literal notranslate"><span class="pre">OLD</span></code>.  Alternatively
one may fix the project to work with the new behavior and set the
policy state to <code class="docutils literal notranslate"><span class="pre">NEW</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">OLD</span></code> behavior of a policy is
<span class="target" id="index-1-manual:cmake-policies(7)"></span><a class="reference internal" href="../manual/cmake-policies.7.html#manual:cmake-policies(7)" title="cmake-policies(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">deprecated</span> <span class="pre">by</span> <span class="pre">definition</span></code></a>
and may be removed in a future version of CMake.</p>
</div>
</section>
<section id="checking-policy-settings">
<h2>Checking Policy Settings<a class="headerlink" href="#checking-policy-settings" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_policy(</span><span class="no">GET</span><span class="w"> </span><span class="no">CMP</span><span class="nv">&lt;NNNN&gt;</span><span class="w"> </span><span class="nv">&lt;variable&gt;</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Check whether a given policy is set to <code class="docutils literal notranslate"><span class="pre">OLD</span></code> or <code class="docutils literal notranslate"><span class="pre">NEW</span></code> behavior.
The output <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code> value will be <code class="docutils literal notranslate"><span class="pre">OLD</span></code> or <code class="docutils literal notranslate"><span class="pre">NEW</span></code> if the
policy is set, and empty otherwise.</p>
</section>
<section id="cmake-policy-stack">
<h2>CMake Policy Stack<a class="headerlink" href="#cmake-policy-stack" title="Permalink to this heading">¶</a></h2>
<p>CMake keeps policy settings on a stack, so changes made by the
<code class="docutils literal notranslate"><span class="pre">cmake_policy</span></code> command affect only the top of the stack.  A new entry on
the policy stack is managed automatically for each subdirectory to
protect its parents and siblings.  CMake also manages a new entry for
scripts loaded by <span class="target" id="index-0-command:include"></span><a class="reference internal" href="include.html#command:include" title="include"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">include()</span></code></a> and <span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> commands
except when invoked with the <code class="docutils literal notranslate"><span class="pre">NO_POLICY_SCOPE</span></code> option
(see also policy <span class="target" id="index-0-policy:CMP0011"></span><a class="reference internal" href="../policy/CMP0011.html#policy:CMP0011" title="CMP0011"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0011</span></code></a>).
The <code class="docutils literal notranslate"><span class="pre">cmake_policy</span></code> command provides an interface to manage custom
entries on the policy stack:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_policy(</span><span class="no">PUSH</span><span class="nf">)</span><span class="w"></span>
<span class="nf">cmake_policy(</span><span class="no">POP</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Each <code class="docutils literal notranslate"><span class="pre">PUSH</span></code> must have a matching <code class="docutils literal notranslate"><span class="pre">POP</span></code> to erase any changes.
This is useful to make temporary changes to policy settings.
Calls to the <span class="target" id="index-1-command:cmake_minimum_required"></span><a class="reference internal" href="cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required(VERSION)</span></code></a>,
<code class="docutils literal notranslate"><span class="pre">cmake_policy(VERSION)</span></code>, or <code class="docutils literal notranslate"><span class="pre">cmake_policy(SET)</span></code> commands
influence only the current top of the policy stack.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.25: </span>The <span class="target" id="index-0-command:block"></span><a class="reference internal" href="block.html#command:block" title="block"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">block()</span></code></a> and <span class="target" id="index-0-command:endblock"></span><a class="reference internal" href="endblock.html#command:endblock" title="endblock"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">endblock()</span></code></a> commands offer a more flexible
and more secure way to manage the policy stack. The pop action is done
automatically when the <span class="target" id="index-1-command:endblock"></span><a class="reference internal" href="endblock.html#command:endblock" title="endblock"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">endblock()</span></code></a> command is executed, so it avoid
to call the <span class="target" id="index-0-command:cmake_policy"></span><a class="reference internal" href="#command:cmake_policy" title="cmake_policy"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_policy(POP)</span></code></a> command before each
<span class="target" id="index-0-command:return"></span><a class="reference internal" href="return.html#command:return" title="return"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">return()</span></code></a> command.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># stack management with cmake_policy()</span>
<span class="nf">function(</span><span class="nb">my_func</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">cmake_policy(</span><span class="no">PUSH</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">cmake_policy(</span><span class="no">SET</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">if</span> <span class="nf">(</span><span class="nv">&lt;cond1&gt;</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="p">...</span><span class="w"></span>
<span class="w">    </span><span class="nf">cmake_policy(</span><span class="no">POP</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">return()</span><span class="w"></span>
<span class="w">  </span><span class="nf">elseif(</span><span class="nv">&lt;cond2&gt;</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="p">...</span><span class="w"></span>
<span class="w">    </span><span class="nf">cmake_policy(</span><span class="no">POP</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">return()</span><span class="w"></span>
<span class="w">  </span><span class="nf">endif()</span><span class="w"></span>
<span class="w">  </span><span class="p">...</span><span class="w"></span>
<span class="w">  </span><span class="nf">cmake_policy(</span><span class="no">POP</span><span class="nf">)</span><span class="w"></span>
<span class="nf">endfunction()</span><span class="w"></span>

<span class="c"># stack management with block()/endblock()</span>
<span class="nf">function(</span><span class="nb">my_func</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">block(</span><span class="no">SCOPE_FOR</span><span class="w"> </span><span class="no">POLICIES</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">cmake_policy(</span><span class="no">SET</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">if</span> <span class="nf">(</span><span class="nv">&lt;cond1&gt;</span><span class="nf">)</span><span class="w"></span>
<span class="w">      </span><span class="p">...</span><span class="w"></span>
<span class="w">      </span><span class="nf">return()</span><span class="w"></span>
<span class="w">    </span><span class="nf">elseif(</span><span class="nv">&lt;cond2&gt;</span><span class="nf">)</span><span class="w"></span>
<span class="w">      </span><span class="p">...</span><span class="w"></span>
<span class="w">      </span><span class="nf">return()</span><span class="w"></span>
<span class="w">    </span><span class="nf">endif()</span><span class="w"></span>
<span class="w">    </span><span class="p">...</span><span class="w"></span>
<span class="w">  </span><span class="nf">endblock()</span><span class="w"></span>
<span class="nf">endfunction()</span><span class="w"></span>
</pre></div>
</div>
</div>
<p>Commands created by the <span class="target" id="index-0-command:function"></span><a class="reference internal" href="function.html#command:function" title="function"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">function()</span></code></a> and <span class="target" id="index-0-command:macro"></span><a class="reference internal" href="macro.html#command:macro" title="macro"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">macro()</span></code></a>
commands record policy settings when they are created and
use the pre-record policies when they are invoked.  If the function or
macro implementation sets policies, the changes automatically
propagate up through callers until they reach the closest nested
policy stack entry.</p>
</section>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-2-command:cmake_minimum_required"></span><a class="reference internal" href="cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake_policy</a><ul>
<li><a class="reference internal" href="#setting-policies-by-cmake-version">Setting Policies by CMake Version</a></li>
<li><a class="reference internal" href="#setting-policies-explicitly">Setting Policies Explicitly</a></li>
<li><a class="reference internal" href="#checking-policy-settings">Checking Policy Settings</a></li>
<li><a class="reference internal" href="#cmake-policy-stack">CMake Policy Stack</a></li>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake_path.html"
                          title="previous chapter">cmake_path</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="configure_file.html"
                          title="next chapter">configure_file</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/cmake_policy.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="configure_file.html" title="configure_file"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake_path.html" title="cmake_path"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_policy</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>