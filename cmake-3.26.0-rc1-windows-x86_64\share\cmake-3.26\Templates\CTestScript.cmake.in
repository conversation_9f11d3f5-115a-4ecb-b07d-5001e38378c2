cmake_minimum_required(VERSION 2.4)

# This is a template for the CTest script for this system

set(CTEST_SITE                          "@SITE@")
set(CTEST_BUILD_NAME                    "@BUILDNAME@")

# ---
set(CTEST_SOURCE_DIRECTORY              "@CMAKE_SOURCE_DIR@")
set(CTEST_BINARY_DIRECTORY              "@CMAKE_BINARY_DIR@")
set(CTEST_UPDATE_COMMAND                "@UPDATE_COMMAND@")
set(CTEST_UPDATE_OPTIONS                "@UPDATE_OPTIONS@")
set(CTEST_CMAKE_GENERATOR               "@CMAKE_GENERATOR@")
set(CTEST_BUILD_CONFIGURATION           "Release")
#set(CTEST_MEMORYCHECK_COMMAND           "@MEMORYCHECK_COMMAND@")
#set(CTEST_MEMORYCHECK_SUPPRESSIONS_FILE "@MEMORYCHECK_SUPPRESSIONS_FILE@")
#set(CTEST_MEMORYCHECK_COMMAND_OPTIONS   "@MEMORYCHECK_COMMAND_OPTIONS@")
#set(CTEST_COVERAGE_COMMAND              "@COVERAGE_COMMAND@")
set(CTEST_NOTES_FILES                   "${CTEST_SCRIPT_DIRECTORY}/${CTEST_SCRIPT_NAME}")

#CTEST_EMPTY_BINARY_DIRECTORY(${CTEST_BINARY_DIRECTORY})

set(CTEST_DROP_METHOD "@DROP_METHOD@")

CTEST_START(Experimental TRACK Weekly)
CTEST_UPDATE(SOURCE "${CTEST_SOURCE_DIRECTORY}")
CTEST_CONFIGURE(BUILD "${CTEST_BINARY_DIRECTORY}")
CTEST_READ_CUSTOM_FILES("${CTEST_BINARY_DIRECTORY}")
CTEST_BUILD(BUILD "${CTEST_BINARY_DIRECTORY}")
CTEST_TEST(BUILD "${CTEST_BINARY_DIRECTORY}")
#CTEST_MEMCHECK(BUILD "${CTEST_BINARY_DIRECTORY}")
#CTEST_COVERAGE(BUILD "${CTEST_BINARY_DIRECTORY}")
CTEST_SUBMIT()
