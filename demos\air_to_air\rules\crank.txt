# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
/*
PURPOSE  Crank behavior node  
AUTHOR   Snyder
Classification: UNCLASSIFIED//FOUO

Technical Description:
   If i'm here, then I am guiding a shot.
   Delay offset by 10 seconds to enable more shots
   Offset the threat by APOLE_RDR (PRDATA variable)

*/
advanced_behavior crank

   script_variables 
      extern string faz_desired;
      extern bool change_desired;
      extern WsfPlatform iacid;
      extern string reason;
      extern WsfCommandChain iflite;
      extern bool pitch_up;
      extern double pr_altitude;
      extern double pr_speed;
      extern double pr_heading;
      extern bool first_pass;
      extern double time_ok;
      extern bool alerted;
      extern bool apole_tct;
      extern bool sam_threatnd;
      extern double rng_cls_sam;
      extern double rng_cls_hst;
      extern bool pump_per;
      extern bool threatnd;
      extern bool needil;
      extern WsfSA_EntityPerception ppmjid;
#      extern WsfBrawlerProcessor BRAWLER;
      extern Atmosphere atmos;
      extern bool iout_print;
         extern string iout_path;
      extern double t_phase;
      extern bool flt_guiding;
      extern Array<int> PLAYBOOK;      
   end_script_variables
   precondition 
      ////// Evaluate conditions that would prevent behavior alternative from running
      if (!PROCESSOR.IsA_TypeOf("WSF_SA_PROCESSOR"))
      {
         writeln_d("not a brawler processor!");
         return Failure("behavior not attached to a WSF_SA_PROCESSOR");
      }

      if (PLATFORM->rule_type == "ftr" && PLATFORM->faz == "crank")
      { return Success(); }
      else
      { return Failure(reason); }
 
   end_precondition
   
   execute 
       writeln_d("T = ",TIME_NOW," ",iacid.Name()," crank");   
#      FileIO iout = FileIO();
      bool BrawlMover;
      WsfBrawlerProcessor BRAWLER;
      if (iacid.Mover().Type() == "WSF_BRAWLER_MOVER")
      {
         for (int i=0; i<iacid.ProcessorCount();i+=1)
         {
            if (iacid.ProcessorEntry(i).Type() == "WSF_BRAWLER_PROCESSOR")
            {
               BrawlMover = true;
               BRAWLER = (WsfBrawlerProcessor)iacid.ProcessorEntry(i);
               break;
            }
         }
      }
      int plan_1 = PLAYBOOK[0]; 
      int plan_2 = PLAYBOOK[1]; // create local version of these to manipulate in this routine
      int plan_3 = PLAYBOOK[2];
#      if (iout_print)
#      {
#         iout.Open(iout_path, "append");
#      }
      WsfPlatform my_wing;
      string element = "";
      if (iacid.CommandChain("ELEMENT").IsValid())
      {
         WsfCommandChain el_chain = iacid.CommandChain("ELEMENT");
         if (el_chain.CommanderName() == iacid.Name() && el_chain.SubordinateCount() > 0)
         {  // loop through and pick the platform that's not a missile
            for (int el_ind = 0; el_ind < el_chain.SubordinateCount(); el_ind = el_ind + 1)
            {
               if (!el_chain.SubordinateEntry(el_ind).CategoryMemberOf("missile"))
               {
                  element = el_chain.SubordinateEntry(el_ind).Name(); // should only be one element
                  break;
               }
            }
         }
         else if (el_chain.CommanderName() != iacid.Name() && el_chain.Commander().IsValid())
         {
            element = el_chain.CommanderName(); // should only be one element
         }
      }
      if (element != "" && WsfSimulation.FindPlatform(element).IsValid()) 
      {  
         my_wing = WsfSimulation.FindPlatform(element); 
      }
      faz_desired = "crank"; 
      pr_heading = pole_hdg(iacid); // time ok, crank now
      pr_speed = iacid->APOLE_SPD*atmos.SonicVelocity(iacid.Altitude());
      pr_altitude = iacid->COMMIT_ALT;
      if (time_ok) // breifly stay on current heading for follow on shots
      {
         if (!needil && !my_wing.IsNull() &&  my_wing.IsValid())
         {
#            writeln(iacid.Name()," ",my_wing.Name());
            pr_heading = my_wing->pr_heading;
         }
         extern double pr_gees;
         if (BrawlMover)
         {
            pr_gees = BRAWLER.MaxSustainedGs();
         }
         else
         {
            pr_gees = 3;
         }
         pr_vector(iacid,pr_heading,pr_speed,pr_altitude,pr_gees);
      }
      else  // briefly waiting to crank, aim next shot 
      {
         pr_aim_missile(iacid,ppmjid.Track(),pr_speed);
      }

      if (iout_print) {iout.Write(write_str(iacid.Name()," pole_hdg... ",pr_heading,"\n"));}
 
      // if i'm threatend by a sam, go into sam react phase
      if (rng_cls_hst <= iacid->MERGE_RNG && time_ok)
      {
         if (iacid->NOTCH)
         {  
            faz_desired = "notch";
            reason = "NOTCH MRM DEFENSE";
            t_phase=TIME_NOW + 30;
         }
         else
         {  
            faz_desired = "merge";
            reason = "ACCEPT MERGE";
            t_phase=TIME_NOW + 10;
         }
      }
      else if ( alerted || rng_cls_hst <= iacid->DOR && flt_guiding)
      {  
          if ( plan_3 == 1 && pump_per && time_ok)
          { 
             faz_desired = "pump";
             reason = "ALWAYS PUMP AT MAR";
             t_phase=TIME_NOW + iacid->TDRAG;
          }
          else if ( plan_3 == 2 && pump_per && time_ok && threatnd)
          { 
             faz_desired = "pump";
             reason = "ALWAYS PUMP WHEN THREATENED";
             t_phase=TIME_NOW + iacid->TDRAG;
          }
      }   
      else if (time_ok && !flt_guiding && plan_1 > 3)
      {  
         t_phase=TIME_NOW + 5;
         if (plan_1 == 4) {faz_desired = "vectoring"; reason = "PLAN_1 = 4";}
      }
      else if (time_ok && !flt_guiding && plan_1 <= 3)
      {  
         t_phase=TIME_NOW + 5; 
         faz_desired = "direct"; 
         reason = "".Join({"PLAN_1 = ",(string)plan_1});
      }
      else if (!flt_guiding && (rng_cls_hst > iacid->COMMIT_RNG*1.5 || rng_cls_sam > iacid->COMMIT_RNG*1.5))
      {  
         faz_desired = "ingress";
         reason = "RECOMMIT...WEAPONS AND FUEL LEFT";
      }
#      if (iout_print){iout.Close();}
      return Success(reason);      
   end_execute

end_advanced_behavior
