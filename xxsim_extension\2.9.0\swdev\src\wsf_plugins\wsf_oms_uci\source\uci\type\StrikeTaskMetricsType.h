// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StrikeTaskMetricsType_h
#define Uci__Type__StrikeTaskMetricsType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__UnsignedIntAccessor_h)
# include "uci/base/UnsignedIntAccessor.h"
#endif

#if !defined(Uci__Type__StrikeTaskMetricsTargetingType_h)
# include "uci/type/StrikeTaskMetricsTargetingType.h"
#endif

#if !defined(Uci__Type__BDI_CollectionType_h)
# include "uci/type/BDI_CollectionType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the StrikeTaskMetricsType sequence accessor class */
      class StrikeTaskMetricsType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~StrikeTaskMetricsType()
         { }

         /** Returns this accessor's type constant, i.e. StrikeTaskMetricsType
           *
           * @return This accessor's type constant, i.e. StrikeTaskMetricsType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::strikeTaskMetricsType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StrikeTaskMetricsType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the NumWeaponsReleased.
           *
           * @return The value of the simple primitive data type identified by NumWeaponsReleased.
           */
         virtual xs::UnsignedInt getNumWeaponsReleased() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the NumWeaponsReleased.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setNumWeaponsReleased(xs::UnsignedInt value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Targeting.
           *
           * @return The acecssor that provides access to the complex content that is identified by Targeting.
           */
         virtual const uci::type::StrikeTaskMetricsTargetingType& getTargeting() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Targeting.
           *
           * @return The acecssor that provides access to the complex content that is identified by Targeting.
           */
         virtual uci::type::StrikeTaskMetricsTargetingType& getTargeting()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Targeting to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Targeting
           */
         virtual void setTargeting(const uci::type::StrikeTaskMetricsTargetingType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Targeting exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Targeting is emabled or not
           */
         virtual bool hasTargeting() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Targeting
           *
           * @param type = uci::type::accessorType::strikeTaskMetricsTargetingType This Accessor's accessor type
           */
         virtual void enableTargeting(uci::base::accessorType::AccessorType type = uci::type::accessorType::strikeTaskMetricsTargetingType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Targeting */
         virtual void clearTargeting()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BDI_Collection.
           *
           * @return The acecssor that provides access to the complex content that is identified by BDI_Collection.
           */
         virtual const uci::type::BDI_CollectionType& getBDI_Collection() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BDI_Collection.
           *
           * @return The acecssor that provides access to the complex content that is identified by BDI_Collection.
           */
         virtual uci::type::BDI_CollectionType& getBDI_Collection()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the BDI_Collection to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by BDI_Collection
           */
         virtual void setBDI_Collection(const uci::type::BDI_CollectionType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by BDI_Collection exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by BDI_Collection is emabled or not
           */
         virtual bool hasBDI_Collection() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by BDI_Collection
           *
           * @param type = uci::type::accessorType::bDI_CollectionType This Accessor's accessor type
           */
         virtual void enableBDI_Collection(uci::base::accessorType::AccessorType type = uci::type::accessorType::bDI_CollectionType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by BDI_Collection */
         virtual void clearBDI_Collection()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StrikeTaskMetricsType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StrikeTaskMetricsType to copy from
           */
         StrikeTaskMetricsType(const StrikeTaskMetricsType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StrikeTaskMetricsType to the contents of the StrikeTaskMetricsType
           * on the right hand side (rhs) of the assignment operator.StrikeTaskMetricsType [only available to derived classes]
           *
           * @param rhs The StrikeTaskMetricsType on the right hand side (rhs) of the assignment operator whose contents are used
           *      to set the contents of this uci::type::StrikeTaskMetricsType
           * @return A constant reference to this StrikeTaskMetricsType.
           */
         const StrikeTaskMetricsType& operator=(const StrikeTaskMetricsType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StrikeTaskMetricsType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StrikeTaskMetricsType_h

