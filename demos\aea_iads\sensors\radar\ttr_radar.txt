# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
# * * ************************************** * *
# *   ****** Demonstration input file ******   *
# *   ******      UNCLASSIFIED        ******   *
# * * ************************************** * *

antenna_pattern TTR_RADAR_ANTENNA
  sine_pattern
    peak_gain            20 dB
    minimum_gain         -10.0 db
    azimuth_beamwidth    1 deg
    elevation_beamwidth  1 deg
  end_sine_pattern
end_antenna_pattern

clutter_model TTR_RADAR_SEARCH_CLUTTER surface_clutter
end_clutter_model

clutter_model TTR_RADAR_ACQUIRE_CLUTTER surface_clutter
end_clutter_model

clutter_model TTR_RADAR_TRACK_CLUTTER surface_clutter
end_clutter_model

sensor TTR_RADAR WSF_RADAR_SENSOR
   #show_calibration_data

   selection_mode                 multiple

   slew_mode                      azimuth_and_elevation
   azimuth_slew_limits            -180 deg 180 deg
   elevation_slew_limits          0.0 deg 80.0 deg

   mode_template
      one_m2_detect_range            35.0 nm

      antenna_height                 4.0 m

      transmitter
         antenna_pattern             TTR_RADAR_ANTENNA
         power                       100.0 kw
         frequency                   9500 mhz
         internal_loss               2 db
         attenuation                 blake
         pulse_width                 0.8 usec
         pulse_repetition_interval   12.0 usec
         duty_cycle                  0.067   # 10*log10(0.8/12) = -11.76 dB loss
      end_transmitter

      receiver
         antenna_pattern             TTR_RADAR_ANTENNA
         bandwidth                   500.0 khz
         noise_power                 -160 dBw  # will be calibrated for 1 m^2
         internal_loss               7 dB
      end_receiver

      swerling_case                  1
      probability_of_false_alarm     1.0e-6
      detector_law                   linear

      compute_measurement_errors true

      reports_range
      reports_bearing
      reports_elevation
      reports_velocity
   end_mode_template

   mode SEARCH
      frame_time                     4.0 sec

      maximum_request_count          1

      cue_mode                       azimuth
      scan_mode                      azimuth_and_elevation
      azimuth_scan_limits            -10 deg 10 deg
      elevation_scan_limits            0 deg 40 deg

      hits_to_establish_track        3 5
      hits_to_maintain_track         1 3

      track_quality                  0.6

      on_success TRACK
   end_mode

   mode ACQUIRE
      frame_time                     2.0 sec

      maximum_request_count          1

      scan_mode                      azimuth_and_elevation
      azimuth_scan_limits            -5 deg 5 deg
      elevation_scan_limits          -5 deg 5 deg

      hits_to_establish_track        3 5
      hits_to_maintain_track         1 3

      track_quality                  0.8   // near 'weapons' grade
   end_mode

   mode TRACK
      frame_time                     1.0 sec

      maximum_request_count          6

      scan_mode                      azimuth_and_elevation
      azimuth_scan_limits            -1 deg 1 deg
      elevation_scan_limits          -1 deg 1 deg

      receiver
         bandwidth                   250.0 hz
         noise_power                 -175 dBw  // will be calibrated for 1 m^2
         internal_loss               7 dB
      end_receiver

      clutter_model TTR_RADAR_TRACK_CLUTTER

      hits_to_establish_track        3 5
      hits_to_maintain_track         1 3

      track_quality                  1.0   // 'weapons' grade
   end_mode
end_sensor
