// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SpecialCodeDataLinkIdentifierType_h
#define Uci__Type__SpecialCodeDataLinkIdentifierType_h 1

#if !defined(Uci__Type__DataLinkIdentifierPET_h)
# include "uci/type/DataLinkIdentifierPET.h"
#endif

#if !defined(Uci__Type__SpecialCode2Type_h)
# include "uci/type/SpecialCode2Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is a polymorphic extension which allows for the extension of the base DataLinkIdentifierPET. This represents
        * data used to identify a Special Code participant
        */
      class SpecialCodeDataLinkIdentifierType : public virtual uci::type::DataLinkIdentifierPET {
      public:

         /** The destructor */
         virtual ~SpecialCodeDataLinkIdentifierType()
         { }

         /** Returns this accessor's type constant, i.e. SpecialCodeDataLinkIdentifierType
           *
           * @return This accessor's type constant, i.e. SpecialCodeDataLinkIdentifierType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::specialCodeDataLinkIdentifierType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SpecialCodeDataLinkIdentifierType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the SpecialCode2.
           *
           * @return The value of the string data type identified by SpecialCode2.
           */
         virtual const uci::type::SpecialCode2Type& getSpecialCode2() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the SpecialCode2.
           *
           * @return The value of the string data type identified by SpecialCode2.
           */
         virtual uci::type::SpecialCode2Type& getSpecialCode2()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the SpecialCode2 to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setSpecialCode2(const uci::type::SpecialCode2Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the SpecialCode2 to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setSpecialCode2(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the SpecialCode2 to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setSpecialCode2(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by SpecialCode2 exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by SpecialCode2 is emabled or not
           */
         virtual bool hasSpecialCode2() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by SpecialCode2
           *
           * @param type = uci::type::accessorType::specialCode2Type This Accessor's accessor type
           */
         virtual void enableSpecialCode2(uci::base::accessorType::AccessorType type = uci::type::accessorType::specialCode2Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by SpecialCode2 */
         virtual void clearSpecialCode2()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SpecialCodeDataLinkIdentifierType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SpecialCodeDataLinkIdentifierType to copy from
           */
         SpecialCodeDataLinkIdentifierType(const SpecialCodeDataLinkIdentifierType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SpecialCodeDataLinkIdentifierType to the contents of the
           * SpecialCodeDataLinkIdentifierType on the right hand side (rhs) of the assignment
           * operator.SpecialCodeDataLinkIdentifierType [only available to derived classes]
           *
           * @param rhs The SpecialCodeDataLinkIdentifierType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::SpecialCodeDataLinkIdentifierType
           * @return A constant reference to this SpecialCodeDataLinkIdentifierType.
           */
         const SpecialCodeDataLinkIdentifierType& operator=(const SpecialCodeDataLinkIdentifierType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SpecialCodeDataLinkIdentifierType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SpecialCodeDataLinkIdentifierType_h

