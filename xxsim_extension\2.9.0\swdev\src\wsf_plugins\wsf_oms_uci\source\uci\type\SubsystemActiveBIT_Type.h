// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SubsystemActiveBIT_Type_h
#define Uci__Type__SubsystemActiveBIT_Type_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__BIT_ID_Type_h)
# include "uci/type/BIT_ID_Type.h"
#endif

#if !defined(Uci__Type__DateTimeType_h)
# include "uci/type/DateTimeType.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SubsystemActiveBIT_Type sequence accessor class */
      class SubsystemActiveBIT_Type : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SubsystemActiveBIT_Type()
         { }

         /** Returns this accessor's type constant, i.e. SubsystemActiveBIT_Type
           *
           * @return This accessor's type constant, i.e. SubsystemActiveBIT_Type
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::subsystemActiveBIT_Type;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SubsystemActiveBIT_Type& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BIT_ID.
           *
           * @return The acecssor that provides access to the complex content that is identified by BIT_ID.
           */
         virtual const uci::type::BIT_ID_Type& getBIT_ID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the BIT_ID.
           *
           * @return The acecssor that provides access to the complex content that is identified by BIT_ID.
           */
         virtual uci::type::BIT_ID_Type& getBIT_ID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the BIT_ID to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by BIT_ID
           */
         virtual void setBIT_ID(const uci::type::BIT_ID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the EstimatedCompletionTime.
           *
           * @return The value of the simple primitive data type identified by EstimatedCompletionTime.
           */
         virtual uci::type::DateTimeTypeValue getEstimatedCompletionTime() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the EstimatedCompletionTime.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setEstimatedCompletionTime(uci::type::DateTimeTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by EstimatedCompletionTime exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by EstimatedCompletionTime is emabled or not
           */
         virtual bool hasEstimatedCompletionTime() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by EstimatedCompletionTime
           *
           * @param type = uci::type::accessorType::dateTimeType This Accessor's accessor type
           */
         virtual void enableEstimatedCompletionTime(uci::base::accessorType::AccessorType type = uci::type::accessorType::dateTimeType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by EstimatedCompletionTime */
         virtual void clearEstimatedCompletionTime()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the EstimatedPercentComplete.
           *
           * @return The value of the simple primitive data type identified by EstimatedPercentComplete.
           */
         virtual uci::type::PercentTypeValue getEstimatedPercentComplete() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the EstimatedPercentComplete.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setEstimatedPercentComplete(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by EstimatedPercentComplete exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by EstimatedPercentComplete is emabled or not
           */
         virtual bool hasEstimatedPercentComplete() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by EstimatedPercentComplete
           *
           * @param type = uci::type::accessorType::percentType This Accessor's accessor type
           */
         virtual void enableEstimatedPercentComplete(uci::base::accessorType::AccessorType type = uci::type::accessorType::percentType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by EstimatedPercentComplete */
         virtual void clearEstimatedPercentComplete()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SubsystemActiveBIT_Type()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SubsystemActiveBIT_Type to copy from
           */
         SubsystemActiveBIT_Type(const SubsystemActiveBIT_Type& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SubsystemActiveBIT_Type to the contents of the
           * SubsystemActiveBIT_Type on the right hand side (rhs) of the assignment operator.SubsystemActiveBIT_Type [only
           * available to derived classes]
           *
           * @param rhs The SubsystemActiveBIT_Type on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SubsystemActiveBIT_Type
           * @return A constant reference to this SubsystemActiveBIT_Type.
           */
         const SubsystemActiveBIT_Type& operator=(const SubsystemActiveBIT_Type& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SubsystemActiveBIT_Type


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SubsystemActiveBIT_Type_h

