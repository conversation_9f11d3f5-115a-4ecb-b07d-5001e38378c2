// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StandardIdentityType_h
#define Uci__Type__StandardIdentityType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__StandardIdentityEnum_h)
# include "uci/type/StandardIdentityEnum.h"
#endif

#if !defined(Uci__Type__CountryCodeType_h)
# include "uci/type/CountryCodeType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the StandardIdentityType sequence accessor class */
      class StandardIdentityType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~StandardIdentityType()
         { }

         /** Returns this accessor's type constant, i.e. StandardIdentityType
           *
           * @return This accessor's type constant, i.e. StandardIdentityType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::standardIdentityType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StandardIdentityType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the StandardIdentity.
           *
           * @return The value of the enumeration identified by StandardIdentity.
           */
         virtual const uci::type::StandardIdentityEnum& getStandardIdentity() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the StandardIdentity.
           *
           * @return The value of the enumeration identified by StandardIdentity.
           */
         virtual uci::type::StandardIdentityEnum& getStandardIdentity()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the StandardIdentity.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setStandardIdentity(const uci::type::StandardIdentityEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the StandardIdentity.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setStandardIdentity(uci::type::StandardIdentityEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Allegiance.
           *
           * @return The value of the string data type identified by Allegiance.
           */
         virtual const uci::type::CountryCodeType& getAllegiance() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Allegiance.
           *
           * @return The value of the string data type identified by Allegiance.
           */
         virtual uci::type::CountryCodeType& getAllegiance()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Allegiance to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setAllegiance(const uci::type::CountryCodeType& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Allegiance to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setAllegiance(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Allegiance to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setAllegiance(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Allegiance exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Allegiance is emabled or not
           */
         virtual bool hasAllegiance() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Allegiance
           *
           * @param type = uci::type::accessorType::countryCodeType This Accessor's accessor type
           */
         virtual void enableAllegiance(uci::base::accessorType::AccessorType type = uci::type::accessorType::countryCodeType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Allegiance */
         virtual void clearAllegiance()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StandardIdentityType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StandardIdentityType to copy from
           */
         StandardIdentityType(const StandardIdentityType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StandardIdentityType to the contents of the StandardIdentityType
           * on the right hand side (rhs) of the assignment operator.StandardIdentityType [only available to derived classes]
           *
           * @param rhs The StandardIdentityType on the right hand side (rhs) of the assignment operator whose contents are used
           *      to set the contents of this uci::type::StandardIdentityType
           * @return A constant reference to this StandardIdentityType.
           */
         const StandardIdentityType& operator=(const StandardIdentityType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StandardIdentityType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StandardIdentityType_h

