# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
define_path_variable CASE strike
log_file output/$(CASE).log

include setup.txt

include scenarios/iads_laydown.txt
include scenarios/targets.txt
include scenarios/strike.txt

event_output file output/$(CASE).evt end_event_output
csv_event_output file output/$(CASE).csv end_csv_event_output
event_pipe file output/$(CASE).aer end_event_pipe

end_time 40 min

platform_availability
   name 100_soj availability 1.0
   name 200_soj availability 1.0
   name 300_soj availability 1.0
   name 100_ucav availability 1.0
   name 200_ucav availability 1.0
   name 300_ucav availability 1.0
   name 400_ucav availability 1.0
end_platform_availability
