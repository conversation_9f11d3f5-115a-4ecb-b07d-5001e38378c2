// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__ThreatAssessmentSystemType_h
#define Uci__Type__ThreatAssessmentSystemType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__EntityID_Type_h)
# include "uci/type/EntityID_Type.h"
#endif

#if !defined(Uci__Type__ThreatAssessmentMetricsType_h)
# include "uci/type/ThreatAssessmentMetricsType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the ThreatAssessmentSystemType sequence accessor class */
      class ThreatAssessmentSystemType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~ThreatAssessmentSystemType()
         { }

         /** Returns this accessor's type constant, i.e. ThreatAssessmentSystemType
           *
           * @return This accessor's type constant, i.e. ThreatAssessmentSystemType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::threatAssessmentSystemType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const ThreatAssessmentSystemType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityID.
           */
         virtual const uci::type::EntityID_Type& getEntityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the EntityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by EntityID.
           */
         virtual uci::type::EntityID_Type& getEntityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the EntityID to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by EntityID
           */
         virtual void setEntityID(const uci::type::EntityID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WithoutSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by WithoutSuppression.
           */
         virtual const uci::type::ThreatAssessmentMetricsType& getWithoutSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WithoutSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by WithoutSuppression.
           */
         virtual uci::type::ThreatAssessmentMetricsType& getWithoutSuppression()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WithoutSuppression to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WithoutSuppression
           */
         virtual void setWithoutSuppression(const uci::type::ThreatAssessmentMetricsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by WithoutSuppression exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by WithoutSuppression is emabled or not
           */
         virtual bool hasWithoutSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by WithoutSuppression
           *
           * @param type = uci::type::accessorType::threatAssessmentMetricsType This Accessor's accessor type
           */
         virtual void enableWithoutSuppression(uci::base::accessorType::AccessorType type = uci::type::accessorType::threatAssessmentMetricsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by WithoutSuppression */
         virtual void clearWithoutSuppression()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WithSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by WithSuppression.
           */
         virtual const uci::type::ThreatAssessmentMetricsType& getWithSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WithSuppression.
           *
           * @return The acecssor that provides access to the complex content that is identified by WithSuppression.
           */
         virtual uci::type::ThreatAssessmentMetricsType& getWithSuppression()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WithSuppression to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WithSuppression
           */
         virtual void setWithSuppression(const uci::type::ThreatAssessmentMetricsType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by WithSuppression exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by WithSuppression is emabled or not
           */
         virtual bool hasWithSuppression() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by WithSuppression
           *
           * @param type = uci::type::accessorType::threatAssessmentMetricsType This Accessor's accessor type
           */
         virtual void enableWithSuppression(uci::base::accessorType::AccessorType type = uci::type::accessorType::threatAssessmentMetricsType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by WithSuppression */
         virtual void clearWithSuppression()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         ThreatAssessmentSystemType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The ThreatAssessmentSystemType to copy from
           */
         ThreatAssessmentSystemType(const ThreatAssessmentSystemType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this ThreatAssessmentSystemType to the contents of the
           * ThreatAssessmentSystemType on the right hand side (rhs) of the assignment operator.ThreatAssessmentSystemType [only
           * available to derived classes]
           *
           * @param rhs The ThreatAssessmentSystemType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::ThreatAssessmentSystemType
           * @return A constant reference to this ThreatAssessmentSystemType.
           */
         const ThreatAssessmentSystemType& operator=(const ThreatAssessmentSystemType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // ThreatAssessmentSystemType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__ThreatAssessmentSystemType_h

