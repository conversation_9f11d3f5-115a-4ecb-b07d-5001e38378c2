// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SupportedModeType_h
#define Uci__Type__SupportedModeType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__CommAntennaModeType_h)
# include "uci/type/CommAntennaModeType.h"
#endif

#if !defined(Uci__Type__CommCapabilityEnum_h)
# include "uci/type/CommCapabilityEnum.h"
#endif

#if !defined(Uci__Type__DataRateType_h)
# include "uci/type/DataRateType.h"
#endif

#if !defined(Uci__Type__FrequencyRangeType_h)
# include "uci/type/FrequencyRangeType.h"
#endif

#if !defined(Uci__Type__DecibelType_h)
# include "uci/type/DecibelType.h"
#endif

#if !defined(Uci__Type__AnglePositiveType_h)
# include "uci/type/AnglePositiveType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SupportedModeType sequence accessor class */
      class SupportedModeType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SupportedModeType()
         { }

         /** Returns this accessor's type constant, i.e. SupportedModeType
           *
           * @return This accessor's type constant, i.e. SupportedModeType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::supportedModeType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SupportedModeType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AntennaModeID.
           *
           * @return The acecssor that provides access to the complex content that is identified by AntennaModeID.
           */
         virtual const uci::type::CommAntennaModeType& getAntennaModeID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AntennaModeID.
           *
           * @return The acecssor that provides access to the complex content that is identified by AntennaModeID.
           */
         virtual uci::type::CommAntennaModeType& getAntennaModeID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the AntennaModeID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by AntennaModeID
           */
         virtual void setAntennaModeID(const uci::type::CommAntennaModeType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Waveform.
           *
           * @return The value of the enumeration identified by Waveform.
           */
         virtual const uci::type::CommCapabilityEnum& getWaveform() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Waveform.
           *
           * @return The value of the enumeration identified by Waveform.
           */
         virtual uci::type::CommCapabilityEnum& getWaveform()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Waveform.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWaveform(const uci::type::CommCapabilityEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Waveform.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setWaveform(uci::type::CommCapabilityEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the UplinkDataRate.
           *
           * @return The value of the simple primitive data type identified by UplinkDataRate.
           */
         virtual uci::type::DataRateTypeValue getUplinkDataRate() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the UplinkDataRate.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setUplinkDataRate(uci::type::DataRateTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the UplinkFrequencyRange.
           *
           * @return The acecssor that provides access to the complex content that is identified by UplinkFrequencyRange.
           */
         virtual const uci::type::FrequencyRangeType& getUplinkFrequencyRange() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the UplinkFrequencyRange.
           *
           * @return The acecssor that provides access to the complex content that is identified by UplinkFrequencyRange.
           */
         virtual uci::type::FrequencyRangeType& getUplinkFrequencyRange()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the UplinkFrequencyRange to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by UplinkFrequencyRange
           */
         virtual void setUplinkFrequencyRange(const uci::type::FrequencyRangeType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the DownlinkDataRate.
           *
           * @return The value of the simple primitive data type identified by DownlinkDataRate.
           */
         virtual uci::type::DataRateTypeValue getDownlinkDataRate() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the DownlinkDataRate.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setDownlinkDataRate(uci::type::DataRateTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the DownlinkFrequencyRange.
           *
           * @return The acecssor that provides access to the complex content that is identified by DownlinkFrequencyRange.
           */
         virtual const uci::type::FrequencyRangeType& getDownlinkFrequencyRange() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the DownlinkFrequencyRange.
           *
           * @return The acecssor that provides access to the complex content that is identified by DownlinkFrequencyRange.
           */
         virtual uci::type::FrequencyRangeType& getDownlinkFrequencyRange()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the DownlinkFrequencyRange to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by DownlinkFrequencyRange
           */
         virtual void setDownlinkFrequencyRange(const uci::type::FrequencyRangeType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Gain.
           *
           * @return The value of the simple primitive data type identified by Gain.
           */
         virtual uci::type::DecibelTypeValue getGain() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Gain.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setGain(uci::type::DecibelTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Beamwidth.
           *
           * @return The value of the simple primitive data type identified by Beamwidth.
           */
         virtual uci::type::AnglePositiveTypeValue getBeamwidth() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Beamwidth.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setBeamwidth(uci::type::AnglePositiveTypeValue value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SupportedModeType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SupportedModeType to copy from
           */
         SupportedModeType(const SupportedModeType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SupportedModeType to the contents of the SupportedModeType on the
           * right hand side (rhs) of the assignment operator.SupportedModeType [only available to derived classes]
           *
           * @param rhs The SupportedModeType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::SupportedModeType
           * @return A constant reference to this SupportedModeType.
           */
         const SupportedModeType& operator=(const SupportedModeType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SupportedModeType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SupportedModeType_h

