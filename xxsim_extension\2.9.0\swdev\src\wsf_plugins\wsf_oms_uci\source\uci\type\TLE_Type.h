// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TLE_Type_h
#define Uci__Type__TLE_Type_h 1

#if !defined(Uci__Type__TLE_BaseType_h)
# include "uci/type/TLE_BaseType.h"
#endif

#if !defined(Uci__Type__SatelliteIdentifierType_h)
# include "uci/type/SatelliteIdentifierType.h"
#endif

#if !defined(Ismclassall__Type__CVEnumISMClassificationAll_h)
# include "ismclassall/type/CVEnumISMClassificationAll.h"
#endif

#if !defined(Uci__Base__UnsignedByteAccessor_h)
# include "uci/base/UnsignedByteAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Two Line Element (TLE) is a standard format for conveying the orbital elements of an Earth orbiting object at a point
        * in time. Prediction models may be used to determine past and future positions of the object based on the TLE content.
        */
      class TLE_Type : public virtual uci::type::TLE_BaseType {
      public:

         /** The destructor */
         virtual ~TLE_Type()
         { }

         /** Returns this accessor's type constant, i.e. TLE_Type
           *
           * @return This accessor's type constant, i.e. TLE_Type
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::tLE_Type;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TLE_Type& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SatelliteIdentity.
           *
           * @return The acecssor that provides access to the complex content that is identified by SatelliteIdentity.
           */
         virtual const uci::type::SatelliteIdentifierType& getSatelliteIdentity() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SatelliteIdentity.
           *
           * @return The acecssor that provides access to the complex content that is identified by SatelliteIdentity.
           */
         virtual uci::type::SatelliteIdentifierType& getSatelliteIdentity()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SatelliteIdentity to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SatelliteIdentity
           */
         virtual void setSatelliteIdentity(const uci::type::SatelliteIdentifierType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Classification.
           *
           * @return The value of the enumeration identified by Classification.
           */
         virtual const ismclassall::type::CVEnumISMClassificationAll& getClassification() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Classification.
           *
           * @return The value of the enumeration identified by Classification.
           */
         virtual ismclassall::type::CVEnumISMClassificationAll& getClassification()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Classification.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setClassification(const ismclassall::type::CVEnumISMClassificationAll& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Classification.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setClassification(ismclassall::type::CVEnumISMClassificationAll::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the EphemerisType.
           *
           * @return The value of the simple primitive data type identified by EphemerisType.
           */
         virtual xs::UnsignedByte getEphemerisType() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the EphemerisType.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setEphemerisType(xs::UnsignedByte value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TLE_Type()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TLE_Type to copy from
           */
         TLE_Type(const TLE_Type& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TLE_Type to the contents of the TLE_Type on the right hand side
           * (rhs) of the assignment operator.TLE_Type [only available to derived classes]
           *
           * @param rhs The TLE_Type on the right hand side (rhs) of the assignment operator whose contents are used to set the
           *      contents of this uci::type::TLE_Type
           * @return A constant reference to this TLE_Type.
           */
         const TLE_Type& operator=(const TLE_Type& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TLE_Type


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TLE_Type_h

