// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WeatherMDT_h
#define Uci__Type__WeatherMDT_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__WeatherReportDataType_h)
# include "uci/type/WeatherReportDataType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WeatherMDT sequence accessor class */
      class WeatherMDT : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WeatherMDT()
         { }

         /** Returns this accessor's type constant, i.e. WeatherMDT
           *
           * @return This accessor's type constant, i.e. WeatherMDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::weatherMDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WeatherMDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherReport.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherReport.
           */
         virtual const uci::type::WeatherReportDataType& getWeatherReport() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the WeatherReport.
           *
           * @return The acecssor that provides access to the complex content that is identified by WeatherReport.
           */
         virtual uci::type::WeatherReportDataType& getWeatherReport()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the WeatherReport to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by WeatherReport
           */
         virtual void setWeatherReport(const uci::type::WeatherReportDataType& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WeatherMDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WeatherMDT to copy from
           */
         WeatherMDT(const WeatherMDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WeatherMDT to the contents of the WeatherMDT on the right hand
           * side (rhs) of the assignment operator.WeatherMDT [only available to derived classes]
           *
           * @param rhs The WeatherMDT on the right hand side (rhs) of the assignment operator whose contents are used to set the
           *      contents of this uci::type::WeatherMDT
           * @return A constant reference to this WeatherMDT.
           */
         const WeatherMDT& operator=(const WeatherMDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WeatherMDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WeatherMDT_h

