// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SystemCapabilitySpecificationType_h
#define Uci__Type__SystemCapabilitySpecificationType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SubsystemSpecificationType_h)
# include "uci/type/SubsystemSpecificationType.h"
#endif

#if !defined(Uci__Type__CapabilitySpecificationType_h)
# include "uci/type/CapabilitySpecificationType.h"
#endif

#if !defined(Uci__Type__CapabilityTypeEnum_h)
# include "uci/type/CapabilityTypeEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Specification of a group of capabilities, either explicitly, via a common Subsystem, or by type. */
      class SystemCapabilitySpecificationType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SystemCapabilitySpecificationType()
         { }

         /** Returns this accessor's type constant, i.e. SystemCapabilitySpecificationType
           *
           * @return This accessor's type constant, i.e. SystemCapabilitySpecificationType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::systemCapabilitySpecificationType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SystemCapabilitySpecificationType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Reference a set of capabilities by a common subsystem. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SubsystemSpecificationType, uci::type::accessorType::subsystemSpecificationType> SubsystemReference;

         /** Reference a specific set of capabilities. If a subsystem was specified in the sibling SubsystemReference that has
           * this capability, the capabilities considered for that subststem will be limited to the capabilities specified here.
           * For capabilities not included in any specified Subsystems, they are to be considered in addition to all capabilities
           * included in the specified subsystems. [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::CapabilitySpecificationType, uci::type::accessorType::capabilitySpecificationType> CapabilityReference;

         /** Reference a set of capabilities by a common type. Can be used in conjunction with it's subling elements (ie.
           * Capabilities on this Subsystem of CapabilityType). [Minimum occurrences: 0] [Maximum occurrences: 14]
           */
         typedef uci::base::BoundedList<uci::type::CapabilityTypeEnum, uci::type::accessorType::capabilityTypeEnum> CapabilityType;

         /** Returns the bounded list that is identified by the SubsystemReference.
           *
           * @return The bounded list identified by SubsystemReference.
           */
         virtual const uci::type::SystemCapabilitySpecificationType::SubsystemReference& getSubsystemReference() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the SubsystemReference.
           *
           * @return The bounded list identified by SubsystemReference.
           */
         virtual uci::type::SystemCapabilitySpecificationType::SubsystemReference& getSubsystemReference()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the SubsystemReference.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setSubsystemReference(const uci::type::SystemCapabilitySpecificationType::SubsystemReference& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CapabilityReference.
           *
           * @return The bounded list identified by CapabilityReference.
           */
         virtual const uci::type::SystemCapabilitySpecificationType::CapabilityReference& getCapabilityReference() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CapabilityReference.
           *
           * @return The bounded list identified by CapabilityReference.
           */
         virtual uci::type::SystemCapabilitySpecificationType::CapabilityReference& getCapabilityReference()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the CapabilityReference.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCapabilityReference(const uci::type::SystemCapabilitySpecificationType::CapabilityReference& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CapabilityType.
           *
           * @return The bounded list identified by CapabilityType.
           */
         virtual const uci::type::SystemCapabilitySpecificationType::CapabilityType& getCapabilityType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CapabilityType.
           *
           * @return The bounded list identified by CapabilityType.
           */
         virtual uci::type::SystemCapabilitySpecificationType::CapabilityType& getCapabilityType()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the CapabilityType.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCapabilityType(const uci::type::SystemCapabilitySpecificationType::CapabilityType& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SystemCapabilitySpecificationType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SystemCapabilitySpecificationType to copy from
           */
         SystemCapabilitySpecificationType(const SystemCapabilitySpecificationType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SystemCapabilitySpecificationType to the contents of the
           * SystemCapabilitySpecificationType on the right hand side (rhs) of the assignment
           * operator.SystemCapabilitySpecificationType [only available to derived classes]
           *
           * @param rhs The SystemCapabilitySpecificationType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::SystemCapabilitySpecificationType
           * @return A constant reference to this SystemCapabilitySpecificationType.
           */
         const SystemCapabilitySpecificationType& operator=(const SystemCapabilitySpecificationType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SystemCapabilitySpecificationType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SystemCapabilitySpecificationType_h

