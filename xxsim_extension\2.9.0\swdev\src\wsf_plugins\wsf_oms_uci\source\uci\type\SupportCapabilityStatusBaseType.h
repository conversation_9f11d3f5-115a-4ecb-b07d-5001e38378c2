// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SupportCapabilityStatusBaseType_h
#define Uci__Type__SupportCapabilityStatusBaseType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SupportCapabilityID_Type_h)
# include "uci/type/SupportCapabilityID_Type.h"
#endif

#if !defined(Uci__Type__AvailabilityInfoType_h)
# include "uci/type/AvailabilityInfoType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SupportCapabilityStatusBaseType sequence accessor class */
      class SupportCapabilityStatusBaseType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SupportCapabilityStatusBaseType()
         { }

         /** Returns this accessor's type constant, i.e. SupportCapabilityStatusBaseType
           *
           * @return This accessor's type constant, i.e. SupportCapabilityStatusBaseType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::supportCapabilityStatusBaseType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SupportCapabilityStatusBaseType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SupportCapabilityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SupportCapabilityID.
           */
         virtual const uci::type::SupportCapabilityID_Type& getSupportCapabilityID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SupportCapabilityID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SupportCapabilityID.
           */
         virtual uci::type::SupportCapabilityID_Type& getSupportCapabilityID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SupportCapabilityID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SupportCapabilityID
           */
         virtual void setSupportCapabilityID(const uci::type::SupportCapabilityID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AvailabilityInfo.
           *
           * @return The acecssor that provides access to the complex content that is identified by AvailabilityInfo.
           */
         virtual const uci::type::AvailabilityInfoType& getAvailabilityInfo() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the AvailabilityInfo.
           *
           * @return The acecssor that provides access to the complex content that is identified by AvailabilityInfo.
           */
         virtual uci::type::AvailabilityInfoType& getAvailabilityInfo()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the AvailabilityInfo to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by AvailabilityInfo
           */
         virtual void setAvailabilityInfo(const uci::type::AvailabilityInfoType& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SupportCapabilityStatusBaseType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SupportCapabilityStatusBaseType to copy from
           */
         SupportCapabilityStatusBaseType(const SupportCapabilityStatusBaseType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SupportCapabilityStatusBaseType to the contents of the
           * SupportCapabilityStatusBaseType on the right hand side (rhs) of the assignment
           * operator.SupportCapabilityStatusBaseType [only available to derived classes]
           *
           * @param rhs The SupportCapabilityStatusBaseType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::SupportCapabilityStatusBaseType
           * @return A constant reference to this SupportCapabilityStatusBaseType.
           */
         const SupportCapabilityStatusBaseType& operator=(const SupportCapabilityStatusBaseType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SupportCapabilityStatusBaseType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SupportCapabilityStatusBaseType_h

