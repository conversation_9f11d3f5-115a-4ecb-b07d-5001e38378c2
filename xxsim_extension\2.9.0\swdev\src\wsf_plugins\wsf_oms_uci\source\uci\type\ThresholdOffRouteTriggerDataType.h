// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__ThresholdOffRouteTriggerDataType_h
#define Uci__Type__ThresholdOffRouteTriggerDataType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__DistanceType_h)
# include "uci/type/DistanceType.h"
#endif

#if !defined(Uci__Type__TimeErrorType_h)
# include "uci/type/TimeErrorType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the ThresholdOffRouteTriggerDataType sequence accessor class */
      class ThresholdOffRouteTriggerDataType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~ThresholdOffRouteTriggerDataType()
         { }

         /** Returns this accessor's type constant, i.e. ThresholdOffRouteTriggerDataType
           *
           * @return This accessor's type constant, i.e. ThresholdOffRouteTriggerDataType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::thresholdOffRouteTriggerDataType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const ThresholdOffRouteTriggerDataType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the TrackError.
           *
           * @return The value of the simple primitive data type identified by TrackError.
           */
         virtual uci::type::DistanceTypeValue getTrackError() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the TrackError.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setTrackError(uci::type::DistanceTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TrackError exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TrackError is emabled or not
           */
         virtual bool hasTrackError() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TrackError
           *
           * @param type = uci::type::accessorType::distanceType This Accessor's accessor type
           */
         virtual void enableTrackError(uci::base::accessorType::AccessorType type = uci::type::accessorType::distanceType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TrackError */
         virtual void clearTrackError()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the AltitudeError.
           *
           * @return The value of the simple primitive data type identified by AltitudeError.
           */
         virtual uci::type::DistanceTypeValue getAltitudeError() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the AltitudeError.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setAltitudeError(uci::type::DistanceTypeValue value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by AltitudeError exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by AltitudeError is emabled or not
           */
         virtual bool hasAltitudeError() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by AltitudeError
           *
           * @param type = uci::type::accessorType::distanceType This Accessor's accessor type
           */
         virtual void enableAltitudeError(uci::base::accessorType::AccessorType type = uci::type::accessorType::distanceType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by AltitudeError */
         virtual void clearAltitudeError()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TimeError.
           *
           * @return The acecssor that provides access to the complex content that is identified by TimeError.
           */
         virtual const uci::type::TimeErrorType& getTimeError() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the TimeError.
           *
           * @return The acecssor that provides access to the complex content that is identified by TimeError.
           */
         virtual uci::type::TimeErrorType& getTimeError()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the TimeError to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by TimeError
           */
         virtual void setTimeError(const uci::type::TimeErrorType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by TimeError exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by TimeError is emabled or not
           */
         virtual bool hasTimeError() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by TimeError
           *
           * @param type = uci::type::accessorType::timeErrorType This Accessor's accessor type
           */
         virtual void enableTimeError(uci::base::accessorType::AccessorType type = uci::type::accessorType::timeErrorType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by TimeError */
         virtual void clearTimeError()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         ThresholdOffRouteTriggerDataType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The ThresholdOffRouteTriggerDataType to copy from
           */
         ThresholdOffRouteTriggerDataType(const ThresholdOffRouteTriggerDataType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this ThresholdOffRouteTriggerDataType to the contents of the
           * ThresholdOffRouteTriggerDataType on the right hand side (rhs) of the assignment
           * operator.ThresholdOffRouteTriggerDataType [only available to derived classes]
           *
           * @param rhs The ThresholdOffRouteTriggerDataType on the right hand side (rhs) of the assignment operator whose
           *      contents are used to set the contents of this uci::type::ThresholdOffRouteTriggerDataType
           * @return A constant reference to this ThresholdOffRouteTriggerDataType.
           */
         const ThresholdOffRouteTriggerDataType& operator=(const ThresholdOffRouteTriggerDataType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // ThresholdOffRouteTriggerDataType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__ThresholdOffRouteTriggerDataType_h

