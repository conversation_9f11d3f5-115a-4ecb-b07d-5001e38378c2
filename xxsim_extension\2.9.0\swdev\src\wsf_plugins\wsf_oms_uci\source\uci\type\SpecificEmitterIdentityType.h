// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SpecificEmitterIdentityType_h
#define Uci__Type__SpecificEmitterIdentityType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__ForeignKeyType_h)
# include "uci/type/ForeignKeyType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SpecificEmitterIdentityType sequence accessor class */
      class SpecificEmitterIdentityType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SpecificEmitterIdentityType()
         { }

         /** Returns this accessor's type constant, i.e. SpecificEmitterIdentityType
           *
           * @return This accessor's type constant, i.e. SpecificEmitterIdentityType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::specificEmitterIdentityType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SpecificEmitterIdentityType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SpecificEmitterID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SpecificEmitterID.
           */
         virtual const uci::type::ForeignKeyType& getSpecificEmitterID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SpecificEmitterID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SpecificEmitterID.
           */
         virtual uci::type::ForeignKeyType& getSpecificEmitterID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SpecificEmitterID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SpecificEmitterID
           */
         virtual void setSpecificEmitterID(const uci::type::ForeignKeyType& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SpecificEmitterIdentityType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SpecificEmitterIdentityType to copy from
           */
         SpecificEmitterIdentityType(const SpecificEmitterIdentityType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SpecificEmitterIdentityType to the contents of the
           * SpecificEmitterIdentityType on the right hand side (rhs) of the assignment operator.SpecificEmitterIdentityType [only
           * available to derived classes]
           *
           * @param rhs The SpecificEmitterIdentityType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SpecificEmitterIdentityType
           * @return A constant reference to this SpecificEmitterIdentityType.
           */
         const SpecificEmitterIdentityType& operator=(const SpecificEmitterIdentityType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SpecificEmitterIdentityType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SpecificEmitterIdentityType_h

