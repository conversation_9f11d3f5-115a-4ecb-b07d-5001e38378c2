// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SpecificIdentityConfidenceType_h
#define Uci__Type__SpecificIdentityConfidenceType_h 1

#if !defined(Uci__Type__SpecificIdentityType_h)
# include "uci/type/SpecificIdentityType.h"
#endif

#if !defined(Uci__Type__PercentType_h)
# include "uci/type/PercentType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Indicates the confidence of the declaration of the Entity's Identity. */
      class SpecificIdentityConfidenceType : public virtual uci::type::SpecificIdentityType {
      public:

         /** The destructor */
         virtual ~SpecificIdentityConfidenceType()
         { }

         /** Returns this accessor's type constant, i.e. SpecificIdentityConfidenceType
           *
           * @return This accessor's type constant, i.e. SpecificIdentityConfidenceType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::specificIdentityConfidenceType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SpecificIdentityConfidenceType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Confidence.
           *
           * @return The value of the simple primitive data type identified by Confidence.
           */
         virtual uci::type::PercentTypeValue getConfidence() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Confidence.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setConfidence(uci::type::PercentTypeValue value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SpecificIdentityConfidenceType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SpecificIdentityConfidenceType to copy from
           */
         SpecificIdentityConfidenceType(const SpecificIdentityConfidenceType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SpecificIdentityConfidenceType to the contents of the
           * SpecificIdentityConfidenceType on the right hand side (rhs) of the assignment operator.SpecificIdentityConfidenceType
           * [only available to derived classes]
           *
           * @param rhs The SpecificIdentityConfidenceType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::SpecificIdentityConfidenceType
           * @return A constant reference to this SpecificIdentityConfidenceType.
           */
         const SpecificIdentityConfidenceType& operator=(const SpecificIdentityConfidenceType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SpecificIdentityConfidenceType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SpecificIdentityConfidenceType_h

