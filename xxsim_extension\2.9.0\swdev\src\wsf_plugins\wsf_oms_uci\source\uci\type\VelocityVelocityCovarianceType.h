// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__VelocityVelocityCovarianceType_h
#define Uci__Type__VelocityVelocityCovarianceType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__DoubleAccessor_h)
# include "uci/base/DoubleAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the VelocityVelocityCovarianceType sequence accessor class */
      class VelocityVelocityCovarianceType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~VelocityVelocityCovarianceType()
         { }

         /** Returns this accessor's type constant, i.e. VelocityVelocityCovarianceType
           *
           * @return This accessor's type constant, i.e. VelocityVelocityCovarianceType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::velocityVelocityCovarianceType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const VelocityVelocityCovarianceType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VnVn.
           *
           * @return The value of the simple primitive data type identified by VnVn.
           */
         virtual xs::Double getVnVn() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VnVn.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVnVn(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VnVe.
           *
           * @return The value of the simple primitive data type identified by VnVe.
           */
         virtual xs::Double getVnVe() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VnVe.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVnVe(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VnVd.
           *
           * @return The value of the simple primitive data type identified by VnVd.
           */
         virtual xs::Double getVnVd() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VnVd.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVnVd(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VnVd exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VnVd is emabled or not
           */
         virtual bool hasVnVd() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VnVd
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableVnVd(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VnVd */
         virtual void clearVnVd()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VeVe.
           *
           * @return The value of the simple primitive data type identified by VeVe.
           */
         virtual xs::Double getVeVe() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VeVe.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVeVe(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VeVd.
           *
           * @return The value of the simple primitive data type identified by VeVd.
           */
         virtual xs::Double getVeVd() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VeVd.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVeVd(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VeVd exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VeVd is emabled or not
           */
         virtual bool hasVeVd() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VeVd
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableVeVd(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VeVd */
         virtual void clearVeVd()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the VdVd.
           *
           * @return The value of the simple primitive data type identified by VdVd.
           */
         virtual xs::Double getVdVd() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the VdVd.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setVdVd(xs::Double value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by VdVd exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by VdVd is emabled or not
           */
         virtual bool hasVdVd() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by VdVd
           *
           * @param type = uci::base::accessorType::doubleAccessor This Accessor's accessor type
           */
         virtual void enableVdVd(uci::base::accessorType::AccessorType type = uci::base::accessorType::doubleAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by VdVd */
         virtual void clearVdVd()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         VelocityVelocityCovarianceType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The VelocityVelocityCovarianceType to copy from
           */
         VelocityVelocityCovarianceType(const VelocityVelocityCovarianceType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this VelocityVelocityCovarianceType to the contents of the
           * VelocityVelocityCovarianceType on the right hand side (rhs) of the assignment operator.VelocityVelocityCovarianceType
           * [only available to derived classes]
           *
           * @param rhs The VelocityVelocityCovarianceType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::VelocityVelocityCovarianceType
           * @return A constant reference to this VelocityVelocityCovarianceType.
           */
         const VelocityVelocityCovarianceType& operator=(const VelocityVelocityCovarianceType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // VelocityVelocityCovarianceType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__VelocityVelocityCovarianceType_h

