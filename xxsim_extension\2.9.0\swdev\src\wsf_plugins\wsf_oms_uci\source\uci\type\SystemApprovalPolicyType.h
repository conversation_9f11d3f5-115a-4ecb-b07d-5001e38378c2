// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SystemApprovalPolicyType_h
#define Uci__Type__SystemApprovalPolicyType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SystemApproverType_h)
# include "uci/type/SystemApproverType.h"
#endif

#if !defined(Uci__Type__ApprovalPolicyID_Type_h)
# include "uci/type/ApprovalPolicyID_Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SystemApprovalPolicyType sequence accessor class */
      class SystemApprovalPolicyType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SystemApprovalPolicyType()
         { }

         /** Returns this accessor's type constant, i.e. SystemApprovalPolicyType
           *
           * @return This accessor's type constant, i.e. SystemApprovalPolicyType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::systemApprovalPolicyType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SystemApprovalPolicyType& accessor)
            throw(uci::base::UCIException) = 0;


         /** A list of services that are allowed to approve the included approval policies and autonomy settings. [Maximum
           * occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SystemApproverType, uci::type::accessorType::systemApproverType> Approver;

         /** Returns the bounded list that is identified by the Approver.
           *
           * @return The bounded list identified by Approver.
           */
         virtual const uci::type::SystemApprovalPolicyType::Approver& getApprover() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Approver.
           *
           * @return The bounded list identified by Approver.
           */
         virtual uci::type::SystemApprovalPolicyType::Approver& getApprover()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Approver.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setApprover(const uci::type::SystemApprovalPolicyType::Approver& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the ApprovalPolicyID.
           *
           * @return The acecssor that provides access to the complex content that is identified by ApprovalPolicyID.
           */
         virtual const uci::type::ApprovalPolicyID_Type& getApprovalPolicyID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the ApprovalPolicyID.
           *
           * @return The acecssor that provides access to the complex content that is identified by ApprovalPolicyID.
           */
         virtual uci::type::ApprovalPolicyID_Type& getApprovalPolicyID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the ApprovalPolicyID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by ApprovalPolicyID
           */
         virtual void setApprovalPolicyID(const uci::type::ApprovalPolicyID_Type& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SystemApprovalPolicyType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SystemApprovalPolicyType to copy from
           */
         SystemApprovalPolicyType(const SystemApprovalPolicyType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SystemApprovalPolicyType to the contents of the
           * SystemApprovalPolicyType on the right hand side (rhs) of the assignment operator.SystemApprovalPolicyType [only
           * available to derived classes]
           *
           * @param rhs The SystemApprovalPolicyType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::SystemApprovalPolicyType
           * @return A constant reference to this SystemApprovalPolicyType.
           */
         const SystemApprovalPolicyType& operator=(const SystemApprovalPolicyType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SystemApprovalPolicyType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SystemApprovalPolicyType_h

