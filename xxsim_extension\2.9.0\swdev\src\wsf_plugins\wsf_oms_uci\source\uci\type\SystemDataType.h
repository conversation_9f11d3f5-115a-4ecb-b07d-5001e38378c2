// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SystemDataType_h
#define Uci__Type__SystemDataType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SystemStatusMDT_h)
# include "uci/type/SystemStatusMDT.h"
#endif

#if !defined(Uci__Type__PositionReportMDT_h)
# include "uci/type/PositionReportMDT.h"
#endif

#if !defined(Uci__Type__NavigationReportMDT_h)
# include "uci/type/NavigationReportMDT.h"
#endif

#if !defined(Uci__Type__SystemMetadataMDT_h)
# include "uci/type/SystemMetadataMDT.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** The complete data needed to represent a System. */
      class SystemDataType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SystemDataType()
         { }

         /** Returns this accessor's type constant, i.e. SystemDataType
           *
           * @return This accessor's type constant, i.e. SystemDataType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::systemDataType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SystemDataType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SystemStatus.
           *
           * @return The acecssor that provides access to the complex content that is identified by SystemStatus.
           */
         virtual const uci::type::SystemStatusMDT& getSystemStatus() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SystemStatus.
           *
           * @return The acecssor that provides access to the complex content that is identified by SystemStatus.
           */
         virtual uci::type::SystemStatusMDT& getSystemStatus()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SystemStatus to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SystemStatus
           */
         virtual void setSystemStatus(const uci::type::SystemStatusMDT& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Position.
           *
           * @return The acecssor that provides access to the complex content that is identified by Position.
           */
         virtual const uci::type::PositionReportMDT& getPosition() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Position.
           *
           * @return The acecssor that provides access to the complex content that is identified by Position.
           */
         virtual uci::type::PositionReportMDT& getPosition()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Position to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Position
           */
         virtual void setPosition(const uci::type::PositionReportMDT& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Position exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Position is emabled or not
           */
         virtual bool hasPosition() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Position
           *
           * @param type = uci::type::accessorType::positionReportMDT This Accessor's accessor type
           */
         virtual void enablePosition(uci::base::accessorType::AccessorType type = uci::type::accessorType::positionReportMDT)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Position */
         virtual void clearPosition()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Navigation.
           *
           * @return The acecssor that provides access to the complex content that is identified by Navigation.
           */
         virtual const uci::type::NavigationReportMDT& getNavigation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Navigation.
           *
           * @return The acecssor that provides access to the complex content that is identified by Navigation.
           */
         virtual uci::type::NavigationReportMDT& getNavigation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Navigation to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Navigation
           */
         virtual void setNavigation(const uci::type::NavigationReportMDT& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Navigation exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Navigation is emabled or not
           */
         virtual bool hasNavigation() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Navigation
           *
           * @param type = uci::type::accessorType::navigationReportMDT This Accessor's accessor type
           */
         virtual void enableNavigation(uci::base::accessorType::AccessorType type = uci::type::accessorType::navigationReportMDT)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Navigation */
         virtual void clearNavigation()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Metadata.
           *
           * @return The acecssor that provides access to the complex content that is identified by Metadata.
           */
         virtual const uci::type::SystemMetadataMDT& getMetadata() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Metadata.
           *
           * @return The acecssor that provides access to the complex content that is identified by Metadata.
           */
         virtual uci::type::SystemMetadataMDT& getMetadata()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Metadata to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Metadata
           */
         virtual void setMetadata(const uci::type::SystemMetadataMDT& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Metadata exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Metadata is emabled or not
           */
         virtual bool hasMetadata() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Metadata
           *
           * @param type = uci::type::accessorType::systemMetadataMDT This Accessor's accessor type
           */
         virtual void enableMetadata(uci::base::accessorType::AccessorType type = uci::type::accessorType::systemMetadataMDT)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Metadata */
         virtual void clearMetadata()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SystemDataType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SystemDataType to copy from
           */
         SystemDataType(const SystemDataType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SystemDataType to the contents of the SystemDataType on the right
           * hand side (rhs) of the assignment operator.SystemDataType [only available to derived classes]
           *
           * @param rhs The SystemDataType on the right hand side (rhs) of the assignment operator whose contents are used to set
           *      the contents of this uci::type::SystemDataType
           * @return A constant reference to this SystemDataType.
           */
         const SystemDataType& operator=(const SystemDataType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SystemDataType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SystemDataType_h

