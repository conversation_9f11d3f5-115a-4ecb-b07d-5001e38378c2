// ****************************************************************************
// CUI
//
// The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
//
// Copyright 2016 Infoscitex, a DCS Company. All rights reserved.
//
// The use, dissemination or disclosure of data in this file is subject to
// limitation or restriction. See accompanying README and LICENSE for details.
// ****************************************************************************
#ifndef WKFNETWORKTOOLBAR_HPP
#define WKFNETWORKTOOLBAR_HPP

#include <QToolBar>
#include <QLineEdit>
#include <QSpinBox>
#include <QLabel>
#include <QPushButton>
#include <QHBoxLayout>
#include <QWidget>
#include <QFrame>

#include "wkf_export.h"

namespace wkf
{

class WKF_EXPORT NetworkToolbar : public QToolBar
{
    Q_OBJECT

public:
    explicit NetworkToolbar(QWidget* parent = nullptr);
    ~NetworkToolbar() override = default;

    // 获取当前设置的IP和端口
    QString GetIpAddress() const;
    int GetPort() const;
    
    // 设置IP和端口（从配置文件读取时使用）
    void SetIpAddress(const QString& ip);
    void SetPort(int port);
    
    // 设置连接状态显示
    void SetConnectionStatus(bool connected);

signals:
    // 当用户点击连接按钮时发射
    void ConnectRequested(const QString& ip, int port);
    // 当用户点击断开按钮时发射
    void DisconnectRequested();
    // 当IP或端口改变时发射
    void NetworkConfigChanged(const QString& ip, int port);

private slots:
    void OnConnectClicked();
    void OnDisconnectClicked();
    void OnIpChanged();
    void OnPortChanged();

private:
    void CreateWidgets();
    void UpdateConnectionUI(bool connected);

    // UI组件
    QWidget*     mContainerWidget;
    QLabel*      mIpLabel;
    QLineEdit*   mIpLineEdit;
    QLabel*      mPortLabel;
    QSpinBox*    mPortSpinBox;
    QPushButton* mConnectButton;
    QPushButton* mDisconnectButton;
    QLabel*      mStatusLabel;
    
    // 状态
    bool mIsConnected;
};

} // namespace wkf

#endif // WKFNETWORKTOOLBAR_HPP
