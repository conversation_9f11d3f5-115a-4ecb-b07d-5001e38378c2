
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>mark_as_advanced &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="math" href="math.html" />
    <link rel="prev" title="macro" href="macro.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="math.html" title="math"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="macro.html" title="macro"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">mark_as_advanced</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="mark-as-advanced">
<span id="command:mark_as_advanced"></span><h1>mark_as_advanced<a class="headerlink" href="#mark-as-advanced" title="Permalink to this heading">¶</a></h1>
<p>Mark cmake cached variables as advanced.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">mark_as_advanced(</span><span class="p">[</span><span class="no">CLEAR</span><span class="p">|</span><span class="no">FORCE</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;var1&gt;</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>Sets the advanced/non-advanced state of the named
cached variables.</p>
<p>An advanced variable will not be displayed in any
of the cmake GUIs unless the <code class="docutils literal notranslate"><span class="pre">show</span> <span class="pre">advanced</span></code> option is on.
In script mode, the advanced/non-advanced state has no effect.</p>
<p>If the keyword <code class="docutils literal notranslate"><span class="pre">CLEAR</span></code> is given
then advanced variables are changed back to unadvanced.
If the keyword <code class="docutils literal notranslate"><span class="pre">FORCE</span></code> is given
then the variables are made advanced.
If neither <code class="docutils literal notranslate"><span class="pre">FORCE</span></code> nor <code class="docutils literal notranslate"><span class="pre">CLEAR</span></code> is specified,
new values will be marked as advanced, but if a
variable already has an advanced/non-advanced state,
it will not be changed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.17: </span>Variables passed to this command which are not already in the cache
are ignored. See policy <span class="target" id="index-0-policy:CMP0102"></span><a class="reference internal" href="../policy/CMP0102.html#policy:CMP0102" title="CMP0102"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0102</span></code></a>.</p>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="macro.html"
                          title="previous chapter">macro</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="math.html"
                          title="next chapter">math</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/mark_as_advanced.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="math.html" title="math"
             >next</a> |</li>
        <li class="right" >
          <a href="macro.html" title="macro"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">mark_as_advanced</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>