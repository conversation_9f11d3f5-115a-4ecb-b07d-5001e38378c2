
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>return &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="separate_arguments" href="separate_arguments.html" />
    <link rel="prev" title="option" href="option.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="separate_arguments.html" title="separate_arguments"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="option.html" title="option"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">return</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="return">
<span id="command:return"></span><h1>return<a class="headerlink" href="#return" title="Permalink to this heading">¶</a></h1>
<p>Return from a file, directory or function.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">return(</span><span class="p">[</span><span class="no">PROPAGATE</span><span class="w"> </span><span class="nv">&lt;var-name&gt;...</span><span class="p">]</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>When this command is encountered in an included file (via <span class="target" id="index-0-command:include"></span><a class="reference internal" href="include.html#command:include" title="include"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">include()</span></code></a> or
<span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a>), it causes processing of the current file to stop
and control is returned to the including file.  If it is encountered in a
file which is not included by another file, e.g. a <code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code>,
deferred calls scheduled by <span class="target" id="index-0-command:cmake_language"></span><a class="reference internal" href="cmake_language.html#command:cmake_language" title="cmake_language"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_language(DEFER)</span></code></a> are invoked and
control is returned to the parent directory if there is one.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">return()</span></code> is called in a function, control is returned to the caller
of that function.  Note that a <span class="target" id="index-0-command:macro"></span><a class="reference internal" href="macro.html#command:macro" title="macro"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">macro()</span></code></a>, unlike a <span class="target" id="index-0-command:function"></span><a class="reference internal" href="function.html#command:function" title="function"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">function()</span></code></a>,
is expanded in place and therefore cannot handle <code class="docutils literal notranslate"><span class="pre">return()</span></code>.</p>
<p>Policy <span class="target" id="index-0-policy:CMP0140"></span><a class="reference internal" href="../policy/CMP0140.html#policy:CMP0140" title="CMP0140"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0140</span></code></a> controls the behavior regarding the arguments of the
command.  All arguments are ignored unless that policy is set to <code class="docutils literal notranslate"><span class="pre">NEW</span></code>.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">PROPAGATE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>This option sets or unsets the specified variables in the parent directory or
function caller scope. This is equivalent to <span class="target" id="index-0-command:set"></span><a class="reference internal" href="set.html#command:set" title="set"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set(PARENT_SCOPE)</span></code></a> or
<span class="target" id="index-0-command:unset"></span><a class="reference internal" href="unset.html#command:unset" title="unset"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">unset(PARENT_SCOPE)</span></code></a> commands, except for the way it interacts
with the <span class="target" id="index-0-command:block"></span><a class="reference internal" href="block.html#command:block" title="block"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">block()</span></code></a> command, as described below.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">PROPAGATE</span></code> option can be very useful in conjunction with the
<span class="target" id="index-1-command:block"></span><a class="reference internal" href="block.html#command:block" title="block"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">block()</span></code></a> command.  A <code class="docutils literal notranslate"><span class="pre">return</span></code> will propagate the
specified variables through any enclosing block scopes created by the
<span class="target" id="index-2-command:block"></span><a class="reference internal" href="block.html#command:block" title="block"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">block()</span></code></a> commands.  Inside a function, this ensures the variables
are propagated to the function's caller, regardless of any blocks within
the function.  If not inside a function, it ensures the variables are
propagated to the parent file or directory scope. For example:</p>
<div class="literal-block-wrapper docutils container" id="id1">
<div class="code-block-caption"><span class="caption-text">CMakeLists.txt</span><a class="headerlink" href="#id1" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_version_required(</span><span class="no">VERSION</span><span class="w"> </span><span class="m">3.25</span><span class="nf">)</span><span class="w"></span>
<span class="nf">project(</span><span class="nb">example</span><span class="nf">)</span><span class="w"></span>

<span class="nf">set(</span><span class="nb">var1</span><span class="w"> </span><span class="s">&quot;top-value&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="nf">block(</span><span class="no">SCOPE_FOR</span><span class="w"> </span><span class="no">VARIABLES</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">add_subdirectory(</span><span class="nb">subDir</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="c"># var1 has the value &quot;block-nested&quot;</span>
<span class="nf">endblock()</span><span class="w"></span>

<span class="c"># var1 has the value &quot;top-value&quot;</span>
</pre></div>
</div>
</div>
<div class="literal-block-wrapper docutils container" id="id2">
<div class="code-block-caption"><span class="caption-text">subDir/CMakeLists.txt</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">function(</span><span class="nb">multi_scopes</span><span class="w"> </span><span class="nb">result_var1</span><span class="w"> </span><span class="nb">result_var2</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">block(</span><span class="no">SCOPE_FOR</span><span class="w"> </span><span class="no">VARIABLES</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="c"># This would only propagate out of the immediate block, not to</span>
<span class="w">    </span><span class="c"># the caller of the function.</span>
<span class="w">    </span><span class="c">#set(${result_var1} &quot;new-value&quot; PARENT_SCOPE)</span>
<span class="w">    </span><span class="c">#unset(${result_var2} PARENT_SCOPE)</span>

<span class="w">    </span><span class="c"># This propagates the variables through the enclosing block and</span>
<span class="w">    </span><span class="c"># out to the caller of the function.</span>
<span class="w">    </span><span class="nf">set(</span><span class="o">${</span><span class="nt">result_var1</span><span class="o">}</span><span class="w"> </span><span class="s">&quot;new-value&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">unset(</span><span class="o">${</span><span class="nt">result_var2</span><span class="o">}</span><span class="nf">)</span><span class="w"></span>
<span class="w">    </span><span class="nf">return(</span><span class="no">PROPAGATE</span><span class="w"> </span><span class="o">${</span><span class="nt">result_var1</span><span class="o">}</span><span class="w"> </span><span class="o">${</span><span class="nt">result_var2</span><span class="o">}</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">endblock()</span><span class="w"></span>
<span class="nf">endfunction()</span><span class="w"></span>

<span class="nf">set(</span><span class="nb">var1</span><span class="w"> </span><span class="s">&quot;some-value&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="nf">set(</span><span class="nb">var2</span><span class="w"> </span><span class="s">&quot;another-value&quot;</span><span class="nf">)</span><span class="w"></span>

<span class="nf">multi_scopes(</span><span class="nb">var1</span><span class="w"> </span><span class="nb">var2</span><span class="nf">)</span><span class="w"></span>
<span class="c"># Now var1 will hold &quot;new-value&quot; and var2 will be unset</span>

<span class="nf">block(</span><span class="no">SCOPE_FOR</span><span class="w"> </span><span class="no">VARIABLES</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="c"># This return() will set var1 in the directory scope that included us</span>
<span class="w">  </span><span class="c"># via add_subdirectory(). The surrounding block() here does not limit</span>
<span class="w">  </span><span class="c"># propagation to the current file, but the block() in the parent</span>
<span class="w">  </span><span class="c"># directory scope does prevent propagation going any further.</span>
<span class="w">  </span><span class="nf">set(</span><span class="nb">var1</span><span class="w"> </span><span class="s">&quot;block-nested&quot;</span><span class="nf">)</span><span class="w"></span>
<span class="w">  </span><span class="nf">return(</span><span class="no">PROPAGATE</span><span class="w"> </span><span class="nb">var1</span><span class="nf">)</span><span class="w"></span>
<span class="nf">endblock()</span><span class="w"></span>
</pre></div>
</div>
</div>
</dd>
</dl>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-3-command:block"></span><a class="reference internal" href="block.html#command:block" title="block"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">block()</span></code></a></p></li>
<li><p><span class="target" id="index-1-command:function"></span><a class="reference internal" href="function.html#command:function" title="function"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">function()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">return</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="option.html"
                          title="previous chapter">option</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="separate_arguments.html"
                          title="next chapter">separate_arguments</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/return.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="separate_arguments.html" title="separate_arguments"
             >next</a> |</li>
        <li class="right" >
          <a href="option.html" title="option"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">return</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>