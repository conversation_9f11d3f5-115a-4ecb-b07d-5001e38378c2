// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__TaskAllocationParametersType_h
#define Uci__Type__TaskAllocationParametersType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__RankingType_h)
# include "uci/type/RankingType.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__TaskAllocationConstraintType_h)
# include "uci/type/TaskAllocationConstraintType.h"
#endif

#if !defined(Uci__Base__BooleanAccessor_h)
# include "uci/base/BooleanAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the TaskAllocationParametersType sequence accessor class */
      class TaskAllocationParametersType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~TaskAllocationParametersType()
         { }

         /** Returns this accessor's type constant, i.e. TaskAllocationParametersType
           *
           * @return This accessor's type constant, i.e. TaskAllocationParametersType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::taskAllocationParametersType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const TaskAllocationParametersType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates the System and optionally Capability that this task MUST be allocated to during the allocation process. If
           * multiple required Systems are given: 1) allocation to any one of the Systems is the minimum expectation, 2)
           * decomposition of the Task into multiple child Tasks allocated across the multiple required Systems is acceptable, 3)
           * there is no expectation that each required System must perform the Task. This element allows the Task creator to
           * force an allocation to a specific System at the risk of 1) a less than optimal overall solution or 2) failure of the
           * task being allocated despite availability of another suitable system. [Minimum occurrences: 0] [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::TaskAllocationConstraintType, uci::type::accessorType::taskAllocationConstraintType> RequiredAllocation;

         /** Indicates the System and optionally Capability that this task should be allocated to during the allocation process.
           * However, allocation to another System is acceptable. If multiple preferred Systems are given: 1) allocation to any
           * one of the Systems is the minimum expectation, 2) decomposition of the Task into multiple child Tasks allocated
           * across the multiple preferred Systems is acceptable, 3) there is no expectation that each preferred System must
           * perform the Task. This element allows the Task creator to suggest an allocation to a specific System at the risk of a
           * less than optimal overall solution. [Minimum occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::TaskAllocationConstraintType, uci::type::accessorType::taskAllocationConstraintType> PreferredAllocation;

         /** Returns the accessor that provides access to the complex content that is identified by the Ranking.
           *
           * @return The acecssor that provides access to the complex content that is identified by Ranking.
           */
         virtual const uci::type::RankingType& getRanking() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Ranking.
           *
           * @return The acecssor that provides access to the complex content that is identified by Ranking.
           */
         virtual uci::type::RankingType& getRanking()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Ranking to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Ranking
           */
         virtual void setRanking(const uci::type::RankingType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Ranking exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Ranking is emabled or not
           */
         virtual bool hasRanking() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Ranking
           *
           * @param type = uci::type::accessorType::rankingType This Accessor's accessor type
           */
         virtual void enableRanking(uci::base::accessorType::AccessorType type = uci::type::accessorType::rankingType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Ranking */
         virtual void clearRanking()
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the RequiredAllocation.
           *
           * @return The bounded list identified by RequiredAllocation.
           */
         virtual const uci::type::TaskAllocationParametersType::RequiredAllocation& getRequiredAllocation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the RequiredAllocation.
           *
           * @return The bounded list identified by RequiredAllocation.
           */
         virtual uci::type::TaskAllocationParametersType::RequiredAllocation& getRequiredAllocation()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the RequiredAllocation.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setRequiredAllocation(const uci::type::TaskAllocationParametersType::RequiredAllocation& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the PreferredAllocation.
           *
           * @return The bounded list identified by PreferredAllocation.
           */
         virtual const uci::type::TaskAllocationParametersType::PreferredAllocation& getPreferredAllocation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the PreferredAllocation.
           *
           * @return The bounded list identified by PreferredAllocation.
           */
         virtual uci::type::TaskAllocationParametersType::PreferredAllocation& getPreferredAllocation()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the PreferredAllocation.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setPreferredAllocation(const uci::type::TaskAllocationParametersType::PreferredAllocation& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the CommsRequired.
           *
           * @return The value of the simple primitive data type identified by CommsRequired.
           */
         virtual xs::Boolean getCommsRequired() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the CommsRequired.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setCommsRequired(xs::Boolean value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by CommsRequired exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by CommsRequired is emabled or not
           */
         virtual bool hasCommsRequired() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by CommsRequired
           *
           * @param type = uci::base::accessorType::booleanAccessor This Accessor's accessor type
           */
         virtual void enableCommsRequired(uci::base::accessorType::AccessorType type = uci::base::accessorType::booleanAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by CommsRequired */
         virtual void clearCommsRequired()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         TaskAllocationParametersType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The TaskAllocationParametersType to copy from
           */
         TaskAllocationParametersType(const TaskAllocationParametersType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this TaskAllocationParametersType to the contents of the
           * TaskAllocationParametersType on the right hand side (rhs) of the assignment operator.TaskAllocationParametersType
           * [only available to derived classes]
           *
           * @param rhs The TaskAllocationParametersType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::TaskAllocationParametersType
           * @return A constant reference to this TaskAllocationParametersType.
           */
         const TaskAllocationParametersType& operator=(const TaskAllocationParametersType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // TaskAllocationParametersType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__TaskAllocationParametersType_h

