// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__VideoAugmentationParametersType_h
#define Uci__Type__VideoAugmentationParametersType_h 1

#if !defined(Uci__Type__AugmentationProcessingParametersType_h)
# include "uci/type/AugmentationProcessingParametersType.h"
#endif

#if !defined(Uci__Type__VisibleString1024Type_h)
# include "uci/type/VisibleString1024Type.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This type extends AugmentationProcessingParametersType to provide augmentation parameters specific to video
        * processing.
        */
      class VideoAugmentationParametersType : public virtual uci::type::AugmentationProcessingParametersType {
      public:

         /** The destructor */
         virtual ~VideoAugmentationParametersType()
         { }

         /** Returns this accessor's type constant, i.e. VideoAugmentationParametersType
           *
           * @return This accessor's type constant, i.e. VideoAugmentationParametersType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::videoAugmentationParametersType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const VideoAugmentationParametersType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the KeySet.
           *
           * @return The value of the string data type identified by KeySet.
           */
         virtual const uci::type::VisibleString1024Type& getKeySet() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the KeySet.
           *
           * @return The value of the string data type identified by KeySet.
           */
         virtual uci::type::VisibleString1024Type& getKeySet()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the KeySet to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setKeySet(const uci::type::VisibleString1024Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the KeySet to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setKeySet(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the KeySet to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setKeySet(const char* value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         VideoAugmentationParametersType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The VideoAugmentationParametersType to copy from
           */
         VideoAugmentationParametersType(const VideoAugmentationParametersType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this VideoAugmentationParametersType to the contents of the
           * VideoAugmentationParametersType on the right hand side (rhs) of the assignment
           * operator.VideoAugmentationParametersType [only available to derived classes]
           *
           * @param rhs The VideoAugmentationParametersType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::VideoAugmentationParametersType
           * @return A constant reference to this VideoAugmentationParametersType.
           */
         const VideoAugmentationParametersType& operator=(const VideoAugmentationParametersType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // VideoAugmentationParametersType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__VideoAugmentationParametersType_h

