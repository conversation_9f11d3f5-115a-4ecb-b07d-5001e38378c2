# ****************************************************************************
# CUI
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************
# common file for selecting which messages appear in the event log

event_output
   file replay.evt
   time_format h:m:s.1
   lat_lon_format d:m:s.2
   print_track_in_message true

   disable TEAM_NAME_DEFINITION

   #enable COMM_TURNED_OFF
   #enable COMM_TURNED_ON
   enable JAMMING_ATTEMPT
   enable JAMMING_REQUEST_CANCELED
   enable JAMMING_REQUEST_INITIATED
   enable JAMMING_REQUEST_UPDATED
   #enable LOCAL_TRACK_CORRELATION
   #enable LOCAL_TRACK_DECORRELATION
   #enable LOCAL_TRACK_INITIATED
   #enable LOCAL_TRACK_UPDATED
   #enable LOCAL_TRACK_DROPPED
   #enable MESSAGE_DISCARDED
   #enable MESSAGE_UPDATED
   #enable MESSAGE_QUEUED
   #enable MESSAGE_RECEIVED
   #enable MESSAGE_TRANSMITTED
   #enable OPERATING_LEVEL_CHANGED
   #enable PLATFORM_ADDED
   #enable PLATFORM_DELETED
   #enable PLATFORM_BROKEN
   #enable PROCESSOR_TURNED_OFF
   #enable PROCESSOR_TURNED_ON
   enable SENSOR_DETECTION_ATTEMPT
   #enable SENSOR_FREQUENCY_CHANGED
   #enable SENSOR_MODE_ACTIVATED
   #enable SENSOR_MODE_DEACTIVATED
   #enable SENSOR_REQUEST_CANCELED
   #enable SENSOR_REQUEST_INITIATED
   #enable SENSOR_REQUEST_UPDATED
   #enable SENSOR_TRACK_INITIATED
   #enable SENSOR_TRACK_UPDATED
   #enable SENSOR_TRACK_DROPPED
   #enable SENSOR_TURNED_ON
   #enable SENSOR_TURNED_OFF
   #enable SIMULATION_STARTING
   #enable SIMULATION_COMPLETE
   #enable TASK_ASSIGNED
   #enable TASK_CANCELED
   #enable TASK_COMPLETED
   #enable WEAPON_FIRE_ABORTED
   #enable WEAPON_FIRE_REQUESTED
   #enable WEAPON_FIRED
   #enable WEAPON_HIT
   #enable WEAPON_MISSED
   #enable WEAPON_RELOAD_STARTED
   #enable WEAPON_RELOAD_ENDED
   #enable WEAPON_TERMINATED
   #enable WEAPON_TURNED_OFF
   #enable WEAPON_TURNED_ON
end_event_output
