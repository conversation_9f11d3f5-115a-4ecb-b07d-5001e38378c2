
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <title>variable_requires &mdash; CMake 3.26.0-rc1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    
    <link rel="shortcut icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="write_file" href="write_file.html" />
    <link rel="prev" title="utility_source" href="utility_source.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="write_file.html" title="write_file"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="utility_source.html" title="utility_source"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">variable_requires</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="variable-requires">
<span id="command:variable_requires"></span><h1>variable_requires<a class="headerlink" href="#variable-requires" title="Permalink to this heading">¶</a></h1>
<p>Disallowed since version 3.0.  See CMake Policy <span class="target" id="index-0-policy:CMP0035"></span><a class="reference internal" href="../policy/CMP0035.html#policy:CMP0035" title="CMP0035"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0035</span></code></a>.</p>
<p>Use the <span class="target" id="index-0-command:if"></span><a class="reference internal" href="if.html#command:if" title="if"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">if()</span></code></a> command instead.</p>
<p>Assert satisfaction of an option's required variables.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">variable_requires(</span><span class="no">TEST_VARIABLE</span><span class="w"> </span><span class="no">RESULT_VARIABLE</span><span class="w"></span>
<span class="w">                  </span><span class="no">REQUIRED_VARIABLE1</span><span class="w"></span>
<span class="w">                  </span><span class="no">REQUIRED_VARIABLE2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span><span class="w"></span>
</pre></div>
</div>
<p>The first argument (<code class="docutils literal notranslate"><span class="pre">TEST_VARIABLE</span></code>) is the name of the variable to be
tested, if that variable is false nothing else is done.  If
<code class="docutils literal notranslate"><span class="pre">TEST_VARIABLE</span></code> is true, then the next argument (<code class="docutils literal notranslate"><span class="pre">RESULT_VARIABLE</span></code>)
is a variable that is set to true if all the required variables are set.
The rest of the arguments are variables that must be true or not set
to <code class="docutils literal notranslate"><span class="pre">NOTFOUND</span></code> to avoid an error.  If any are not true, an error is
reported.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="utility_source.html"
                          title="previous chapter">utility_source</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="write_file.html"
                          title="next chapter">write_file</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/variable_requires.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="write_file.html" title="write_file"
             >next</a> |</li>
        <li class="right" >
          <a href="utility_source.html" title="utility_source"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.26.0-rc1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">variable_requires</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>