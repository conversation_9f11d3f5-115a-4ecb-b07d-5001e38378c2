// ****************************************************************************
// CUI
//
// The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
//
// Copyright 2016 Infoscitex, a DCS Company. All rights reserved.
//
// The use, dissemination or disclosure of data in this file is subject to
// limitation or restriction. See accompanying README and LICENSE for details.
// ****************************************************************************
#include "WkfNetworkToolbar.hpp"

#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGroupBox>
#include <QValidator>
#include <QRegularExpressionValidator>
#include <QRegularExpression>
#include <QFile>
#include <QTextStream>
#include <QStringList>

wkf::NetworkToolbar::NetworkToolbar(QWidget* parent)
    : QToolBar("net config", parent)
    , mContainerWidget(nullptr)
    , mIpLabel(nullptr)
    , mIpLineEdit(nullptr)
    , mPortLabel(nullptr)
    , mPortSpinBox(nullptr)
    , mConnectButton(nullptr)
    , mDisconnectButton(nullptr)
    , mStatusLabel(nullptr)
    // Temporarily comment out mIsConnected initialization for debugging
    // , mIsConnected(false)
{
    setObjectName("NetworkToolbar");
    setIconSize(QSize(16, 16));
    setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    CreateWidgets();
    // Temporarily comment out for debugging
    // UpdateConnectionUI(false);
}

void wkf::NetworkToolbar::CreateWidgets()
{
    // Create container widget
    mContainerWidget = new QWidget(this);
    mContainerWidget->setStyleSheet(
        "QWidget { "
        "background-color: #3c3c3c; "
        "border: none; "
        "margin: 0px; "
        "padding: 2px; "
        "}"
    );

    // Create main layout
    auto* mainLayout = new QHBoxLayout(mContainerWidget);
    mainLayout->setSpacing(8);
    mainLayout->setContentsMargins(8, 4, 8, 4);

    // IP address input
    mIpLabel = new QLabel("IP:", mContainerWidget);
    mIpLabel->setStyleSheet("QLabel { border: none; background: transparent; font-weight: bold; color: white; }");
    
    mIpLineEdit = new QLineEdit(mContainerWidget);
    mIpLineEdit->setPlaceholderText("*************");
    mIpLineEdit->setMinimumWidth(120);
    mIpLineEdit->setMaximumWidth(150);
    mIpLineEdit->setStyleSheet("QLineEdit { border: 1px solid #555555; padding: 2px; background: #f0f0f0; color: #333333; }");

    // IP address validator
    QRegularExpression ipRegex("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    mIpLineEdit->setValidator(new QRegularExpressionValidator(ipRegex, mIpLineEdit));

    // Port input
    mPortLabel = new QLabel("Port:", mContainerWidget);
    mPortLabel->setStyleSheet("QLabel { border: none; background: transparent; font-weight: bold; color: white; }");
    
    mPortSpinBox = new QSpinBox(mContainerWidget);
    mPortSpinBox->setRange(1, 65535);
    mPortSpinBox->setValue(8080);
    mPortSpinBox->setMinimumWidth(80);
    mPortSpinBox->setMaximumWidth(100);
    mPortSpinBox->setStyleSheet(
        "QSpinBox { border: 1px solid #555555; padding: 2px; background: #f0f0f0; color: #333333; } "
        "QSpinBox::up-button { width: 0px; } "
        "QSpinBox::down-button { width: 0px; }"
    );

    // Connect button
    mConnectButton = new QPushButton("modify", mContainerWidget);
    mConnectButton->setStyleSheet(
        "QPushButton { "
        "background-color: #4CAF50; "
        "color: white; "
        "border: none; "
        "padding: 4px 12px; "
        "border-radius: 3px; "
        "font-weight: bold; "
        "} "
        "QPushButton:hover { background-color: #45a049; } "
        "QPushButton:pressed { background-color: #3d8b40; }"
    );

    // // Disconnect button
    // mDisconnectButton = new QPushButton("Disconnect", mContainerWidget);
    // mDisconnectButton->setStyleSheet(
    //     "QPushButton { "
    //     "background-color: #f44336; "
    //     "color: white; "
    //     "border: none; "
    //     "padding: 4px 12px; "
    //     "border-radius: 3px; "
    //     "font-weight: bold; "
    //     "} "
    //     "QPushButton:hover { background-color: #da190b; } "
    //     "QPushButton:pressed { background-color: #c1170a; }"
    // );

    // Status label
    // mStatusLabel = new QLabel("Not Connected", mContainerWidget);
    // mStatusLabel->setStyleSheet(
    //     "QLabel { "
    //     "border: none; "
    //     "background: transparent; "
    //     "color: #666; "
    //     "font-weight: bold; "
    //     "padding: 4px; "
    //     "}"
    // );

    // Add to layout
    mainLayout->addWidget(mIpLabel);
    mainLayout->addWidget(mIpLineEdit);
    mainLayout->addWidget(mPortLabel);
    mainLayout->addWidget(mPortSpinBox);
    mainLayout->addWidget(mConnectButton);
    //mainLayout->addWidget(mDisconnectButton);
    //mainLayout->addWidget(mStatusLabel);
    mainLayout->addStretch(); // Add flexible space

    // Add container to toolbar
    addWidget(mContainerWidget);

    // Connect signals
    connect(mConnectButton, SIGNAL(clicked()), this, SLOT(OnConnectClicked()));
    connect(mDisconnectButton, SIGNAL(clicked()), this, SLOT(OnDisconnectClicked()));
    connect(mIpLineEdit, SIGNAL(textChanged(const QString&)), this, SLOT(OnIpChanged()));
    connect(mPortSpinBox, SIGNAL(valueChanged(int)), this, SLOT(OnPortChanged()));
}

QString wkf::NetworkToolbar::GetIpAddress() const
{
    return mIpLineEdit ? mIpLineEdit->text() : QString();
}

int wkf::NetworkToolbar::GetPort() const
{
    return mPortSpinBox ? mPortSpinBox->value() : 0;
}

void wkf::NetworkToolbar::SetIpAddress(const QString& ip)
{
    if (mIpLineEdit)
    {
        mIpLineEdit->setText(ip);
    }
}

void wkf::NetworkToolbar::SetPort(int port)
{
    if (mPortSpinBox)
    {
        mPortSpinBox->setValue(port);
    }
}

// These methods temporarily removed for debugging compilation issues

void wkf::NetworkToolbar::OnConnectClicked()
{
    QString configPath = "config/dis_config.txt";
    QString ipAddress = GetIpAddress();
    int port = GetPort();

    // Read the configuration file
    QFile file(configPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        // If file doesn't exist, create a basic one
        if (file.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QTextStream out(&file);
            out << "dis_interface\n";
            out << "  unicast " << ipAddress << "\n";
            out << "  send_port " << port << "\n";
            out << "  autostart\n";
            out << "end_dis_interface\n";
            file.close();
        }
        emit ConnectRequested(ipAddress, port);
        return;
    }

    // Read all lines
    QTextStream in(&file);
    QStringList lines;
    while (!in.atEnd())
    {
        lines.append(in.readLine());
    }
    file.close();

    // Modify the lines
    bool unicastFound = false;
    bool sendPortFound = false;

    for (int i = 0; i < lines.size(); ++i)
    {
        QString line = lines[i].trimmed();

        // Update unicast line
        if (line.startsWith("unicast"))
        {
            lines[i] = QString("  unicast %1").arg(ipAddress);
            unicastFound = true;
        }
        // Update send_port line
        else if (line.startsWith("send_port"))
        {
            lines[i] = QString("  send_port %1").arg(port);
            sendPortFound = true;
        }
    }

    // If unicast or send_port not found, add them
    if (!unicastFound || !sendPortFound)
    {
        // Find dis_interface section to add missing entries
        for (int i = 0; i < lines.size(); ++i)
        {
            if (lines[i].trimmed() == "dis_interface")
            {
                if (!unicastFound)
                {
                    lines.insert(i + 1, QString("  unicast %1").arg(ipAddress));
                    i++;
                }
                if (!sendPortFound)
                {
                    lines.insert(i + 1, QString("  send_port %1").arg(port));
                }
                break;
            }
        }
    }

    // Write back to file
    if (file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QTextStream out(&file);
        for (const QString& line : lines)
        {
            out << line << "\n";
        }
        file.close();
    }

    //emit ConnectRequested(ipAddress, port);
}

void wkf::NetworkToolbar::OnDisconnectClicked()
{
    emit DisconnectRequested();
}

void wkf::NetworkToolbar::OnIpChanged()
{
    emit NetworkConfigChanged(GetIpAddress(), GetPort());
}

void wkf::NetworkToolbar::OnPortChanged()
{
    emit NetworkConfigChanged(GetIpAddress(), GetPort());
}
