// ****************************************************************************
// CUI
//
// The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
//
// Copyright 2016 Infoscitex, a DCS Company. All rights reserved.
//
// The use, dissemination or disclosure of data in this file is subject to
// limitation or restriction. See accompanying README and LICENSE for details.
// ****************************************************************************
#include "WkfNetworkToolbar.hpp"

#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGroupBox>
#include <QValidator>
#include <QRegularExpressionValidator>
#include <QRegularExpression>

wkf::NetworkToolbar::NetworkToolbar(QWidget* parent)
    : QToolBar("网络配置", parent)
    , mContainerWidget(nullptr)
    , mIpLabel(nullptr)
    , mIpLineEdit(nullptr)
    , mPortLabel(nullptr)
    , mPortSpinBox(nullptr)
    , mConnectButton(nullptr)
    , mDisconnectButton(nullptr)
    , mStatusLabel(nullptr)
    , mIsConnected(false)
{
    setObjectName("NetworkToolbar");
    setIconSize(QSize(16, 16));
    setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    
    CreateWidgets();
    UpdateConnectionUI(false);
}

void wkf::NetworkToolbar::CreateWidgets()
{
    // 创建容器widget
    mContainerWidget = new QWidget(this);
    mContainerWidget->setStyleSheet(
        "QWidget { "
        "background-color: #f8f8f8; "
        "border: 1px solid #d0d0d0; "
        "border-radius: 4px; "
        "margin: 2px; "
        "padding: 4px; "
        "}"
    );
    
    // 创建主布局
    auto* mainLayout = new QHBoxLayout(mContainerWidget);
    mainLayout->setSpacing(8);
    mainLayout->setContentsMargins(8, 4, 8, 4);
    
    // IP地址输入
    mIpLabel = new QLabel("DCS IP:", mContainerWidget);
    mIpLabel->setStyleSheet("QLabel { border: none; background: transparent; font-weight: bold; }");
    
    mIpLineEdit = new QLineEdit(mContainerWidget);
    mIpLineEdit->setPlaceholderText("*************");
    mIpLineEdit->setMinimumWidth(120);
    mIpLineEdit->setMaximumWidth(150);
    
    // IP地址验证器
    QRegularExpression ipRegex("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    mIpLineEdit->setValidator(new QRegularExpressionValidator(ipRegex, mIpLineEdit));
    
    // 端口输入
    mPortLabel = new QLabel("端口:", mContainerWidget);
    mPortLabel->setStyleSheet("QLabel { border: none; background: transparent; font-weight: bold; }");
    
    mPortSpinBox = new QSpinBox(mContainerWidget);
    mPortSpinBox->setRange(1, 65535);
    mPortSpinBox->setValue(8080);
    mPortSpinBox->setMinimumWidth(80);
    mPortSpinBox->setMaximumWidth(100);
    
    // 连接按钮
    mConnectButton = new QPushButton("连接", mContainerWidget);
    mConnectButton->setStyleSheet(
        "QPushButton { "
        "background-color: #4CAF50; "
        "color: white; "
        "border: none; "
        "padding: 4px 12px; "
        "border-radius: 3px; "
        "font-weight: bold; "
        "} "
        "QPushButton:hover { background-color: #45a049; } "
        "QPushButton:pressed { background-color: #3d8b40; }"
    );
    
    // 断开按钮
    mDisconnectButton = new QPushButton("断开", mContainerWidget);
    mDisconnectButton->setStyleSheet(
        "QPushButton { "
        "background-color: #f44336; "
        "color: white; "
        "border: none; "
        "padding: 4px 12px; "
        "border-radius: 3px; "
        "font-weight: bold; "
        "} "
        "QPushButton:hover { background-color: #da190b; } "
        "QPushButton:pressed { background-color: #c1170a; }"
    );
    
    // 状态标签
    mStatusLabel = new QLabel("未连接", mContainerWidget);
    mStatusLabel->setStyleSheet(
        "QLabel { "
        "border: none; "
        "background: transparent; "
        "color: #666; "
        "font-weight: bold; "
        "padding: 4px; "
        "}"
    );
    
    // 添加到布局
    mainLayout->addWidget(mIpLabel);
    mainLayout->addWidget(mIpLineEdit);
    mainLayout->addWidget(mPortLabel);
    mainLayout->addWidget(mPortSpinBox);
    mainLayout->addWidget(mConnectButton);
    mainLayout->addWidget(mDisconnectButton);
    mainLayout->addWidget(mStatusLabel);
    mainLayout->addStretch(); // 添加弹性空间
    
    // 将容器添加到工具栏
    addWidget(mContainerWidget);
    
    // 连接信号
    connect(mConnectButton, SIGNAL(clicked()), this, SLOT(OnConnectClicked()));
    connect(mDisconnectButton, SIGNAL(clicked()), this, SLOT(OnDisconnectClicked()));
    connect(mIpLineEdit, SIGNAL(textChanged(const QString&)), this, SLOT(OnIpChanged()));
    connect(mPortSpinBox, SIGNAL(valueChanged(int)), this, SLOT(OnPortChanged()));
}

QString wkf::NetworkToolbar::GetIpAddress() const
{
    return mIpLineEdit ? mIpLineEdit->text() : QString();
}

int wkf::NetworkToolbar::GetPort() const
{
    return mPortSpinBox ? mPortSpinBox->value() : 0;
}

void wkf::NetworkToolbar::SetIpAddress(const QString& ip)
{
    if (mIpLineEdit)
    {
        mIpLineEdit->setText(ip);
    }
}

void wkf::NetworkToolbar::SetPort(int port)
{
    if (mPortSpinBox)
    {
        mPortSpinBox->setValue(port);
    }
}

// 暂时注释掉调试编译问题
/*
void wkf::NetworkToolbar::SetConnectionStatus(bool connected)
{
    mIsConnected = connected;
    UpdateConnectionUI(connected);
}
*/

void wkf::NetworkToolbar::UpdateConnectionUI(bool connected)
{
    if (mConnectButton && mDisconnectButton && mStatusLabel)
    {
        mConnectButton->setEnabled(!connected);
        mDisconnectButton->setEnabled(connected);
        
        if (connected)
        {
            mStatusLabel->setText("已连接");
            mStatusLabel->setStyleSheet(
                "QLabel { "
                "border: none; "
                "background: transparent; "
                "color: #4CAF50; "
                "font-weight: bold; "
                "padding: 4px; "
                "}"
            );
        }
        else
        {
            mStatusLabel->setText("未连接");
            mStatusLabel->setStyleSheet(
                "QLabel { "
                "border: none; "
                "background: transparent; "
                "color: #f44336; "
                "font-weight: bold; "
                "padding: 4px; "
                "}"
            );
        }
    }
}

void wkf::NetworkToolbar::OnConnectClicked()
{
    emit ConnectRequested(GetIpAddress(), GetPort());
}

void wkf::NetworkToolbar::OnDisconnectClicked()
{
    emit DisconnectRequested();
}

void wkf::NetworkToolbar::OnIpChanged()
{
    emit NetworkConfigChanged(GetIpAddress(), GetPort());
}

void wkf::NetworkToolbar::OnPortChanged()
{
    emit NetworkConfigChanged(GetIpAddress(), GetPort());
}
