// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SubsystemCalibrationStatusMDT_h
#define Uci__Type__SubsystemCalibrationStatusMDT_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SubsystemID_Type_h)
# include "uci/type/SubsystemID_Type.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__CalibrationID_Type_h)
# include "uci/type/CalibrationID_Type.h"
#endif

#if !defined(Uci__Type__SubsystemCompletedCalibrationType_h)
# include "uci/type/SubsystemCompletedCalibrationType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SubsystemCalibrationStatusMDT sequence accessor class */
      class SubsystemCalibrationStatusMDT : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SubsystemCalibrationStatusMDT()
         { }

         /** Returns this accessor's type constant, i.e. SubsystemCalibrationStatusMDT
           *
           * @return This accessor's type constant, i.e. SubsystemCalibrationStatusMDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::subsystemCalibrationStatusMDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SubsystemCalibrationStatusMDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Indicates a calibration that is currently running or enqueued to run. This element can be used for Calibration
           * initiated via SubsystemStateCommand, SubsystemCalibrationCommand and/or by the subsystem itself. [Minimum
           * occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::CalibrationID_Type, uci::type::accessorType::calibrationID_Type> ActiveCalibrationID;

         /** Indicates results of a previously completed Calibration. This element can be used for results from Calibration
           * initiated via SubsystemStateCommand, SubsystemCalibrationCommand and/or self-initiated by the Subsystem. [Minimum
           * occurrences: 0] [Maximum occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SubsystemCompletedCalibrationType, uci::type::accessorType::subsystemCompletedCalibrationType> CompletedCalibration;

         /** Returns the accessor that provides access to the complex content that is identified by the SubsystemID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SubsystemID.
           */
         virtual const uci::type::SubsystemID_Type& getSubsystemID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SubsystemID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SubsystemID.
           */
         virtual uci::type::SubsystemID_Type& getSubsystemID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SubsystemID to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SubsystemID
           */
         virtual void setSubsystemID(const uci::type::SubsystemID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ActiveCalibrationID.
           *
           * @return The bounded list identified by ActiveCalibrationID.
           */
         virtual const uci::type::SubsystemCalibrationStatusMDT::ActiveCalibrationID& getActiveCalibrationID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the ActiveCalibrationID.
           *
           * @return The bounded list identified by ActiveCalibrationID.
           */
         virtual uci::type::SubsystemCalibrationStatusMDT::ActiveCalibrationID& getActiveCalibrationID()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the ActiveCalibrationID.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setActiveCalibrationID(const uci::type::SubsystemCalibrationStatusMDT::ActiveCalibrationID& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CompletedCalibration.
           *
           * @return The bounded list identified by CompletedCalibration.
           */
         virtual const uci::type::SubsystemCalibrationStatusMDT::CompletedCalibration& getCompletedCalibration() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the CompletedCalibration.
           *
           * @return The bounded list identified by CompletedCalibration.
           */
         virtual uci::type::SubsystemCalibrationStatusMDT::CompletedCalibration& getCompletedCalibration()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the CompletedCalibration.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setCompletedCalibration(const uci::type::SubsystemCalibrationStatusMDT::CompletedCalibration& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SubsystemCalibrationStatusMDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SubsystemCalibrationStatusMDT to copy from
           */
         SubsystemCalibrationStatusMDT(const SubsystemCalibrationStatusMDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SubsystemCalibrationStatusMDT to the contents of the
           * SubsystemCalibrationStatusMDT on the right hand side (rhs) of the assignment operator.SubsystemCalibrationStatusMDT
           * [only available to derived classes]
           *
           * @param rhs The SubsystemCalibrationStatusMDT on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::SubsystemCalibrationStatusMDT
           * @return A constant reference to this SubsystemCalibrationStatusMDT.
           */
         const SubsystemCalibrationStatusMDT& operator=(const SubsystemCalibrationStatusMDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SubsystemCalibrationStatusMDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SubsystemCalibrationStatusMDT_h

