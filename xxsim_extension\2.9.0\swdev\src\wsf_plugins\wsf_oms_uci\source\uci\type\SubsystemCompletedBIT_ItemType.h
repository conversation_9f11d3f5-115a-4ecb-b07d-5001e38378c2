// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SubsystemCompletedBIT_ItemType_h
#define Uci__Type__SubsystemCompletedBIT_ItemType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__VisibleString256Type_h)
# include "uci/type/VisibleString256Type.h"
#endif

#if !defined(Uci__Type__SubsystemBIT_ResultEnum_h)
# include "uci/type/SubsystemBIT_ResultEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SubsystemCompletedBIT_ItemType sequence accessor class */
      class SubsystemCompletedBIT_ItemType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SubsystemCompletedBIT_ItemType()
         { }

         /** Returns this accessor's type constant, i.e. SubsystemCompletedBIT_ItemType
           *
           * @return This accessor's type constant, i.e. SubsystemCompletedBIT_ItemType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::subsystemCompletedBIT_ItemType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SubsystemCompletedBIT_ItemType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the BIT_ItemName.
           *
           * @return The value of the string data type identified by BIT_ItemName.
           */
         virtual const uci::type::VisibleString256Type& getBIT_ItemName() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the BIT_ItemName.
           *
           * @return The value of the string data type identified by BIT_ItemName.
           */
         virtual uci::type::VisibleString256Type& getBIT_ItemName()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the BIT_ItemName to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setBIT_ItemName(const uci::type::VisibleString256Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the BIT_ItemName to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setBIT_ItemName(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the BIT_ItemName to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setBIT_ItemName(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Result.
           *
           * @return The value of the enumeration identified by Result.
           */
         virtual const uci::type::SubsystemBIT_ResultEnum& getResult() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the Result.
           *
           * @return The value of the enumeration identified by Result.
           */
         virtual uci::type::SubsystemBIT_ResultEnum& getResult()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Result.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setResult(const uci::type::SubsystemBIT_ResultEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the Result.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setResult(uci::type::SubsystemBIT_ResultEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the FailReason.
           *
           * @return The value of the string data type identified by FailReason.
           */
         virtual const uci::type::VisibleString256Type& getFailReason() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the FailReason.
           *
           * @return The value of the string data type identified by FailReason.
           */
         virtual uci::type::VisibleString256Type& getFailReason()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setFailReason(const uci::type::VisibleString256Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setFailReason(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the FailReason to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setFailReason(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by FailReason exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by FailReason is emabled or not
           */
         virtual bool hasFailReason() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by FailReason
           *
           * @param type = uci::type::accessorType::visibleString256Type This Accessor's accessor type
           */
         virtual void enableFailReason(uci::base::accessorType::AccessorType type = uci::type::accessorType::visibleString256Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by FailReason */
         virtual void clearFailReason()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SubsystemCompletedBIT_ItemType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SubsystemCompletedBIT_ItemType to copy from
           */
         SubsystemCompletedBIT_ItemType(const SubsystemCompletedBIT_ItemType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SubsystemCompletedBIT_ItemType to the contents of the
           * SubsystemCompletedBIT_ItemType on the right hand side (rhs) of the assignment operator.SubsystemCompletedBIT_ItemType
           * [only available to derived classes]
           *
           * @param rhs The SubsystemCompletedBIT_ItemType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::SubsystemCompletedBIT_ItemType
           * @return A constant reference to this SubsystemCompletedBIT_ItemType.
           */
         const SubsystemCompletedBIT_ItemType& operator=(const SubsystemCompletedBIT_ItemType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SubsystemCompletedBIT_ItemType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SubsystemCompletedBIT_ItemType_h

