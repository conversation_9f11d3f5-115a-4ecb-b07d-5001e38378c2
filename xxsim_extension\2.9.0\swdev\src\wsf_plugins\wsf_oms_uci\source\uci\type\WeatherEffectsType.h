// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__WeatherEffectsType_h
#define Uci__Type__WeatherEffectsType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__WeatherRoadStateEnum_h)
# include "uci/type/WeatherRoadStateEnum.h"
#endif

#if !defined(Uci__Type__WeatherTerrainStateEnum_h)
# include "uci/type/WeatherTerrainStateEnum.h"
#endif

#if !defined(Uci__Type__WeatherSeaStateEnum_h)
# include "uci/type/WeatherSeaStateEnum.h"
#endif

#if !defined(Uci__Type__WeatherSeaStateAmplificationEnum_h)
# include "uci/type/WeatherSeaStateAmplificationEnum.h"
#endif

#if !defined(Uci__Base__BooleanAccessor_h)
# include "uci/base/BooleanAccessor.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the WeatherEffectsType sequence accessor class */
      class WeatherEffectsType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~WeatherEffectsType()
         { }

         /** Returns this accessor's type constant, i.e. WeatherEffectsType
           *
           * @return This accessor's type constant, i.e. WeatherEffectsType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::weatherEffectsType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const WeatherEffectsType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the RoadState.
           *
           * @return The value of the enumeration identified by RoadState.
           */
         virtual const uci::type::WeatherRoadStateEnum& getRoadState() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the RoadState.
           *
           * @return The value of the enumeration identified by RoadState.
           */
         virtual uci::type::WeatherRoadStateEnum& getRoadState()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the RoadState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setRoadState(const uci::type::WeatherRoadStateEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the RoadState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setRoadState(uci::type::WeatherRoadStateEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield RoadStateis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasRoadState() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getRoadState will return the optional field and not throw an exception when
           * invoked.
           *
           * @param type = uci::type::accessorType::weatherRoadStateEnum This Accessor's accessor type
           */
         virtual void enableRoadState(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherRoadStateEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearRoadState()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the TerrainState.
           *
           * @return The value of the enumeration identified by TerrainState.
           */
         virtual const uci::type::WeatherTerrainStateEnum& getTerrainState() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the TerrainState.
           *
           * @return The value of the enumeration identified by TerrainState.
           */
         virtual uci::type::WeatherTerrainStateEnum& getTerrainState()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the TerrainState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setTerrainState(const uci::type::WeatherTerrainStateEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the TerrainState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setTerrainState(uci::type::WeatherTerrainStateEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield TerrainStateis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasTerrainState() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getTerrainState will return the optional field and not throw an exception when
           * invoked.
           *
           * @param type = uci::type::accessorType::weatherTerrainStateEnum This Accessor's accessor type
           */
         virtual void enableTerrainState(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherTerrainStateEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearTerrainState()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SeaState.
           *
           * @return The value of the enumeration identified by SeaState.
           */
         virtual const uci::type::WeatherSeaStateEnum& getSeaState() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SeaState.
           *
           * @return The value of the enumeration identified by SeaState.
           */
         virtual uci::type::WeatherSeaStateEnum& getSeaState()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SeaState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSeaState(const uci::type::WeatherSeaStateEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SeaState.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSeaState(uci::type::WeatherSeaStateEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield SeaStateis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasSeaState() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getSeaState will return the optional field and not throw an exception when
           * invoked.
           *
           * @param type = uci::type::accessorType::weatherSeaStateEnum This Accessor's accessor type
           */
         virtual void enableSeaState(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherSeaStateEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearSeaState()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SeaStateAmplification.
           *
           * @return The value of the enumeration identified by SeaStateAmplification.
           */
         virtual const uci::type::WeatherSeaStateAmplificationEnum& getSeaStateAmplification() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SeaStateAmplification.
           *
           * @return The value of the enumeration identified by SeaStateAmplification.
           */
         virtual uci::type::WeatherSeaStateAmplificationEnum& getSeaStateAmplification()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SeaStateAmplification.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSeaStateAmplification(const uci::type::WeatherSeaStateAmplificationEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SeaStateAmplification.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSeaStateAmplification(uci::type::WeatherSeaStateAmplificationEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether this optionalfield SeaStateAmplificationis available
           *
           * @return Whether the optional field is available (true) or not (false).
           */
         virtual bool hasSeaStateAmplification() const
            throw(uci::base::UCIException) = 0;


         /** Enables the optional field such that getSeaStateAmplification will return the optional field and not throw an
           * exception when invoked.
           *
           * @param type = uci::type::accessorType::weatherSeaStateAmplificationEnum This Accessor's accessor type
           */
         virtual void enableSeaStateAmplification(uci::base::accessorType::AccessorType type = uci::type::accessorType::weatherSeaStateAmplificationEnum)
            throw(uci::base::UCIException) = 0;


         /** Clears the optional item marking it as not being set. */
         virtual void clearSeaStateAmplification()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the SystemIcingState.
           *
           * @return The value of the simple primitive data type identified by SystemIcingState.
           */
         virtual xs::Boolean getSystemIcingState() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the SystemIcingState.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setSystemIcingState(xs::Boolean value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by SystemIcingState exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by SystemIcingState is emabled or not
           */
         virtual bool hasSystemIcingState() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by SystemIcingState
           *
           * @param type = uci::base::accessorType::booleanAccessor This Accessor's accessor type
           */
         virtual void enableSystemIcingState(uci::base::accessorType::AccessorType type = uci::base::accessorType::booleanAccessor)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by SystemIcingState */
         virtual void clearSystemIcingState()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         WeatherEffectsType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The WeatherEffectsType to copy from
           */
         WeatherEffectsType(const WeatherEffectsType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this WeatherEffectsType to the contents of the WeatherEffectsType on
           * the right hand side (rhs) of the assignment operator.WeatherEffectsType [only available to derived classes]
           *
           * @param rhs The WeatherEffectsType on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::WeatherEffectsType
           * @return A constant reference to this WeatherEffectsType.
           */
         const WeatherEffectsType& operator=(const WeatherEffectsType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // WeatherEffectsType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__WeatherEffectsType_h

