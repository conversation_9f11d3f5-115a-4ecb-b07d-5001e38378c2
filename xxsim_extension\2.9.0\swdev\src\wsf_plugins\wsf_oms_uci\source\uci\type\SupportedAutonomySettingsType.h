// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SupportedAutonomySettingsType_h
#define Uci__Type__SupportedAutonomySettingsType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__SupportedAutonomySettingsMissionPlanningType_h)
# include "uci/type/SupportedAutonomySettingsMissionPlanningType.h"
#endif

#if !defined(Uci__Type__MissionPlanActivationAutonomySettingType_h)
# include "uci/type/MissionPlanActivationAutonomySettingType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SupportedAutonomySettingsType sequence accessor class */
      class SupportedAutonomySettingsType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SupportedAutonomySettingsType()
         { }

         /** Returns this accessor's type constant, i.e. SupportedAutonomySettingsType
           *
           * @return This accessor's type constant, i.e. SupportedAutonomySettingsType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::supportedAutonomySettingsType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SupportedAutonomySettingsType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the MissionPlanning.
           *
           * @return The acecssor that provides access to the complex content that is identified by MissionPlanning.
           */
         virtual const uci::type::SupportedAutonomySettingsMissionPlanningType& getMissionPlanning() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the MissionPlanning.
           *
           * @return The acecssor that provides access to the complex content that is identified by MissionPlanning.
           */
         virtual uci::type::SupportedAutonomySettingsMissionPlanningType& getMissionPlanning()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the MissionPlanning to the contents of the complex content that is
           * accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by MissionPlanning
           */
         virtual void setMissionPlanning(const uci::type::SupportedAutonomySettingsMissionPlanningType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by MissionPlanning exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by MissionPlanning is emabled or not
           */
         virtual bool hasMissionPlanning() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by MissionPlanning
           *
           * @param type = uci::type::accessorType::supportedAutonomySettingsMissionPlanningType This Accessor's accessor type
           */
         virtual void enableMissionPlanning(uci::base::accessorType::AccessorType type = uci::type::accessorType::supportedAutonomySettingsMissionPlanningType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by MissionPlanning */
         virtual void clearMissionPlanning()
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the MissionPlanActivation.
           *
           * @return The acecssor that provides access to the complex content that is identified by MissionPlanActivation.
           */
         virtual const uci::type::MissionPlanActivationAutonomySettingType& getMissionPlanActivation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the MissionPlanActivation.
           *
           * @return The acecssor that provides access to the complex content that is identified by MissionPlanActivation.
           */
         virtual uci::type::MissionPlanActivationAutonomySettingType& getMissionPlanActivation()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the MissionPlanActivation to the contents of the complex content that
           * is accessed by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by MissionPlanActivation
           */
         virtual void setMissionPlanActivation(const uci::type::MissionPlanActivationAutonomySettingType& value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by MissionPlanActivation exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by MissionPlanActivation is emabled or not
           */
         virtual bool hasMissionPlanActivation() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by MissionPlanActivation
           *
           * @param type = uci::type::accessorType::missionPlanActivationAutonomySettingType This Accessor's accessor type
           */
         virtual void enableMissionPlanActivation(uci::base::accessorType::AccessorType type = uci::type::accessorType::missionPlanActivationAutonomySettingType)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by MissionPlanActivation */
         virtual void clearMissionPlanActivation()
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SupportedAutonomySettingsType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SupportedAutonomySettingsType to copy from
           */
         SupportedAutonomySettingsType(const SupportedAutonomySettingsType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SupportedAutonomySettingsType to the contents of the
           * SupportedAutonomySettingsType on the right hand side (rhs) of the assignment operator.SupportedAutonomySettingsType
           * [only available to derived classes]
           *
           * @param rhs The SupportedAutonomySettingsType on the right hand side (rhs) of the assignment operator whose contents
           *      are used to set the contents of this uci::type::SupportedAutonomySettingsType
           * @return A constant reference to this SupportedAutonomySettingsType.
           */
         const SupportedAutonomySettingsType& operator=(const SupportedAutonomySettingsType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SupportedAutonomySettingsType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SupportedAutonomySettingsType_h

