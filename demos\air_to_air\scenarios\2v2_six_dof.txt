# ****************************************************************************
# CUI//REL TO USA ONLY
#
# The Advanced Framework for Simulation, Integration, and Modeling (AFSIM)
#
# The use, dissemination or disclosure of data in this file is subject to
# limitation or restriction. See accompanying README and LICENSE for details.
# ****************************************************************************

include_once platforms/lte_fighter.txt

route blue_route
   position 00:00:36.035n 01:47:21.156e altitude 30000 ft msl speed 900 ft/s
   position 00:00:36.035n 01:57:21.156e altitude 30000 ft msl speed 900 ft/s
end_route

route red_route
   position 00:00:36.035n 01:47:21.156w altitude 30000 ft msl speed 900 ft/s
   position 00:00:36.035n 01:57:21.156w altitude 30000 ft msl speed 900 ft/s
end_route

platform blue_1 BLUE_FIGHTER_PM6 
   indestructible 
   side blue 
   commander SELF
   command_chain IFLITE  SELF
   command_chain ELEMENT SELF

   six_dof_set_velocity_ned_fps 0 900 0
   six_dof_position 0.0833 -1.167
   six_dof_alt 30000 ft
   six_dof_ned_heading 90 deg
   
   script_variables
      START_TYPE = "route";
      RISK_WPN = 0.0;
      MISSION_TYPE = "SWEEP";
      ENG_TGT = "";
      RPEAK_LOC ={1.0,0.8,0.95,1.0,1.0};
      ROUTE.Append("blue_route");
      WINCHESTER = {-1,-1,-1,-1,-1};
   end_script_variables
   
   edit processor assessment
      enemy_side    red
      enemy_type    RED_FIGHTER
      friendly_type BLUE_FIGHTER
      flight_id     1
      id_flag       1
      mission_task  SWEEP 
   end_processor   
 
end_platform

platform blue_2 BLUE_FIGHTER_PM6
   indestructible 
   side blue
   commander blue_1
   command_chain IFLITE  blue_1
   command_chain ELEMENT blue_1
   
   six_dof_set_velocity_ned_fps 0 900 0
   six_dof_position -0.0833 -1.167
   six_dof_alt 30000 ft
   six_dof_ned_heading 90 deg
   
   script_variables
      START_TYPE = "route";
      RISK_WPN = 0.0;
      MISSION_TYPE = "SWEEP";
      ENG_TGT = "";
      RPEAK_LOC ={1.0,0.8,0.95,1.0,1.0};
      ROUTE.Append("blue_route");
      WINCHESTER = {-1,-1,-1,-1,-1};
   end_script_variables

   edit processor assessment
      enemy_side    red
      enemy_type    RED_FIGHTER
      friendly_type BLUE_FIGHTER
      flight_id     1
      id_flag       2
      mission_task  SWEEP 
   end_processor   
 
end_platform

platform red_1 RED_FIGHTER_PM6
   side red
   commander SELF
   command_chain IFLITE  SELF
   command_chain ELEMENT SELF
   
   six_dof_set_velocity_ned_fps 0 -900 0
   six_dof_position 0.0 1.167
   six_dof_alt 30000 ft
   six_dof_ned_heading -90 deg
   
   script_variables
      START_TYPE = "route";
      RISK_AVRS = 1.0;
      RISK_WPN = 1.0;
      DOR = 40*MATH.M_PER_NM();
      MISSION_TYPE = "SWEEP";
      ENG_TGT = "";
      RPEAK_LOC ={1.0,0.8,0.95,1.0,1.0};
      ROUTE.Append("red_route");
      WINCHESTER = {-1,-1,-1,-1,-1};
   end_script_variables
  
   edit processor assessment
      enemy_side    blue
      enemy_type    BLUE_FIGHTER
      friendly_type RED_FIGHTER
      flight_id     202
      id_flag       1
      mission_task  SWEEP 
   end_processor   
end_platform

platform red_2 RED_FIGHTER_PM6
   side red
   commander red_1
   command_chain IFLITE  red_1
   command_chain ELEMENT red_1
   
   six_dof_set_velocity_ned_fps 0 -900 0
   six_dof_position 0.167 1.167
   six_dof_alt 30000 ft
   six_dof_ned_heading -90 deg
   
   script_variables
      START_TYPE = "route";
      RISK_AVRS = 1.0;
      RISK_WPN = 1.0;
      DOR = 40*MATH.M_PER_NM();
      MISSION_TYPE = "SWEEP";
      ENG_TGT = "";
      RPEAK_LOC ={1.0,0.8,0.95,1.0,1.0};
      ROUTE.Append("red_route");
      WINCHESTER = {-1,-1,-1,-1,-1};
   end_script_variables
     
   edit processor assessment
      enemy_side    blue
      enemy_type    BLUE_FIGHTER
      friendly_type RED_FIGHTER
      flight_id     202
      id_flag       2
      mission_task  SWEEP 
   end_processor   
end_platform