// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SystemMetadataMDT_h
#define Uci__Type__SystemMetadataMDT_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__MetadataID_Type_h)
# include "uci/type/MetadataID_Type.h"
#endif

#if !defined(Uci__Type__SystemID_Type_h)
# include "uci/type/SystemID_Type.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SystemMetadataPET_h)
# include "uci/type/SystemMetadataPET.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SystemMetadataMDT sequence accessor class */
      class SystemMetadataMDT : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SystemMetadataMDT()
         { }

         /** Returns this accessor's type constant, i.e. SystemMetadataMDT
           *
           * @return This accessor's type constant, i.e. SystemMetadataMDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::systemMetadataMDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SystemMetadataMDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Metadata content. There can be separate metadata for each contributing source. [Maximum occurrences:
           * 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SystemMetadataPET, uci::type::accessorType::systemMetadataPET> Metadata;

         /** Returns the accessor that provides access to the complex content that is identified by the MetadataID.
           *
           * @return The acecssor that provides access to the complex content that is identified by MetadataID.
           */
         virtual const uci::type::MetadataID_Type& getMetadataID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the MetadataID.
           *
           * @return The acecssor that provides access to the complex content that is identified by MetadataID.
           */
         virtual uci::type::MetadataID_Type& getMetadataID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the MetadataID to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by MetadataID
           */
         virtual void setMetadataID(const uci::type::MetadataID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SystemID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SystemID.
           */
         virtual const uci::type::SystemID_Type& getSystemID() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the SystemID.
           *
           * @return The acecssor that provides access to the complex content that is identified by SystemID.
           */
         virtual uci::type::SystemID_Type& getSystemID()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the SystemID to the contents of the complex content that is accessed
           * by the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by SystemID
           */
         virtual void setSystemID(const uci::type::SystemID_Type& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Metadata.
           *
           * @return The bounded list identified by Metadata.
           */
         virtual const uci::type::SystemMetadataMDT::Metadata& getMetadata() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Metadata.
           *
           * @return The bounded list identified by Metadata.
           */
         virtual uci::type::SystemMetadataMDT::Metadata& getMetadata()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Metadata.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setMetadata(const uci::type::SystemMetadataMDT::Metadata& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SystemMetadataMDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SystemMetadataMDT to copy from
           */
         SystemMetadataMDT(const SystemMetadataMDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SystemMetadataMDT to the contents of the SystemMetadataMDT on the
           * right hand side (rhs) of the assignment operator.SystemMetadataMDT [only available to derived classes]
           *
           * @param rhs The SystemMetadataMDT on the right hand side (rhs) of the assignment operator whose contents are used to
           *      set the contents of this uci::type::SystemMetadataMDT
           * @return A constant reference to this SystemMetadataMDT.
           */
         const SystemMetadataMDT& operator=(const SystemMetadataMDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SystemMetadataMDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SystemMetadataMDT_h

