// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StoreManagementCommandMDT_h
#define Uci__Type__StoreManagementCommandMDT_h 1

#if !defined(Uci__Type__SupportCapabilityCommandBaseType_h)
# include "uci/type/SupportCapabilityCommandBaseType.h"
#endif

#if !defined(Uci__Type__StoreManagementCommandType_h)
# include "uci/type/StoreManagementCommandType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the StoreManagementCommandMDT sequence accessor class */
      class StoreManagementCommandMDT : public virtual uci::type::SupportCapabilityCommandBaseType {
      public:

         /** The destructor */
         virtual ~StoreManagementCommandMDT()
         { }

         /** Returns this accessor's type constant, i.e. StoreManagementCommandMDT
           *
           * @return This accessor's type constant, i.e. StoreManagementCommandMDT
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::storeManagementCommandMDT;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StoreManagementCommandMDT& accessor)
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Command.
           *
           * @return The acecssor that provides access to the complex content that is identified by Command.
           */
         virtual const uci::type::StoreManagementCommandType& getCommand() const
            throw(uci::base::UCIException) = 0;


         /** Returns the accessor that provides access to the complex content that is identified by the Command.
           *
           * @return The acecssor that provides access to the complex content that is identified by Command.
           */
         virtual uci::type::StoreManagementCommandType& getCommand()
            throw(uci::base::UCIException) = 0;


         /** Sets the complex content that is identified by the Command to the contents of the complex content that is accessed by
           * the specified accessor.
           *
           * @param value The accessor that provides access to the sequence whose contents are to be used to set the contents of
           *      the sequence identified by Command
           */
         virtual void setCommand(const uci::type::StoreManagementCommandType& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StoreManagementCommandMDT()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StoreManagementCommandMDT to copy from
           */
         StoreManagementCommandMDT(const StoreManagementCommandMDT& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StoreManagementCommandMDT to the contents of the
           * StoreManagementCommandMDT on the right hand side (rhs) of the assignment operator.StoreManagementCommandMDT [only
           * available to derived classes]
           *
           * @param rhs The StoreManagementCommandMDT on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::StoreManagementCommandMDT
           * @return A constant reference to this StoreManagementCommandMDT.
           */
         const StoreManagementCommandMDT& operator=(const StoreManagementCommandMDT& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StoreManagementCommandMDT


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StoreManagementCommandMDT_h

