// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType_h
#define Uci__Type__SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__RoutePlanningTypeEnum_h)
# include "uci/type/RoutePlanningTypeEnum.h"
#endif

#if !defined(Uci__Type__MissionPlanCommandSystemsEnum_h)
# include "uci/type/MissionPlanCommandSystemsEnum.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Type__SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessFunctionType_h)
# include "uci/type/SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessFunctionType.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** This is the SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType sequence accessor class */
      class SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType()
         { }

         /** Returns this accessor's type constant, i.e. SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType
           *
           * @return This accessor's type constant, i.e. SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::supportedMissionPlanCommandsRoutePlanningMissionPlanProcessType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType& accessor)
            throw(uci::base::UCIException) = 0;


         /** If multiple instances are given, each should be of a different combination of child type elements. [Maximum
           * occurrences: 9223372036854775807]
           */
         typedef uci::base::BoundedList<uci::type::SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessFunctionType, uci::type::accessorType::supportedMissionPlanCommandsRoutePlanningMissionPlanProcessFunctionType> Function;

         /** Returns the value of the enumeration that is identified by the ProcessType.
           *
           * @return The value of the enumeration identified by ProcessType.
           */
         virtual const uci::type::RoutePlanningTypeEnum& getProcessType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the ProcessType.
           *
           * @return The value of the enumeration identified by ProcessType.
           */
         virtual uci::type::RoutePlanningTypeEnum& getProcessType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the ProcessType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setProcessType(const uci::type::RoutePlanningTypeEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the ProcessType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setProcessType(uci::type::RoutePlanningTypeEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SystemType.
           *
           * @return The value of the enumeration identified by SystemType.
           */
         virtual const uci::type::MissionPlanCommandSystemsEnum& getSystemType() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the enumeration that is identified by the SystemType.
           *
           * @return The value of the enumeration identified by SystemType.
           */
         virtual uci::type::MissionPlanCommandSystemsEnum& getSystemType()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SystemType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSystemType(const uci::type::MissionPlanCommandSystemsEnum& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the enumeration that is identified by the SystemType.
           *
           * @param value The value to set the enumeration to
           */
         virtual void setSystemType(uci::type::MissionPlanCommandSystemsEnum::EnumerationItem value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Function.
           *
           * @return The bounded list identified by Function.
           */
         virtual const uci::type::SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType::Function& getFunction() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the Function.
           *
           * @return The bounded list identified by Function.
           */
         virtual uci::type::SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType::Function& getFunction()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the Function.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setFunction(const uci::type::SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType::Function& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType to copy from
           */
         SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType(const SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType to
           * the contents of the SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType on the right hand side (rhs) of
           * the assignment operator.SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType [only available to derived
           * classes]
           *
           * @param rhs The SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType on the right hand side (rhs) of the
           *      assignment operator whose contents are used to set the contents of this
           *      uci::type::SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType
           * @return A constant reference to this SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType.
           */
         const SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType& operator=(const SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__SupportedMissionPlanCommandsRoutePlanningMissionPlanProcessType_h

