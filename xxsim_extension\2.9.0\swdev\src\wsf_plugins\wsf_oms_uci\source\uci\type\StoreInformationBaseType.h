// This file was generated  on 12/5/2018 at 1:1:48 PM by the Boeing OMS CAL generation tools
// @warning  This file was automatically generated, edit at your own risk

/**
* Unclassified               U N C L A S S I F I E D               Unclassified
*
* DEVELOPMENT:
*    This document wholly developed with USG funds.
*    For additional information, contact the AFRCO.
*
* ‒  DISTRIBUTION STATEMENT D.  Distribution authorized to the Department
*    of Defense and U.S. DoD contractors only; Critical Technology; 17 Sep 2015.
*    Other requests shall be referred to Air Force Rapid Capabilities Office,
*    Bolling AFB, Washington DC 20032-6400. 
*
* EXPORT CONTROL:
*    WARNING - ITAR CONTROLLED - US ONLY
*    This distribution contains technical data whose export is restricted by
*    the Arms Export Control Act (Title 22, U.S.C., Sec. 2751 et seq. or the
*    Export Administration Act of 1979 as amended Title 50, U.S.C., App.
*    2401-2420 et seq.), as amended. Violation of these export laws are subject
*    to severe criminal penalties.  Disseminate in accordance with provisions of DoD
*    Directive 5230.25.
*/
#ifndef Uci__Type__StoreInformationBaseType_h
#define Uci__Type__StoreInformationBaseType_h 1

#if !defined(Uci__Base__Accessor_h)
# include "uci/base/Accessor.h"
#endif

#if !defined(Uci__Type__VisibleString256Type_h)
# include "uci/type/VisibleString256Type.h"
#endif

#if !defined(Uci__Base__BooleanAccessor_h)
# include "uci/base/BooleanAccessor.h"
#endif

#if !defined(Uci__Base__BoundedList_h)
# include "uci/base/BoundedList.h"
#endif

#if !defined(Uci__Base__IntAccessor_h)
# include "uci/base/IntAccessor.h"
#endif

#if !defined(Uci__Type__StoreJettisonOptionsEnum_h)
# include "uci/type/StoreJettisonOptionsEnum.h"
#endif

//  The namespace in which all UAS C2 Initiative data types are declared
namespace uci {

   //  The namespace in which all generated data types are declared
   namespace type {

      /** Indicates possible store settings that could be used to id the state or extra data related to the attached Mission or
        * Carriage Store.
        */
      class StoreInformationBaseType : public virtual uci::base::Accessor {
      public:

         /** The destructor */
         virtual ~StoreInformationBaseType()
         { }

         /** Returns this accessor's type constant, i.e. StoreInformationBaseType
           *
           * @return This accessor's type constant, i.e. StoreInformationBaseType
           */
         virtual uci::base::accessorType::AccessorType getAccessorType() const
            throw()
         {
            return uci::type::accessorType::storeInformationBaseType;
         }


         /** Initializes the contents of this accessor by copying the contents of the specified accessor
           *
           * @param accessor The accessor whose contents are to be used to initialize the contents of this accessor
           */
         virtual void copy(const StoreInformationBaseType& accessor)
            throw(uci::base::UCIException) = 0;


         /** Store location as defined by system related to int number of Store Point [Maximum occurrences: 9223372036854775807] */
         typedef uci::base::BoundedList<uci::base::IntAccessor, uci::base::accessorType::intAccessor> StoreLocation;

         /** Indicates if and in which situations a store can be jettisoned. [Minimum occurrences: 0] [Maximum occurrences: 4] */
         typedef uci::base::BoundedList<uci::type::StoreJettisonOptionsEnum, uci::type::accessorType::storeJettisonOptionsEnum> JettisonableOption;

         /** Returns the value of the string data type that is identified by the Mnemonic.
           *
           * @return The value of the string data type identified by Mnemonic.
           */
         virtual const uci::type::VisibleString256Type& getMnemonic() const
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the string data type that is identified by the Mnemonic.
           *
           * @return The value of the string data type identified by Mnemonic.
           */
         virtual uci::type::VisibleString256Type& getMnemonic()
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Mnemonic to the string accessed by the specified
           * String.
           *
           * @param value The String whose contents are used to set the string data type.
           */
         virtual void setMnemonic(const uci::type::VisibleString256Type& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Mnemonic to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setMnemonic(const std::string& value)
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the string data type that is identified by the Mnemonic to the specified string.
           *
           * @param value The string used to set the string data type.
           */
         virtual void setMnemonic(const char* value)
            throw(uci::base::UCIException) = 0;


         /** Returns whether the Element that is identified by Mnemonic exists (is enabled) or not.
           *
           * @return A boolean indicating whether the Element identified by Mnemonic is emabled or not
           */
         virtual bool hasMnemonic() const
            throw(uci::base::UCIException) = 0;


         /** Enables the Element that is identified by Mnemonic
           *
           * @param type = uci::type::accessorType::visibleString256Type This Accessor's accessor type
           */
         virtual void enableMnemonic(uci::base::accessorType::AccessorType type = uci::type::accessorType::visibleString256Type)
            throw(uci::base::UCIException) = 0;


         /** Clears (disabled) the Element that is identified by Mnemonic */
         virtual void clearMnemonic()
            throw(uci::base::UCIException) = 0;


         /** Returns the value of the SimplePrimitive data type that is identified by the Simulated.
           *
           * @return The value of the simple primitive data type identified by Simulated.
           */
         virtual xs::Boolean getSimulated() const
            throw(uci::base::UCIException) = 0;


         /** Sets the value of the SimplePrimitive data type that is identified by the Simulated.
           *
           * @param value The value to set the SimplePrimitve data type to
           */
         virtual void setSimulated(xs::Boolean value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the StoreLocation.
           *
           * @return The bounded list identified by StoreLocation.
           */
         virtual const uci::type::StoreInformationBaseType::StoreLocation& getStoreLocation() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the StoreLocation.
           *
           * @return The bounded list identified by StoreLocation.
           */
         virtual uci::type::StoreInformationBaseType::StoreLocation& getStoreLocation()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the StoreLocation.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setStoreLocation(const uci::type::StoreInformationBaseType::StoreLocation& value)
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the JettisonableOption.
           *
           * @return The bounded list identified by JettisonableOption.
           */
         virtual const uci::type::StoreInformationBaseType::JettisonableOption& getJettisonableOption() const
            throw(uci::base::UCIException) = 0;


         /** Returns the bounded list that is identified by the JettisonableOption.
           *
           * @return The bounded list identified by JettisonableOption.
           */
         virtual uci::type::StoreInformationBaseType::JettisonableOption& getJettisonableOption()
            throw(uci::base::UCIException) = 0;


         /** Sets the bounded list that is identified by the JettisonableOption.
           *
           * @param value The bounded list whose contents are to be used to set the value of the bounded list accessed by this
           *      accessor
           */
         virtual void setJettisonableOption(const uci::type::StoreInformationBaseType::JettisonableOption& value)
            throw(uci::base::UCIException) = 0;



      protected:

         /** The constructor [only available to derived classes]. */
         StoreInformationBaseType()
         { }

         /** The copy constructor [only available to derived classes]
           *
           * @param rhs The StoreInformationBaseType to copy from
           */
         StoreInformationBaseType(const StoreInformationBaseType& rhs)
         {
            (void)rhs;
         }

         /** The assignment operator. Sets the contents of this StoreInformationBaseType to the contents of the
           * StoreInformationBaseType on the right hand side (rhs) of the assignment operator.StoreInformationBaseType [only
           * available to derived classes]
           *
           * @param rhs The StoreInformationBaseType on the right hand side (rhs) of the assignment operator whose contents are
           *      used to set the contents of this uci::type::StoreInformationBaseType
           * @return A constant reference to this StoreInformationBaseType.
           */
         const StoreInformationBaseType& operator=(const StoreInformationBaseType& rhs)
         {
            (void)rhs;

            return *this;
         }


      }; // StoreInformationBaseType


   } // Namespace: type
} // Namespace: uci

#endif // Uci__Type__StoreInformationBaseType_h

